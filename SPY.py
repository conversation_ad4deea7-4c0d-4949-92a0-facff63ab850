# -*- coding: utf-8 -*-
import os
import sys
import logging
import warnings
import numpy as np
import pandas as pd
import yfinance as yf
import gymnasium as gym
import joblib
import ta
import quantstats as qs
import random
import time
import signal
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Any, Union, Optional
from decimal import getcontext
from contextlib import contextmanager
import argparse
import json
import torch
import math
import traceback
import concurrent.futures

# Optuna imports
import optuna

# Stable Baselines 3 imports (consolidated)
from stable_baselines3 import PPO
from stable_baselines3.common.vec_env import DummyVecEnv, VecNormalize
from stable_baselines3.common.callbacks import BaseCallback, EvalCallback
from stable_baselines3.common.monitor import Monitor
from stable_baselines3.common.evaluation import evaluate_policy
from gymnasium import spaces

# TensorTrade imports
try:
    from tensortrade.oms.instruments import Instrument, TradingPair, USD
    from tensortrade.oms.orders import Order
    from tensortrade.oms.exchanges import Exchange
    from tensortrade.oms.wallets import Portfolio, Wallet
    from tensortrade.oms.services.execution.simulated import execute_order
    from tensortrade.feed.core import Stream, DataFeed
    from tensortrade.env.generic import ActionScheme, RewardScheme, TradingEnv
    from tensortrade.env.generic import Observer, <PERSON><PERSON>, Ren<PERSON>er, Informer
    TENSORTRADE_AVAILABLE = True
    logging.info("TensorTrade imports successful")
except ImportError as e:
    logging.error(f"TensorTrade not available: {e}")
    logging.error("Please install TensorTrade: pip install tensortrade")
    TENSORTRADE_AVAILABLE = False
    # Create dummy classes to prevent import errors
    class ActionScheme: pass
    class RewardScheme: pass
    class Observer: pass
    class Stopper: pass
    class Renderer: pass
    class Informer: pass
    class TradingEnv: pass
    class Portfolio: pass
    class Wallet: pass
    class Instrument: pass
    class TradingPair: pass
    class Order: pass
    class Exchange: pass
    class Stream: pass
    class DataFeed: pass
    USD = None
    execute_order = None

# Conditional import for aiohttp (used in async operations)
try:
    import aiohttp
    AIOHTTP_AVAILABLE = True
except ImportError:
    AIOHTTP_AVAILABLE = False
    aiohttp = None  # Set to None for consistent handling
    logging.warning("aiohttp not available - async data fetching will be disabled")

# TA library import for technical analysis
try:
    import ta
    from ta.momentum import williams_r
    TA_AVAILABLE = True
    logging.info("TA library imports successful")
except ImportError as e:
    logging.warning(f"TA library not available: {e}")
    logging.warning("Please install ta: pip install ta")
    TA_AVAILABLE = False

# --- Directory Configuration ---
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
SB3_LOG_DIR = os.path.join(SCRIPT_DIR, 'sb3_logs')
MODEL_DIR = os.path.join(SCRIPT_DIR, 'models')

# Create directories if they don't exist
os.makedirs(SB3_LOG_DIR, exist_ok=True)
os.makedirs(MODEL_DIR, exist_ok=True)

# --- Configuration and Trading System ---
class TradingConfig:
    """Configuration container for the trading system"""
    def __init__(self):
        # Constants from original TradingConfig
        self.INITIAL_CASH = 100000.0
        self.MAX_ALLOWED_LOSS = 0.5
        self.COMMISSION_PER_CONTRACT = 1.65
        # ENHANCED: Minimum premium threshold to allow agent learning flexibility
        # Agent should learn to balance commission costs vs premium opportunities
        self.MIN_OPTION_PREMIUM_PER_SHARE = 0.1
        self.SPREAD_PERCENTAGE = 0.01
        self.OPTION_CONTRACT_UNIT = 100
        self.WINDOW_SIZE = 22
        self.REWARD_WINDOW = 22
        self.MIN_PERIODS_INDICATORS = 66
        self.MAX_STEPS_PER_EPISODE = 252
        self.RISK_FREE_RATE_FALLBACK = 0.03
        self.EPSILON = 1e-8
        self.THETA_PENALTY_COEFF = 0.03
        self.VEGA_PENALTY_COEFF = 0.015
        self.PROFIT_REWARD_MULTIPLIER = 4
        self.CONSISTENT_PROFIT_BONUS = 0.8
        self.LOSS_PENALTY_MULTIPLIER = 1
        self.SHARPE_RATIO_WEIGHT = 0.6
        self.DRAWDOWN_PENALTY_COEFF = 0.2
        self.UNDERLYING_TICKER = "SPY"
        self.VIX_TICKER = "^VIX"
        self.VIX3M_TICKER = "^VIX3M"
        self.IRX_TICKER = "^IRX"
        self.TNX_TICKER = "^TNX"
        self.INTERVAL = '1d'


        self.STRICT_MARKET_DATA_VALIDATION = True
        self.STRIKE_CATEGORIES_PCT = [-0.10, -0.09, -0.08, -0.07, -0.06, -0.05, -0.04, -0.03, -0.02, -0.01, 0.00, 0.01, 0.02, 0.03, 0.04, 0.05, 0.06, 0.07, 0.08, 0.09, 0.10]
        self.EXPIRY_CATEGORIES_DTE = [27, 35, 42, 49, 55]
        self.TOTAL_TIMESTEPS = 100000
        self.N_EVAL_EPISODES = 10
        self.EVAL_FREQ_DIVISOR = 10
        self.SCRIPT_VERSION_TAG = "v3.1"
        self.N_STATIC_HELD_OPTION_FEATURES = 20
        self.N_STATIC_PORTFOLIO_FEATURES = 5
        
        # Feature configuration
        self.features_configured = False
        self.n_features = None
        self.n_market_features = None
        self.n_option_features = None
        self.feature_columns = []
        self.base_feature_columns = []
        
        # Portfolio tracking (moved from GlobalState)
        self.net_worth_history: List[float] = []
        self.conceptual_net_worth_history: List[float] = []
        
        # Trading components (moved from GlobalState)
        self.portfolio: Optional[Any] = None
        self.action_scheme: Optional[Any] = None
        self.reward_scheme: Optional[Any] = None
        
    def reset_for_evaluation(self):
        """Reset state for evaluation"""
        self.net_worth_history = []
        self.conceptual_net_worth_history = [self.INITIAL_CASH]
        logging.debug("TradingConfig state reset for evaluation.")
        
    def add_net_worth(self, net_worth: float):
        """Add net worth to history"""
        self.net_worth_history.append(net_worth)
        
    def add_conceptual_net_worth(self, conceptual_net_worth: float):
        """Add conceptual net worth to history"""
        self.conceptual_net_worth_history.append(conceptual_net_worth)
        
    def get_all_tickers(self) -> List[str]:
        """Get list of all tickers used in the system"""
        tickers = [self.UNDERLYING_TICKER, self.VIX_TICKER, self.VIX3M_TICKER, self.IRX_TICKER, self.TNX_TICKER]
        return tickers

    def get_index_like_tickers(self) -> List[str]:
        """Get list of tickers that are indices and may lack volume data"""
        return [self.VIX_TICKER, self.VIX3M_TICKER, self.IRX_TICKER, self.TNX_TICKER]
    
    def get_action_space_size(self) -> int:
        """Calculate total action space size"""
        n_strikes = len(self.STRIKE_CATEGORIES_PCT)
        n_expiries = len(self.EXPIRY_CATEGORIES_DTE)
        n_option_types = 2  # Call, Put
        return 1 + (n_strikes * n_expiries * n_option_types)

    def get_default_held_option_features(self) -> List[float]:
        """Get default values for held option features"""
        return [0.0] * self.N_STATIC_HELD_OPTION_FEATURES

class GlobalState:
    """Global state container for trading system components"""
    def __init__(self):
        # Net worth tracking
        self.net_worth_history: List[float] = []
        self.conceptual_net_worth_history: List[float] = []

        # Trading components
        self.portfolio: Optional[Any] = None
        self.action_scheme: Optional[Any] = None
        self.reward_scheme: Optional[Any] = None

        # CRITICAL FIX: Add evaluation mode flag to prevent history clearing during evaluation
        self.is_evaluation_mode: bool = False

        # CRITICAL FIX: Track last update step to prevent multiple updates per step
        self.last_history_update_step: int = -1

    def add_net_worth(self, net_worth: float):
        """Add net worth to history"""
        self.net_worth_history.append(net_worth)

    def add_conceptual_net_worth(self, conceptual_net_worth: float):
        """Add conceptual net worth to history"""
        self.conceptual_net_worth_history.append(conceptual_net_worth)

    def set_evaluation_mode(self, is_evaluation: bool):
        """Set evaluation mode flag to control reset behavior"""
        self.is_evaluation_mode = is_evaluation
        logging.info(f"GlobalState: Evaluation mode set to {is_evaluation}")

    def reset_for_evaluation(self, initial_cash: float = None):
        """Reset state for evaluation"""
        if initial_cash is None:
            initial_cash = 100000.0  # Default INITIAL_CASH
        self.net_worth_history = []
        self.conceptual_net_worth_history = [initial_cash]
        logging.debug(f"GlobalState reset for evaluation. Initial conceptual_net_worth_history: [{initial_cash}]")

class TradingSystem:
    """Main trading system with proper dependency injection"""
    def __init__(self, config: TradingConfig):
        self.config = config
        self.data_fetcher = None
        self.environment = None

    def initialize(self):
        if not self.config:
            raise ValueError("Configuration must be set before initialization")
        self.data_fetcher = self._setup_data_fetcher()
        self.environment = self._setup_environment()
        self.config.features_configured = True
        logging.info("TradingSystem initialized successfully")

    def _setup_data_fetcher(self):
        # TODO: Implement actual data fetcher setup
        return None

    def _setup_environment(self):
        # TODO: Implement actual environment setup
        return None

# --- Standardized Logging System ---
class StandardizedLogger:
    """Centralized logging system with consistent formatting and performance optimization."""

    def __init__(self, enable_buffering: bool = True, buffer_size: int = 100):
        self.enable_buffering = enable_buffering
        self.buffer_size = buffer_size
        self.log_buffer = []
        self.performance_mode = False

    def set_performance_mode(self, enabled: bool):
        """Enable/disable performance mode (reduces logging verbosity)."""
        self.performance_mode = enabled
        if enabled:
            logging.getLogger().setLevel(logging.WARNING)
        else:
            logging.getLogger().setLevel(logging.INFO)

    def _should_log(self, level: int) -> bool:
        """Determine if message should be logged based on performance mode."""
        if self.performance_mode:
            return level >= logging.WARNING
        return True

    def _log_message(self, level: int, component: str, message: str, **kwargs):
        """Internal method to handle logging with buffering."""
        if not self._should_log(level):
            return

        formatted_msg = ErrorMessageFormatter.format_message(
            severity=logging.getLevelName(level),
            component=component,
            error_code="",
            message=message,
            **kwargs
        )

        if self.enable_buffering and level < logging.ERROR:
            self.log_buffer.append((level, formatted_msg))
            if len(self.log_buffer) >= self.buffer_size:
                self.flush_buffer()
        else:
            logging.log(level, formatted_msg)

    def flush_buffer(self):
        """Flush buffered log messages."""
        for level, message in self.log_buffer:
            logging.log(level, message)
        self.log_buffer.clear()

    def info(self, component: str, message: str, **kwargs):
        """Log info message."""
        self._log_message(logging.INFO, component, message, **kwargs)

    def warning(self, component: str, message: str, **kwargs):
        """Log warning message."""
        self._log_message(logging.WARNING, component, message, **kwargs)

    def error(self, component: str, message: str, **kwargs):
        """Log error message."""
        self._log_message(logging.ERROR, component, message, **kwargs)

    def critical(self, component: str, message: str, **kwargs):
        """Log critical message."""
        self._log_message(logging.CRITICAL, component, message, **kwargs)

    def debug(self, component: str, message: str, **kwargs):
        """Log debug message."""
        self._log_message(logging.DEBUG, component, message, **kwargs)

# --- Performance Optimization ---
class OptimizedTradingSystem:
    def __init__(self):
        self.logger = StandardizedLogger(enable_buffering=True, buffer_size=50)
        self.enable_detailed_logging = False

    def log_buffered(self, message, level=logging.INFO):
        """Buffer logs to reduce I/O - deprecated, use StandardizedLogger instead"""
        self.logger._log_message(level, "OptimizedSystem", message)
        
    async def fetch_data_parallel(self, tickers):
        """Parallel data fetching"""
        import asyncio

        if not AIOHTTP_AVAILABLE:
            logging.warning("aiohttp not available - falling back to sequential fetching")
            return [(None, ticker) for ticker in tickers]

        async def fetch_ticker(session, ticker):
            # Implement async fetch using yfinance API
            try:
                url = f"https://query2.finance.yahoo.com/v8/finance/chart/{ticker}"
                async with session.get(url) as response:
                    if response.status == 200:
                        return await response.json(), ticker
                    else:
                        return None, ticker
            except Exception as e:
                logging.error(f"Error fetching {ticker}: {e}")
                return None, ticker

        async with aiohttp.ClientSession() as session:
            tasks = [fetch_ticker(session, ticker) for ticker in tickers]
            results = await asyncio.gather(*tasks, return_exceptions=True)

        return results
    
    def process_dataframe_efficiently(self, df):
        """Avoid unnecessary copies"""
        # Use inplace operations where possible
        df.fillna(0, inplace=True)
        
        # Use views instead of copies for important columns
        important_columns = ['open', 'high', 'low', 'close', 'volume']
        # Create view for important columns (optimization example)
        _ = df.loc[:, important_columns]  # View, not copy

        # Vectorize operations
        df['new_feature'] = df['close'].rolling(20).mean()
        
        return df

# Create configuration and global state instances
config = TradingConfig()
global_state = GlobalState()

# Create global logger instance
system_logger = StandardizedLogger(enable_buffering=True, buffer_size=100)

# Legacy constants for backward compatibility (will be gradually replaced)
INITIAL_CASH = config.INITIAL_CASH
WINDOW_SIZE = config.WINDOW_SIZE
REWARD_WINDOW = config.REWARD_WINDOW
MAX_STEPS_PER_EPISODE = config.MAX_STEPS_PER_EPISODE
MIN_PERIODS_INDICATORS = config.MIN_PERIODS_INDICATORS
MAX_ALLOWED_LOSS = config.MAX_ALLOWED_LOSS
OPTION_CONTRACT_UNIT = config.OPTION_CONTRACT_UNIT
EPSILON = config.EPSILON
SPREAD_PERCENTAGE = config.SPREAD_PERCENTAGE
RISK_FREE_RATE_FALLBACK = config.RISK_FREE_RATE_FALLBACK
COMMISSION_PER_CONTRACT = config.COMMISSION_PER_CONTRACT
MIN_OPTION_PREMIUM_PER_SHARE = config.MIN_OPTION_PREMIUM_PER_SHARE

# Additional constants that were missing
SCRIPT_VERSION_TAG = config.SCRIPT_VERSION_TAG
N_STATIC_HELD_OPTION_FEATURES = config.N_STATIC_HELD_OPTION_FEATURES
N_STATIC_PORTFOLIO_FEATURES = config.N_STATIC_PORTFOLIO_FEATURES
UNDERLYING_TICKER = config.UNDERLYING_TICKER
VIX_TICKER = config.VIX_TICKER
VIX3M_TICKER = config.VIX3M_TICKER
IRX_TICKER = config.IRX_TICKER
TNX_TICKER = config.TNX_TICKER
INTERVAL = config.INTERVAL
STRIKE_CATEGORIES_PCT = config.STRIKE_CATEGORIES_PCT
EXPIRY_CATEGORIES_DTE = config.EXPIRY_CATEGORIES_DTE
TOTAL_TIMESTEPS = config.TOTAL_TIMESTEPS
N_EVAL_EPISODES = config.N_EVAL_EPISODES
EVAL_FREQ_DIVISOR = config.EVAL_FREQ_DIVISOR
STRICT_MARKET_DATA_VALIDATION = config.STRICT_MARKET_DATA_VALIDATION

# Derived constants
N_STRIKES = len(STRIKE_CATEGORIES_PCT)
N_EXPIRIES = len(EXPIRY_CATEGORIES_DTE)
ACTION_SPACE_SIZE = config.get_action_space_size()

# Default static held option features (15 features)
DEFAULT_STATIC_HELD_OPTION_FEATURES = config.get_default_held_option_features()

# --- Custom Exception Classes ---
class TradingSystemError(Exception):
    """Base exception for all trading system errors."""
    pass

class DataFetchError(TradingSystemError):
    """Raised when data fetching fails."""
    def __init__(self, ticker: str, operation: str, message: str, original_error: Exception = None):
        self.ticker = ticker
        self.operation = operation
        self.original_error = original_error
        super().__init__(f"Data fetch failed for {ticker} during {operation}: {message}")

class DataValidationError(TradingSystemError):
    """Raised when data validation fails."""
    def __init__(self, ticker: str, validation_type: str, message: str):
        self.ticker = ticker
        self.validation_type = validation_type
        super().__init__(f"Data validation failed for {ticker} ({validation_type}): {message}")

class EnvironmentError(TradingSystemError):
    """Raised when trading environment setup or operation fails."""
    def __init__(self, component: str, message: str):
        self.component = component
        super().__init__(f"Environment error in {component}: {message}")

class ModelError(TradingSystemError):
    """Raised when model training or prediction fails."""
    def __init__(self, operation: str, message: str, original_error: Exception = None):
        self.operation = operation
        self.original_error = original_error
        super().__init__(f"Model error during {operation}: {message}")

class TimeoutError(TradingSystemError):
    """Raised when operations exceed timeout limits."""
    def __init__(self, operation: str, timeout_seconds: float, message: str = None):
        self.operation = operation
        self.timeout_seconds = timeout_seconds
        if message:
            super().__init__(f"Operation '{operation}' timed out after {timeout_seconds} seconds: {message}")
        else:
            super().__init__(f"Operation '{operation}' timed out after {timeout_seconds} seconds")

class ConfigurationError(TradingSystemError):
    """Raised when configuration is invalid or missing."""
    def __init__(self, parameter: str, message: str):
        self.parameter = parameter
        super().__init__(f"Configuration error for '{parameter}': {message}")

# --- Standardized Error Handling System ---
class TradingSystemErrorHandler:
    """Centralized error handling system with consistent patterns and recovery strategies."""

    def __init__(self, logger: 'StandardizedLogger'):
        self.logger = logger
        self.error_counts = {}
        self.recovery_strategies = {}

    def register_recovery_strategy(self, error_type: type, strategy_func):
        """Register a recovery strategy for a specific error type."""
        self.recovery_strategies[error_type] = strategy_func

    def handle_error(self, error: Exception, component: str, operation: str,
                    context: dict = None, attempt_recovery: bool = True) -> bool:
        """
        Handle an error with standardized logging and optional recovery.

        Returns:
            bool: True if error was recovered, False otherwise
        """
        error_key = f"{component}:{operation}:{type(error).__name__}"
        self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1

        # Log the error with context
        self.logger.error(
            component,
            f"Error in {operation}: {str(error)}",
            error_type=type(error).__name__,
            error_count=self.error_counts[error_key],
            **(context or {})
        )

        # Attempt recovery if enabled and strategy exists
        if attempt_recovery and type(error) in self.recovery_strategies:
            try:
                recovery_result = self.recovery_strategies[type(error)](error, context)
                if recovery_result:
                    self.logger.info(
                        component,
                        f"Successfully recovered from {type(error).__name__} in {operation}",
                        recovery_strategy=self.recovery_strategies[type(error)].__name__
                    )
                    return True
            except Exception as recovery_error:
                self.logger.error(
                    component,
                    f"Recovery strategy failed: {str(recovery_error)}",
                    original_error=str(error),
                    recovery_error=str(recovery_error)
                )

        return False

    def get_error_summary(self) -> dict:
        """Get summary of all errors encountered."""
        return dict(self.error_counts)

# Create global error handler instance (after class definition)
error_handler = TradingSystemErrorHandler(system_logger)

# --- Error Message Standardization ---
class ErrorMessageFormatter:
    """Standardized error message formatting for consistent logging."""

    # Error severity levels
    CRITICAL = "CRITICAL"
    ERROR = "ERROR"
    WARNING = "WARNING"
    INFO = "INFO"

    # Error codes for better debugging
    ERROR_CODES = {
        'DATA_FETCH_FAILED': 'DF001',
        'DATA_VALIDATION_FAILED': 'DV001',
        'AUTHENTIC_DATA_VALIDATION_FAILED': 'ADV001',
        'ENVIRONMENT_SETUP_FAILED': 'ENV001',
        'MODEL_TRAINING_FAILED': 'MT001',
        'MODEL_PREDICTION_FAILED': 'MP001',
        'TIMEOUT_EXCEEDED': 'TO001',
        'CONFIGURATION_ERROR': 'CF001',
        'CRITICAL_TICKER_MISSING': 'CTM001',
        'SCRIPT_TERMINATION': 'ST001'
    }

    @classmethod
    def format_message(cls, severity: str, component: str, error_code: str, message: str,
                      ticker: str = None, operation: str = None) -> str:
        """
        Format error message with consistent structure.

        Args:
            severity: Error severity level (CRITICAL, ERROR, WARNING, INFO)
            component: Component where error occurred
            error_code: Error code from ERROR_CODES
            message: Detailed error message
            ticker: Optional ticker symbol
            operation: Optional operation name

        Returns:
            Formatted error message string
        """
        code = cls.ERROR_CODES.get(error_code, 'UNK001')

        # Base format: [SEVERITY] Component (CODE): Message
        formatted_msg = f"[{severity}] {component} ({code}): {message}"

        # Add optional context
        if ticker:
            formatted_msg += f" [Ticker: {ticker}]"
        if operation:
            formatted_msg += f" [Operation: {operation}]"

        return formatted_msg

    @classmethod
    def critical(cls, component: str, error_code: str, message: str, **kwargs) -> str:
        """Format critical error message."""
        return cls.format_message(cls.CRITICAL, component, error_code, message, **kwargs)

    @classmethod
    def error(cls, component: str, error_code: str, message: str, **kwargs) -> str:
        """Format error message."""
        return cls.format_message(cls.ERROR, component, error_code, message, **kwargs)

    @classmethod
    def warning(cls, component: str, error_code: str, message: str, **kwargs) -> str:
        """Format warning message."""
        return cls.format_message(cls.WARNING, component, error_code, message, **kwargs)

    @classmethod
    def info(cls, component: str, message: str, **kwargs) -> str:
        """Format info message (no error code needed)."""
        formatted_msg = f"[{cls.INFO}] {component}: {message}"

        # Add optional context
        ticker = kwargs.get('ticker')
        operation = kwargs.get('operation')
        if ticker:
            formatted_msg += f" [Ticker: {ticker}]"
        if operation:
            formatted_msg += f" [Operation: {operation}]"

        return formatted_msg

# --- Resource Management ---
@contextmanager
def managed_yfinance_session():
    """Context manager for yfinance session with proper cleanup."""
    session = None
    try:
        if REQUESTS_AVAILABLE:
            # Create session with rate limiting and caching
            class CachedLimiterSession(CacheMixin, LimiterMixin, RequestsSession):
                pass

            session = CachedLimiterSession(
                limiter=Limiter(RequestRate(2, Duration.SECOND)),  # 2 requests per second
                bucket_class=MemoryQueueBucket,
                backend=SQLiteCache("yfinance_cache")
            )
        else:
            session = None

        yield session
    finally:
        if session:
            try:
                session.close()
            except Exception as e:
                logging.warning(f"Error closing yfinance session: {e}")

@contextmanager
def managed_environment(env):
    """Context manager for trading environment with proper cleanup."""
    try:
        yield env
    finally:
        if env:
            try:
                env.close()
            except Exception as e:
                logging.warning(f"Error closing environment: {e}")

@contextmanager
def managed_model_training(model, env):
    """Context manager for model training with proper resource cleanup."""
    try:
        yield model, env
    finally:
        # Clean up model resources
        if hasattr(model, 'env') and model.env:
            try:
                model.env.close()
            except Exception as e:
                logging.warning(f"Error closing model environment: {e}")

        # Clean up environment
        if env:
            try:
                env.close()
            except Exception as e:
                logging.warning(f"Error closing training environment: {e}")

# --- Performance Monitoring ---
class PerformanceMonitor:
    """Monitor and track performance metrics for data fetching and processing operations."""

    def __init__(self):
        self.metrics = {
            'data_fetch_times': {},
            'data_fetch_success_rates': {},
            'processing_times': {},
            'total_operations': 0,
            'successful_operations': 0,
            'failed_operations': 0
        }
        self.operation_start_times = {}

    def start_operation(self, operation_id: str, operation_type: str, ticker: str = None):
        """Start timing an operation."""
        self.operation_start_times[operation_id] = {
            'start_time': time.time(),
            'operation_type': operation_type,
            'ticker': ticker
        }
        self.metrics['total_operations'] += 1

    def end_operation(self, operation_id: str, success: bool = True, error_message: str = None):
        """End timing an operation and record metrics."""
        if operation_id not in self.operation_start_times:
            logging.warning(f"Operation {operation_id} not found in start times")
            return

        operation_info = self.operation_start_times[operation_id]
        duration = time.time() - operation_info['start_time']
        operation_type = operation_info['operation_type']
        ticker = operation_info.get('ticker', 'unknown')

        # Record timing
        if operation_type not in self.metrics['data_fetch_times']:
            self.metrics['data_fetch_times'][operation_type] = []
        self.metrics['data_fetch_times'][operation_type].append(duration)

        # Record success/failure
        if success:
            self.metrics['successful_operations'] += 1
        else:
            self.metrics['failed_operations'] += 1

        # Record success rate by ticker
        if ticker not in self.metrics['data_fetch_success_rates']:
            self.metrics['data_fetch_success_rates'][ticker] = {'success': 0, 'total': 0}

        self.metrics['data_fetch_success_rates'][ticker]['total'] += 1
        if success:
            self.metrics['data_fetch_success_rates'][ticker]['success'] += 1

        # Clean up
        del self.operation_start_times[operation_id]

        # Log performance info
        status = "SUCCESS" if success else "FAILED"
        log_msg = ErrorMessageFormatter.info(
            "PerformanceMonitor",
            f"Operation {operation_id} {status} in {duration:.2f}s",
            ticker=ticker,
            operation=operation_type
        )
        logging.info(log_msg)

        if not success and error_message:
            error_msg = ErrorMessageFormatter.error(
                "PerformanceMonitor",
                "TIMEOUT_EXCEEDED" if "timeout" in error_message.lower() else "DATA_FETCH_FAILED",
                error_message,
                ticker=ticker,
                operation=operation_type
            )
            logging.error(error_msg)

    def get_summary(self) -> dict:
        """Get performance summary statistics."""
        summary = {
            'total_operations': self.metrics['total_operations'],
            'successful_operations': self.metrics['successful_operations'],
            'failed_operations': self.metrics['failed_operations'],
            'overall_success_rate': 0.0,
            'average_fetch_times': {},
            'ticker_success_rates': {}
        }

        # Calculate overall success rate
        if self.metrics['total_operations'] > 0:
            summary['overall_success_rate'] = (
                self.metrics['successful_operations'] / self.metrics['total_operations']
            )

        # Calculate average fetch times by operation type
        for op_type, times in self.metrics['data_fetch_times'].items():
            if times:
                summary['average_fetch_times'][op_type] = sum(times) / len(times)

        # Calculate success rates by ticker
        for ticker, stats in self.metrics['data_fetch_success_rates'].items():
            if stats['total'] > 0:
                summary['ticker_success_rates'][ticker] = stats['success'] / stats['total']

        return summary

    def log_summary(self):
        """Log performance summary."""
        summary = self.get_summary()

        logging.info(ErrorMessageFormatter.info(
            "PerformanceMonitor",
            f"Performance Summary: {summary['successful_operations']}/{summary['total_operations']} operations successful "
            f"({summary['overall_success_rate']:.1%} success rate)"
        ))

        # Log average fetch times
        for op_type, avg_time in summary['average_fetch_times'].items():
            logging.info(ErrorMessageFormatter.info(
                "PerformanceMonitor",
                f"Average {op_type} time: {avg_time:.2f}s"
            ))

        # Log ticker-specific success rates
        for ticker, success_rate in summary['ticker_success_rates'].items():
            if success_rate < 0.8:  # Log warning for low success rates
                logging.warning(ErrorMessageFormatter.warning(
                    "PerformanceMonitor",
                    "DATA_FETCH_FAILED",
                    f"Low success rate: {success_rate:.1%}",
                    ticker=ticker
                ))

# Create global performance monitor instance
performance_monitor = PerformanceMonitor()

# --- Utility Classes ---
class DataValidationUtils:
    """Utility class for data validation operations."""

    @staticmethod
    def validate_dataframe(df: pd.DataFrame, ticker: str, required_columns: List[str] = None) -> bool:
        """Validate that a DataFrame meets basic requirements."""
        if df is None or df.empty:
            raise DataValidationError(ticker, "empty_dataframe", "DataFrame is None or empty")

        if required_columns:
            missing_cols = [col for col in required_columns if col not in df.columns]
            if missing_cols:
                raise DataValidationError(ticker, "missing_columns", f"Missing columns: {missing_cols}")

        return True

    @staticmethod
    def validate_price_data(df: pd.DataFrame, ticker: str, min_periods: int = None) -> bool:
        """Validate price data quality."""
        min_periods = min_periods or config.MIN_PERIODS_INDICATORS

        if len(df) < min_periods:
            raise DataValidationError(ticker, "insufficient_data",
                                    f"Only {len(df)} periods available, need at least {min_periods}")

        # Check for reasonable price ranges
        if ticker == config.UNDERLYING_TICKER:
            price_cols = [col for col in df.columns if 'close' in col.lower()]
            for col in price_cols:
                if col in df.columns:
                    prices = df[col].dropna()
                    if len(prices) > 0:
                        if prices.min() <= 0 or prices.max() > 10000:  # Reasonable SPY range
                            raise DataValidationError(ticker, "invalid_price_range",
                                                    f"Prices outside reasonable range: {prices.min()}-{prices.max()}")

        return True

    @staticmethod
    def validate_market_data_authenticity(df: pd.DataFrame, ticker: str) -> bool:
        """Validate that market data appears authentic (not synthetic)."""
        if config.STRICT_MARKET_DATA_VALIDATION:
            # Check for patterns that might indicate synthetic data
            close_col = None
            for col in df.columns:
                if 'close' in col.lower():
                    close_col = col
                    break

            if close_col and close_col in df.columns:
                prices = df[close_col].dropna()
                if len(prices) > 10:
                    # Check for unrealistic patterns
                    price_changes = prices.pct_change().dropna()
                    if len(price_changes) > 0:
                        # Flag if all changes are exactly the same (synthetic pattern)
                        if price_changes.nunique() == 1 and price_changes.iloc[0] != 0:
                            raise DataValidationError(ticker, "synthetic_pattern",
                                                    "Data shows synthetic patterns")

        return True

class ConfigurationUtils:
    """Utility class for configuration operations."""

    @staticmethod
    def validate_config() -> bool:
        """Validate that configuration is complete and consistent."""
        try:
            # Check required configuration values
            required_attrs = ['INITIAL_CASH', 'WINDOW_SIZE', 'MAX_STEPS_PER_EPISODE',
                            'UNDERLYING_TICKER', 'VIX_TICKER']

            for attr in required_attrs:
                if not hasattr(config, attr):
                    raise ConfigurationError(attr, f"Missing required configuration: {attr}")

                value = getattr(config, attr)
                if value is None:
                    raise ConfigurationError(attr, f"Configuration value is None: {attr}")

            # Validate numeric ranges
            if config.INITIAL_CASH <= 0:
                raise ConfigurationError('INITIAL_CASH', 'Must be positive')

            if config.WINDOW_SIZE <= 0:
                raise ConfigurationError('WINDOW_SIZE', 'Must be positive')

            if config.MAX_STEPS_PER_EPISODE <= 0:
                raise ConfigurationError('MAX_STEPS_PER_EPISODE', 'Must be positive')

            return True

        except Exception as e:
            if isinstance(e, ConfigurationError):
                raise
            else:
                raise ConfigurationError('validation', f"Configuration validation failed: {str(e)}")

    @staticmethod
    def get_timeout_config(operation_type: str) -> dict:
        """Get timeout configuration for different operation types."""
        timeout_configs = {
            'data_fetch': {'timeout': 60, 'retries': 3, 'delay': 5},
            'model_training': {'timeout': 300, 'retries': 1, 'delay': 0},
            'model_prediction': {'timeout': 10, 'retries': 2, 'delay': 1},
            'data_processing': {'timeout': 120, 'retries': 2, 'delay': 2}
        }

        return timeout_configs.get(operation_type, timeout_configs['data_fetch'])

class DataFetchUtils:
    """Utility class for data fetching operations."""

    @staticmethod
    def get_ticker_classification(ticker: str) -> dict:
        """Classify ticker and return its characteristics."""
        is_vix = ticker == config.VIX_TICKER
        is_vix3m = ticker == config.VIX3M_TICKER
        is_irx = ticker == config.IRX_TICKER
        is_tnx = ticker == config.TNX_TICKER
        is_spy = ticker == config.UNDERLYING_TICKER and config.UNDERLYING_TICKER == "SPY"

        is_vix_term = is_vix3m  # Only VIX3M now
        is_treasury = is_irx or is_tnx
        is_critical_ticker = is_vix or is_irx or is_tnx or is_vix_term
        # Updated: VIX is now considered problematic for timeout purposes due to frequent API issues
        is_problematic = is_vix or is_vix3m or is_treasury

        return {
            'is_vix': is_vix,
            'is_vix3m': is_vix3m,
            'is_irx': is_irx,
            'is_tnx': is_tnx,
            'is_spy': is_spy,
            'is_vix_term': is_vix_term,
            'is_treasury': is_treasury,
            'is_critical': is_critical_ticker,
            'is_problematic': is_problematic
        }

    @staticmethod
    def get_fetch_timeouts(ticker_info: dict, operation_type: str = 'training') -> dict:
        """Get optimized timeouts to prevent excessive waiting and hanging."""
        # ENHANCED: Increased timeouts to prevent timeout issues while maintaining reasonable limits
        if operation_type == 'evaluation':
            if ticker_info['is_vix3m']:
                # VIX3M: increased from 12s to 20s
                return {'timeout': 20, 'max_retries': 2, 'retry_delay': 2}
            elif ticker_info['is_vix']:
                # VIX: increased from 10s to 15s
                return {'timeout': 15, 'max_retries': 2, 'retry_delay': 2}
            elif ticker_info['is_treasury']:
                # Treasury: increased from 8s to 15s
                return {'timeout': 15, 'max_retries': 2, 'retry_delay': 2}
            else:
                # Others: increased from 10s to 15s
                return {'timeout': 15, 'max_retries': 2, 'retry_delay': 2}

        # Standard timeouts for training operations (historical data)
        if ticker_info['is_vix']:
            # VIX: increased from 10s to 20s
            return {'timeout': 20, 'max_retries': 3, 'retry_delay': 3}
        elif ticker_info['is_vix3m']:
            # VIX3M: increased from 12s to 25s
            return {'timeout': 25, 'max_retries': 3, 'retry_delay': 3}
        elif ticker_info['is_treasury']:
            # Treasury: increased from 8s to 20s
            return {'timeout': 20, 'max_retries': 3, 'retry_delay': 3}
        elif ticker_info['is_problematic']:
            # Problematic: increased from 8s to 15s
            return {'timeout': 15, 'max_retries': 2, 'retry_delay': 3}
        else:
            # Standard: increased from 10s to 20s
            return {'timeout': 20, 'max_retries': 3, 'retry_delay': 3}

    @staticmethod
    def apply_fetch_delay(ticker_info: dict):
        """Apply minimal delays to reduce overall data preparation time."""
        # OPTIMIZED: Significantly reduced delays to speed up data preparation
        if ticker_info['is_problematic']:
            time.sleep(0.3)  # Reduced from 1.0s to 0.3s
        elif ticker_info['is_critical']:
            time.sleep(0.2)  # Reduced from 0.5s to 0.2s
        else:
            time.sleep(0.1)  # Reduced from 0.25s to 0.1s

    @staticmethod
    def validate_critical_ticker_data(df: pd.DataFrame, ticker: str, ticker_info: dict) -> bool:
        """Validate that critical ticker data has reasonable values."""
        if not ticker_info['is_critical'] or df.empty:
            return True

        try:
            col_name = f"close_{ticker.replace('^', '').replace('=F', '').upper()}"
            if col_name in df.columns:
                last_value = df[col_name].iloc[-1] if len(df) > 0 else None
                if last_value is not None:
                    logging.info(f"VALIDATION: {ticker} last value: {last_value}")
                    return True
            return False
        except Exception as e:
            logging.error(f"Validation error for {ticker}: {e}")
            return False

class DataFetchStrategies:
    """Collection of data fetching strategies for different scenarios."""

    @staticmethod
    def try_ticker_history(ticker: str, start: str, end: str, interval: str,
                          session, ticker_info: dict, operation_type: str = 'training') -> tuple:
        """Try to fetch data using Ticker.history() method."""
        try:
            yf_ticker = yf.Ticker(ticker, session=session)

            if ticker_info['is_problematic']:
                # Use timeout handling for problematic tickers
                def fetch_history():
                    # Disable repair for ALL tickers to ensure authentic, unmodified data
                    # repair=True can cause data corruption (e.g., Treasury rates multiplied by 100)

                    if interval == '1d':
                        return yf_ticker.history(start=start, end=end, interval=interval,
                                               auto_adjust=True, repair=False)
                    else:
                        df_local = yf_ticker.history(period="max", interval=interval,
                                                   auto_adjust=True, repair=False)
                        if not df_local.empty:
                            return df_local.loc[start:end]
                        return df_local

                timeouts = DataFetchUtils.get_fetch_timeouts(ticker_info, operation_type)
                df, error = SPY.fetch_with_retry(
                    fetch_history,
                    max_retries=timeouts['max_retries'],
                    initial_timeout=timeouts['timeout']
                )
                if error is not None:
                    raise error
            else:
                # Standard approach for non-problematic tickers
                # Disable repair for ALL tickers to ensure authentic, unmodified data
                # repair=True can cause data corruption (e.g., Treasury rates multiplied by 100)

                if interval == '1d':
                    df = yf_ticker.history(start=start, end=end, interval=interval,
                                         auto_adjust=True, repair=False)
                else:
                    df = yf_ticker.history(period="max", interval=interval,
                                         auto_adjust=True, repair=False)
                    if not df.empty:
                        df = df.loc[start:end]

            if not df.empty:
                return df, "Ticker.history"
            else:
                raise DataFetchError(ticker, "ticker_history", "Empty DataFrame returned")

        except Exception as e:
            raise DataFetchError(ticker, "ticker_history", str(e))

    @staticmethod
    def try_yf_download(ticker: str, start: str, end: str, interval: str,
                       session, ticker_info: dict, operation_type: str = 'training') -> tuple:
        """Try to fetch data using yf.download() method."""
        try:
            timeouts = DataFetchUtils.get_fetch_timeouts(ticker_info, operation_type)

            def download_with_timeout():
                # Disable repair for ALL tickers to ensure authentic, unmodified data
                # repair=True can cause data corruption (e.g., Treasury rates multiplied by 100)

                return yf.download(
                    ticker,
                    start=start,
                    end=end,
                    interval=interval,
                    session=session,
                    progress=False,
                    auto_adjust=True,
                    prepost=False,
                    repair=False,  # Disabled for ALL tickers to prevent data corruption
                    threads=False,
                    timeout=timeouts['timeout']
                )

            df, timeout_error = SPY.fetch_with_timeout(download_with_timeout, timeouts['timeout'] + 5)

            if timeout_error is not None:
                raise TimeoutError("yf_download", timeouts['timeout'] + 5, str(timeout_error))

            if not df.empty:
                return df, "yf.download"
            else:
                raise DataFetchError(ticker, "yf_download", "Empty DataFrame returned")

        except Exception as e:
            if isinstance(e, (TimeoutError, DataFetchError)):
                raise
            else:
                raise DataFetchError(ticker, "yf_download", str(e))

    @staticmethod
    def try_extended_retry(ticker: str, start: str, end: str, interval: str,
                          session, ticker_info: dict, operation_type: str = 'training') -> tuple:
        """Try extended retry logic with different parameters."""
        try:
            yf_ticker = yf.Ticker(ticker, session=session)
            timeouts = DataFetchUtils.get_fetch_timeouts(ticker_info, operation_type)

            # Adjust timeout for extended retry
            method_timeout = min(timeouts['timeout'], 15)

            # Disable repair for ALL tickers to ensure authentic, unmodified data
            # repair=True can cause data corruption (e.g., Treasury rates multiplied by 100)

            df, error = SPY.fetch_with_retry(yf_ticker.history, max_retries=timeouts['max_retries'],
                                            initial_timeout=method_timeout, start=start, end=end,
                                            interval=interval, auto_adjust=True, repair=False)
            if error is not None:
                raise error

            if df is not None and not df.empty:
                return df, "extended_retry"
            else:
                raise DataFetchError(ticker, "extended_retry", "Empty DataFrame returned")

        except Exception as e:
            if isinstance(e, DataFetchError):
                raise
            else:
                raise DataFetchError(ticker, "extended_retry", str(e))

    @staticmethod
    def try_shorter_periods(ticker: str, start: str, end: str, interval: str,
                           session, ticker_info: dict) -> tuple:
        """Try fetching with shorter time periods."""
        try:
            yf_ticker = yf.Ticker(ticker, session=session)

            # Try with 1 year back only for speed
            short_start = pd.Timestamp(end) - pd.Timedelta(days=365)
            short_start_str = short_start.strftime('%Y-%m-%d')

            timeout_for_short = 20 if ticker_info['is_treasury'] else 15
            # Disable repair for ALL tickers to ensure authentic, unmodified data
            # repair=True can cause data corruption (e.g., Treasury rates multiplied by 100)

            df, error = SPY.fetch_with_timeout(yf_ticker.history, timeout_for_short,
                                             start=short_start_str, end=end, interval=interval,
                                             auto_adjust=True, repair=False)

            if error is not None:
                raise TimeoutError(ticker, "shorter_periods", timeout_for_short, str(error))

            if df is not None and not df.empty:
                return df, "shorter_period"
            else:
                raise DataFetchError(ticker, "shorter_periods", "Empty DataFrame returned")

        except Exception as e:
            if isinstance(e, (TimeoutError, DataFetchError)):
                raise
            else:
                raise DataFetchError(ticker, "shorter_periods", str(e))

    @staticmethod
    def try_period_based(ticker: str, start: str, end: str, interval: str,
                        session, ticker_info: dict) -> tuple:
        """Try fetching using period-based approach."""
        try:
            yf_ticker = yf.Ticker(ticker, session=session)

            period_func = lambda: yf_ticker.history(period="1y")
            period_df, period_error = SPY.fetch_with_timeout(period_func, 10)

            if period_error is not None:
                raise TimeoutError(ticker, "period_based", 10, str(period_error))

            if period_df is not None and not period_df.empty:
                # Filter to desired date range if possible
                if start and end:
                    try:
                        period_df = period_df.loc[start:end]
                    except Exception:
                        pass  # Use what we have if filtering fails

                return period_df, "period_1y"
            else:
                raise DataFetchError(ticker, "period_based", "Empty DataFrame returned")

        except Exception as e:
            if isinstance(e, (TimeoutError, DataFetchError)):
                raise
            else:
                raise DataFetchError(ticker, "period_based", str(e))



def fetch_historical_data_yf_refactored(ticker: str, start: str, end: str, interval: str = '1d',
                                       session = None, min_periods_override: int = None,
                                       skip_problematic: bool = False, operation_type: str = 'training') -> pd.DataFrame:
    """
    Refactored version of fetch_historical_data_yf using utility classes.

    This is a cleaner, more maintainable version that uses the new utility classes
    for better organization and error handling.

    Args:
        ticker: Ticker symbol to fetch
        start: Start date string
        end: End date string
        interval: Data interval (default '1d')
        session: Optional session for requests
        min_periods_override: Optional minimum periods override
        _skip_problematic: Skip problematic ticker handling
        operation_type: Type of operation ('training' or 'evaluation')

    Returns:
        pd.DataFrame: Processed historical data
    """
    # Start performance monitoring
    operation_id = f"fetch_refactored_{ticker}_{int(time.time())}"
    performance_monitor.start_operation(operation_id, "refactored_fetch", ticker)

    system_logger.info(
        "DataFetch",
        f"Fetching {interval} data from {start} to {end}",
        ticker=ticker,
        operation="refactored_fetch"
    )

    # Get ticker classification and timeouts
    ticker_info = DataFetchUtils.get_ticker_classification(ticker)

    # Check if we should skip problematic tickers
    if skip_problematic and ticker_info.get('is_problematic', False):
        system_logger.info(
            "DataFetch",
            f"Skipping problematic ticker {ticker} due to skip_problematic=True",
            ticker=ticker,
            operation="refactored_fetch"
        )
        performance_monitor.end_operation(operation_id, success=False, error_message="Skipped problematic ticker")
        return pd.DataFrame()

    # Apply appropriate delay before fetching
    DataFetchUtils.apply_fetch_delay(ticker_info)

    # Use shared session if none provided
    active_session = session or (shared_yf_session if 'shared_yf_session' in globals() else None)

    # Define the strategies to try in order
    strategies = [
        DataFetchStrategies.try_ticker_history,
        DataFetchStrategies.try_yf_download,
        DataFetchStrategies.try_extended_retry,
        DataFetchStrategies.try_shorter_periods,
        DataFetchStrategies.try_period_based
    ]

    # Try each strategy in order
    for strategy_func in strategies:
        try:
            # Pass operation_type to strategies that support it
            if strategy_func in [DataFetchStrategies.try_ticker_history,
                               DataFetchStrategies.try_yf_download,
                               DataFetchStrategies.try_extended_retry]:
                df, data_source = strategy_func(ticker, start, end, interval,
                                              active_session, ticker_info, operation_type)
            else:
                df, data_source = strategy_func(ticker, start, end, interval,
                                              active_session, ticker_info)

            if not df.empty:
                system_logger.info(
                    "DataFetch",
                    f"Retrieved data using {data_source}: {len(df)} rows",
                    ticker=ticker,
                    operation=strategy_func.__name__
                )

                # Process the data
                processed_df = process_yf_data(df, ticker, min_periods_override=min_periods_override)

                # Validate critical ticker data
                if ticker_info['is_critical'] and not processed_df.empty:
                    DataFetchUtils.validate_critical_ticker_data(processed_df, ticker, ticker_info)

                performance_monitor.end_operation(operation_id, success=True)
                return processed_df

        except (DataFetchError, TimeoutError, ConfigurationError) as e:
            system_logger.warning(
                "DataFetch",
                f"Strategy {strategy_func.__name__} failed: {str(e)}",
                ticker=ticker,
                operation=strategy_func.__name__
            )
            continue
        except Exception as e:
            system_logger.error(
                "DataFetch",
                f"Unexpected error in {strategy_func.__name__}: {str(e)}",
                ticker=ticker,
                operation=strategy_func.__name__
            )
            continue

    # If all strategies failed
    error_msg = "All fetch strategies failed"
    if ticker_info['is_critical']:
        system_logger.critical(
            "DataFetch",
            f"Unable to fetch {ticker} data. This will affect option pricing accuracy.",
            ticker=ticker,
            operation="refactored_fetch"
        )
    else:
        system_logger.warning(
            "DataFetch",
            f"Unable to fetch {ticker} data using any strategy",
            ticker=ticker,
            operation="refactored_fetch"
        )

    performance_monitor.end_operation(operation_id, success=False, error_message=error_msg)

    # Log performance summary for monitoring improvements
    performance_monitor.log_summary()

    return pd.DataFrame()

class DataFrameOptimizer:
    """Utility class for optimizing DataFrame operations."""

    @staticmethod
    def optimize_dtypes(df: pd.DataFrame) -> pd.DataFrame:
        """Optimize DataFrame dtypes to reduce memory usage."""
        if df.empty:
            return df

        optimized_df = df.copy()

        for col in optimized_df.columns:
            if optimized_df[col].dtype == 'object':
                continue  # Skip object columns

            # Convert float64 to float32 where possible
            if optimized_df[col].dtype == 'float64':
                col_min = optimized_df[col].min()
                col_max = optimized_df[col].max()

                # Check if values fit in float32 range
                if (col_min >= np.finfo(np.float32).min and
                    col_max <= np.finfo(np.float32).max):
                    optimized_df[col] = optimized_df[col].astype(np.float32)

            # Convert int64 to smaller int types where possible
            elif optimized_df[col].dtype == 'int64':
                col_min = optimized_df[col].min()
                col_max = optimized_df[col].max()

                if col_min >= np.iinfo(np.int8).min and col_max <= np.iinfo(np.int8).max:
                    optimized_df[col] = optimized_df[col].astype(np.int8)
                elif col_min >= np.iinfo(np.int16).min and col_max <= np.iinfo(np.int16).max:
                    optimized_df[col] = optimized_df[col].astype(np.int16)
                elif col_min >= np.iinfo(np.int32).min and col_max <= np.iinfo(np.int32).max:
                    optimized_df[col] = optimized_df[col].astype(np.int32)

        return optimized_df

    @staticmethod
    def efficient_merge(dfs: List[pd.DataFrame], on_index: bool = True) -> pd.DataFrame:
        """Efficiently merge multiple DataFrames."""
        if not dfs:
            return pd.DataFrame()

        if len(dfs) == 1:
            return dfs[0]

        # Start with the largest DataFrame as base
        dfs_sorted = sorted(dfs, key=len, reverse=True)
        result = dfs_sorted[0].copy()

        # Merge remaining DataFrames
        for df in dfs_sorted[1:]:
            if df.empty:
                continue

            if on_index:
                result = result.join(df, how='outer', rsuffix='_dup')
                # Remove duplicate columns
                dup_cols = [col for col in result.columns if col.endswith('_dup')]
                result = result.drop(columns=dup_cols)
            else:
                result = pd.merge(result, df, left_index=True, right_index=True, how='outer')

        return result

    @staticmethod
    def vectorized_calculations(df: pd.DataFrame, operations: List[dict]) -> pd.DataFrame:
        """Apply vectorized calculations efficiently."""
        result_df = df.copy()

        for op in operations:
            op_type = op.get('type')
            col_name = op.get('column')
            target_col = op.get('target', col_name + '_calc')

            if col_name not in result_df.columns:
                continue

            try:
                if op_type == 'rolling_mean':
                    window = op.get('window', 20)
                    result_df[target_col] = result_df[col_name].rolling(window=window, min_periods=1).mean()

                elif op_type == 'rolling_std':
                    window = op.get('window', 20)
                    result_df[target_col] = result_df[col_name].rolling(window=window, min_periods=1).std()

                elif op_type == 'pct_change':
                    periods = op.get('periods', 1)
                    result_df[target_col] = result_df[col_name].pct_change(periods=periods)

                elif op_type == 'shift':
                    periods = op.get('periods', 1)
                    result_df[target_col] = result_df[col_name].shift(periods=periods)

                elif op_type == 'diff':
                    periods = op.get('periods', 1)
                    result_df[target_col] = result_df[col_name].diff(periods=periods)

            except Exception as e:
                logging.warning(f"Failed to apply operation {op_type} to {col_name}: {e}")
                continue

        return result_df

    @staticmethod
    def memory_efficient_concat(dfs: List[pd.DataFrame], **kwargs) -> pd.DataFrame:
        """Memory-efficient concatenation of DataFrames."""
        if not dfs:
            return pd.DataFrame()

        # Filter out empty DataFrames
        non_empty_dfs = [df for df in dfs if not df.empty]

        if not non_empty_dfs:
            return pd.DataFrame()

        if len(non_empty_dfs) == 1:
            return non_empty_dfs[0]

        # Use ignore_index=True by default for better performance
        kwargs.setdefault('ignore_index', True)
        kwargs.setdefault('sort', False)  # Avoid sorting for performance

        return pd.concat(non_empty_dfs, **kwargs)

class InputValidator:
    """Utility class for input validation."""

    @staticmethod
    def validate_ticker(ticker: str) -> str:
        """Validate and normalize ticker symbol."""
        if not ticker or not isinstance(ticker, str):
            raise ValueError("Ticker must be a non-empty string")

        ticker = ticker.strip().upper()
        if not ticker:
            raise ValueError("Ticker cannot be empty after stripping whitespace")

        # Basic ticker format validation
        if len(ticker) > 10:
            raise ValueError(f"Ticker '{ticker}' is too long (max 10 characters)")

        return ticker

    @staticmethod
    def validate_date_string(date_str: str, param_name: str = "date") -> str:
        """Validate date string format."""
        if not date_str or not isinstance(date_str, str):
            raise ValueError(f"{param_name} must be a non-empty string")

        try:
            # Try to parse the date to validate format
            pd.to_datetime(date_str)
            return date_str
        except Exception as e:
            raise ValueError(f"Invalid {param_name} format '{date_str}': {e}")

    @staticmethod
    def validate_date_range(start: str, end: str) -> tuple:
        """Validate that start date is before end date."""
        start_validated = InputValidator.validate_date_string(start, "start date")
        end_validated = InputValidator.validate_date_string(end, "end date")

        start_dt = pd.to_datetime(start_validated)
        end_dt = pd.to_datetime(end_validated)

        if start_dt >= end_dt:
            raise ValueError(f"Start date '{start}' must be before end date '{end}'")

        return start_validated, end_validated

    @staticmethod
    def validate_interval(interval: str) -> str:
        """Validate yfinance interval parameter."""
        valid_intervals = ['1m', '2m', '5m', '15m', '30m', '60m', '90m', '1h', '1d', '5d', '1wk', '1mo', '3mo']

        if not interval or not isinstance(interval, str):
            raise ValueError("Interval must be a non-empty string")

        if interval not in valid_intervals:
            raise ValueError(f"Invalid interval '{interval}'. Valid intervals: {valid_intervals}")

        return interval

    @staticmethod
    def validate_numeric_range(value, min_val=None, max_val=None, param_name="value") -> float:
        """Validate numeric value is within specified range."""
        if value is None:
            raise ValueError(f"{param_name} cannot be None")

        try:
            value = float(value)
        except (TypeError, ValueError):
            raise ValueError(f"{param_name} must be numeric, got {type(value)}")

        if min_val is not None and value < min_val:
            raise ValueError(f"{param_name} must be >= {min_val}, got {value}")

        if max_val is not None and value > max_val:
            raise ValueError(f"{param_name} must be <= {max_val}, got {value}")

        return value

    @staticmethod
    def validate_dataframe_not_empty(df: pd.DataFrame, param_name: str = "DataFrame") -> pd.DataFrame:
        """Validate that DataFrame is not None or empty."""
        if df is None:
            raise ValueError(f"{param_name} cannot be None")

        if not isinstance(df, pd.DataFrame):
            raise ValueError(f"{param_name} must be a pandas DataFrame, got {type(df)}")

        if df.empty:
            raise ValueError(f"{param_name} cannot be empty")

        return df

    @staticmethod
    def validate_required_columns(df: pd.DataFrame, required_cols: List[str], param_name: str = "DataFrame") -> pd.DataFrame:
        """Validate that DataFrame contains required columns."""
        InputValidator.validate_dataframe_not_empty(df, param_name)

        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            raise ValueError(f"{param_name} missing required columns: {missing_cols}")

        return df

# --- Parameter Configuration Classes ---
from dataclasses import dataclass
from typing import Optional

@dataclass
class DataFetchParams:
    """Configuration for data fetching operations."""
    ticker: str
    start: str
    end: str
    interval: str = '1d'
    session: Optional[Any] = None
    min_periods_override: Optional[int] = None
    skip_problematic: bool = False
    operation_type: str = 'training'

    def __post_init__(self):
        """Validate parameters after initialization."""
        self.ticker = InputValidator.validate_ticker(self.ticker)
        self.start, self.end = InputValidator.validate_date_range(self.start, self.end)
        self.interval = InputValidator.validate_interval(self.interval)

@dataclass
class TreasuryValidationParams:
    """Configuration for Treasury data validation."""
    close_col: str
    ticker_type: str
    min_plausible: float
    max_plausible: float
    expected_range_decimal: Optional[tuple] = None
    expected_range_percent: Optional[tuple] = None
    expected_range_bps: Optional[tuple] = None

@dataclass
class EnvironmentParams:
    """Configuration for trading environment setup."""
    features_df: pd.DataFrame
    action_space_size: int
    observation_space_size: int
    max_steps: int = 250
    initial_cash: float = 100000.0

# --- Feature Management ---
class FeatureAligner:
    def __init__(self, expected_features: List[str]):
        self.expected_features = expected_features

    def align_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Properly align features without padding"""
        missing_features = set(self.expected_features) - set(data.columns)
        extra_features = set(data.columns) - set(self.expected_features)

        if missing_features:
            # Log error and provide meaningful defaults based on feature type
            for feature in missing_features:
                if 'return' in feature:
                    data[feature] = 0.0  # Returns default to 0
                elif 'volume' in feature:
                    data[feature] = data['volume'].mean() if 'volume' in data else 1e6
                else:
                    # Use domain-specific defaults
                    data[feature] = self._get_feature_default(feature)

            system_logger.warning("FeatureAligner", f"Added missing features with defaults: {missing_features}")

        if extra_features:
            system_logger.warning("FeatureAligner", f"Removing extra features: {extra_features}")
            data = data.drop(columns=list(extra_features))

        # Ensure correct order
        return data[self.expected_features]

    def _get_feature_default(self, feature_name: str) -> float:
        """Get appropriate default based on feature type"""
        # Implement domain-specific logic based on feature name patterns
        if 'price' in feature_name.lower():
            return 100.0  # Reasonable default for price features
        elif 'volume' in feature_name.lower():
            return 1000000.0  # Reasonable default for volume features
        elif 'volatility' in feature_name.lower() or 'vix' in feature_name.lower():
            return 20.0  # Reasonable default for volatility features
        elif 'rate' in feature_name.lower() or 'yield' in feature_name.lower():
            return 0.03  # Reasonable default for rate features
        else:
            return 0.0  # Safe default for other features

# --- Global State Management ---
# Configuration instance is created above
trading_config = TradingConfig()
trading_config.initial_cash = INITIAL_CASH  # Set from existing config

# Create trading system instance
trading_system = TradingSystem(trading_config)
logging.info("TradingSystem initialized with configuration.")

# --- Global Constants & Configuration ---
# Note: Using INITIAL_CASH (defined later) as the single source of truth for initial balance
MAX_STEPS_PER_EPISODE = 250    # Max steps per episode for evaluation, consistent with Monitor wrapper

# Enhanced Optuna Pruning Callback for Stable Baselines3 with Win Rate Tracking
class OptunaPruningEvalCallback(EvalCallback):
    def __init__(self, eval_env, trial: optuna.Trial, eval_freq: int, log_path: str, best_model_save_path: str, n_eval_episodes: int = 5, deterministic: bool = True, render: bool = False, verbose: int = 0):
        super().__init__(eval_env,
                         eval_freq=eval_freq,
                         log_path=log_path,
                         best_model_save_path=best_model_save_path,
                         n_eval_episodes=n_eval_episodes,
                         deterministic=deterministic,
                         render=render,
                         verbose=verbose)
        self.trial = trial
        self.eval_idx = 0
        # ADAPTIVE EVALUATION TIMEOUT: Scale with total timesteps and evaluation episodes
        # Estimate: ~1-2 seconds per 1000 timesteps for evaluation
        base_eval_time = max(n_eval_episodes * 250 / 1000 * 1.5, 180)  # Minimum 3 minutes
        self.evaluation_timeout = min(base_eval_time, 1800)  # Maximum 30 minutes per evaluation
        self.last_evaluation_time = 0
        self.failed_evaluations = 0
        self.max_failed_evaluations = 3

        # ENHANCED: Track additional performance metrics for better trial evaluation
        self.last_win_rate = None
        self.last_total_trades = None
        self.last_total_profit = None
        self.last_composite_score = None

    def _extract_trading_performance(self, env):
        """Extract trading performance metrics from the environment's action scheme."""
        try:
            # Access the action scheme through the environment wrapper
            action_scheme = None

            # Try different ways to access the action scheme
            if hasattr(env, 'envs') and len(env.envs) > 0:
                # VecEnv wrapper
                base_env = env.envs[0]
                if hasattr(base_env, 'env'):
                    # Monitor wrapper
                    base_env = base_env.env
                if hasattr(base_env, 'action_scheme'):
                    action_scheme = base_env.action_scheme
            elif hasattr(env, 'action_scheme'):
                # Direct access
                action_scheme = env.action_scheme

            if action_scheme and hasattr(action_scheme, 'trade_count'):
                total_trades = action_scheme.trade_count
                win_count = getattr(action_scheme, 'win_count', 0)
                total_profit = getattr(action_scheme, 'total_profit', 0.0)

                win_rate = (win_count / total_trades) if total_trades > 0 else 0.0

                return {
                    'win_rate': win_rate,
                    'total_trades': total_trades,
                    'win_count': win_count,
                    'total_profit': total_profit
                }
            else:
                logging.debug("Could not access action_scheme for trading performance metrics")
                return None

        except Exception as e:
            logging.debug(f"Error extracting trading performance: {e}")
            return None

    def _calculate_composite_score(self, mean_reward, win_rate, total_trades):
        """
        Calculate a composite score combining mean reward and win rate.

        Args:
            mean_reward: Average reward per episode
            win_rate: Percentage of profitable trades (0.0 to 1.0)
            total_trades: Total number of trades executed

        Returns:
            Composite score for trial ranking
        """
        # Normalize win rate to 0-100 scale to match reward magnitude
        win_rate_scaled = win_rate * 100

        # Weight factors (can be tuned)
        reward_weight = 0.7  # Primary focus on reward
        win_rate_weight = 0.3  # Secondary focus on win rate

        # Base composite score
        composite = (reward_weight * mean_reward) + (win_rate_weight * win_rate_scaled)

        # Apply trade count bonus: more trades = more statistical significance
        # Bonus maxes out at 20 trades, adds up to 10% to the score
        trade_bonus_factor = min(total_trades / 20.0, 1.0) * 0.1
        composite *= (1.0 + trade_bonus_factor)

        return composite

    def _on_step(self) -> bool:
        continue_training = True
        if self.eval_freq > 0 and self.n_calls % self.eval_freq == 0:
            import time
            eval_start_time = time.time()

            logging.info(f"OptunaPruningEvalCallback: Starting evaluation at step {self.num_timesteps} (trial {self.trial.number})")

            try:
                # Perform evaluation with timeout protection
                evaluation_successful = False

                # Call parent evaluation with timeout protection
                super()._on_step()

                # Check if evaluation completed within reasonable time
                eval_duration = time.time() - eval_start_time
                if eval_duration > self.evaluation_timeout:
                    logging.warning(f"Evaluation took too long ({eval_duration:.1f}s). Skipping this evaluation.")
                    self.failed_evaluations += 1
                else:
                    evaluation_successful = True
                    self.last_evaluation_time = eval_duration
                    self.failed_evaluations = 0  # Reset counter on success

                # Report to Optuna if evaluation was successful
                if evaluation_successful and self.last_mean_reward is not None:
                    # ENHANCED: Extract trading performance metrics
                    trading_perf = self._extract_trading_performance(self.eval_env)

                    if trading_perf:
                        self.last_win_rate = trading_perf['win_rate']
                        self.last_total_trades = trading_perf['total_trades']
                        self.last_total_profit = trading_perf['total_profit']

                        # Calculate composite score combining mean reward and win rate
                        self.last_composite_score = self._calculate_composite_score(
                            self.last_mean_reward,
                            self.last_win_rate,
                            self.last_total_trades
                        )

                        logging.info(f"OptunaPruningEvalCallback: Enhanced metrics - "
                                   f"Mean Reward: {self.last_mean_reward:.2f}, "
                                   f"Win Rate: {self.last_win_rate:.1%}, "
                                   f"Total Trades: {self.last_total_trades}, "
                                   f"Composite Score: {self.last_composite_score:.2f}")

                        # Report composite score to Optuna instead of just mean reward
                        self.trial.report(self.last_composite_score, self.eval_idx)
                    else:
                        # Fallback to mean reward if trading performance unavailable
                        logging.info(f"OptunaPruningEvalCallback: Using mean_reward {self.last_mean_reward} (trading metrics unavailable)")
                        self.trial.report(self.last_mean_reward, self.eval_idx)

                    self.eval_idx += 1

                    # Check for pruning (but don't prune during optimization to ensure trials complete)
                    if hasattr(self.trial.study, 'pruner') and not isinstance(self.trial.study.pruner, optuna.pruners.NopPruner):
                        if self.trial.should_prune():
                            logging.info(f"Trial {self.trial.number} pruned by Optuna at step {self.num_timesteps} with mean reward {self.last_mean_reward}.")
                            raise optuna.exceptions.TrialPruned(f"Pruned at step {self.eval_idx} with value {self.last_mean_reward}.")
                else:
                    logging.warning(f"OptunaPruningEvalCallback: Evaluation failed or last_mean_reward is None at step {self.num_timesteps}")
                    self.failed_evaluations += 1

                # Stop trial if too many evaluation failures
                if self.failed_evaluations >= self.max_failed_evaluations:
                    logging.error(f"Trial {self.trial.number}: Too many evaluation failures ({self.failed_evaluations}). Stopping trial.")
                    raise optuna.exceptions.TrialPruned(f"Too many evaluation failures: {self.failed_evaluations}")

            except optuna.exceptions.TrialPruned:
                raise  # Re-raise pruning exceptions
            except Exception as e:
                logging.error(f"OptunaPruningEvalCallback: Error during evaluation: {e}")
                self.failed_evaluations += 1
                if self.failed_evaluations >= self.max_failed_evaluations:
                    raise optuna.exceptions.TrialPruned(f"Evaluation error: {e}")

        return continue_training



# Requests and rate limiting imports
try:
    from requests import Session as RequestsSession
    from requests_cache import CacheMixin, SQLiteCache
    from requests_ratelimiter import LimiterMixin, MemoryQueueBucket
    from pyrate_limiter import Duration, RequestRate, Limiter, BucketFullException
    REQUESTS_AVAILABLE = True
except ImportError as e:
    logging.error(f"Requests libraries not available: {e}")
    logging.error("Please install: pip install requests requests-cache requests-ratelimiter pyrate-limiter")
    REQUESTS_AVAILABLE = False
    # Create dummy classes
    class RequestsSession: pass
    class CacheMixin: pass
    class LimiterMixin: pass
    class SQLiteCache: pass
    class MemoryQueueBucket: pass
    class Duration: pass
    class RequestRate: pass
    class Limiter: pass
    class BucketFullException(Exception): pass

# Option Pricing / Greeks
try:
    from py_vollib.black_scholes import black_scholes
    from py_vollib.black_scholes.greeks.analytical import delta as bs_delta_analytic
    from py_vollib.black_scholes.greeks.analytical import gamma as bs_gamma_analytic
    from py_vollib.black_scholes.greeks.analytical import theta as bs_theta_analytic
    from py_vollib.black_scholes.greeks.analytical import vega as bs_vega_analytic
    logging.info("py_vollib library found.")
except ImportError:
    logging.warning("py_vollib library not found. Option calculations will not be available. pip install py_vollib")
    def black_scholes(*_args, **_kwargs): return 0.0
    def bs_delta_analytic(*_args, **_kwargs): return 0.0
    def bs_gamma_analytic(*_args, **_kwargs): return 0.0
    def bs_theta_analytic(*_args, **_kwargs): return 0.0
    def bs_vega_analytic(*_args, **_kwargs): return 0.0

# --- Cached Limiter Session Definition ---
if REQUESTS_AVAILABLE:
    class CachedLimiterSession(CacheMixin, LimiterMixin, RequestsSession):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self.hooks['response'] = [self._log_request_status]

        def _log_request_status(self, response, *args, **kwargs):
            """Log request details at debug level for monitoring"""
            if response.status_code != 200:
                logging.debug(f"Request to {response.url} returned status code {response.status_code}")
            return response
else:
    class CachedLimiterSession:
        def __init__(self, *args, **kwargs):
            logging.error("CachedLimiterSession not available - requests libraries missing")
        def get(self, *args, **kwargs):
            raise ImportError("Requests libraries not available")


# --- Threading-based Timeout Implementation ---
# Windows doesn't support signal.SIGALRM, so we use threading-based timeouts instead
import concurrent.futures

class SPY:
    # Fetch data with timeout protection
    @staticmethod
    def fetch_with_timeout(func, func_name=None, timeout=30, executor=None):
        """Improved version with proper thread cleanup"""
        executor_created = False
        if executor is None:
            executor = concurrent.futures.ThreadPoolExecutor(max_workers=1)
            executor_created = True

        try:
            future = executor.submit(func)
            result = future.result(timeout=timeout)
            return result, None
        except concurrent.futures.TimeoutError:
            # Cancel the future to prevent resource leaks
            future.cancel()
            if func_name is None:
                func_name = getattr(func, '__name__', str(func))
            logging.warning(f"Operation {func_name} timed out after {timeout}s")
            return None, TimeoutError(func_name, timeout)
        except Exception as e:
            logging.error(f"Error in {func_name if func_name else str(func)}: {str(e)}")
            return None, e
        finally:
            # Clean up the executor if we created it
            if executor_created and executor:
                try:
                    executor.shutdown(wait=False, cancel_futures=True)
                except Exception as cleanup_error:
                    logging.debug(f"Error during executor cleanup: {cleanup_error}")

    @contextmanager
    def managed_thread_pool(max_workers=1):
        """Context manager for thread pool management"""
        executor = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers)
        try:
            yield executor
        finally:
            executor.shutdown(wait=True, cancel_futures=True)
    @staticmethod
    def fetch_with_retry(func, max_retries=2, initial_timeout=30, *args, **kwargs):
        """Execute a function with timeout and retry"""
        timeout = initial_timeout

        for attempt in range(max_retries):
            logging.info(f"Attempt {attempt+1}/{max_retries} with timeout {timeout}s")
            result, error = SPY.fetch_with_timeout(func, timeout, *args, **kwargs)

            if error is None:
                return result, None

            if isinstance(error, TimeoutError):
                # Keep same timeout for consistency
                logging.warning(f"Timeout on attempt {attempt+1}, retrying with same timeout")
            else:
                # If it's not a timeout error, don't retry
                return None, error

        func_name = getattr(func, '__name__', str(func))
        return None, TimeoutError(f"{func_name}_retry", timeout * max_retries)

# Synthetic data generation removed - using only authentic yfinance data

# DataCleaner class for consistent zero value handling
class DataCleaner:
    def __init__(self, zero_threshold=1e-10):
        self.zero_threshold = zero_threshold
        
    def clean_financial_data(self, data: pd.Series, name: str) -> pd.Series:
        """Simple, predictable data cleaning"""
        # Step 1: Replace zeros and near-zeros
        mask_zero = data.abs() < self.zero_threshold
        if mask_zero.any():
            logging.info(f"Replacing {mask_zero.sum()} zero values in {name}")
            data[mask_zero] = np.nan
        
        # Step 2: Simple interpolation
        if data.isna().any():
            data = data.interpolate(method='linear', limit=5)
            
        # Step 3: Fill remaining with median
        if data.isna().any():
            median_val = data.median()
            if np.isnan(median_val):
                median_val = 0.001  # Last resort
            data = data.fillna(median_val)
            
        return data

# --- Legacy Constants (Replaced by TradingConfig) ---
# These constants are now managed by TradingConfig class above
# Keeping for backward compatibility during transition

# Use config instance for all new references
ALL_TICKERS = config.get_all_tickers()
INDEX_LIKE_TICKERS = config.get_index_like_tickers()

# Additional legacy constants
UNDERLYING_TICKER = config.UNDERLYING_TICKER
VIX_TICKER = config.VIX_TICKER
VIX3M_TICKER = config.VIX3M_TICKER
IRX_TICKER = config.IRX_TICKER
TNX_TICKER = config.TNX_TICKER
INTERVAL = config.INTERVAL
STRICT_MARKET_DATA_VALIDATION = config.STRICT_MARKET_DATA_VALIDATION

# Reward scheme coefficients
THETA_PENALTY_COEFF = config.THETA_PENALTY_COEFF
VEGA_PENALTY_COEFF = config.VEGA_PENALTY_COEFF
PROFIT_REWARD_MULTIPLIER = config.PROFIT_REWARD_MULTIPLIER
CONSISTENT_PROFIT_BONUS = config.CONSISTENT_PROFIT_BONUS
LOSS_PENALTY_MULTIPLIER = config.LOSS_PENALTY_MULTIPLIER
SHARPE_RATIO_WEIGHT = config.SHARPE_RATIO_WEIGHT
DRAWDOWN_PENALTY_COEFF = config.DRAWDOWN_PENALTY_COEFF



# Training & Evaluation Periods - Automated calculation
from datetime import datetime, timedelta

# FIXED DATES: Use fixed date ranges for fair testing of different parameters
# Fixed end date: June 15, 2025 (for consistent testing across different runs)
# This ensures reproducible results when testing different parameters
FIXED_END_DATE = datetime(2025, 6, 15)  # Fixed end date for fair testing

TODAY = FIXED_END_DATE.strftime('%Y-%m-%d')
# Add 3-day buffer to avoid requesting very recent data that may not be available
EVAL_END_BUFFER = (FIXED_END_DATE - timedelta(days=3)).strftime('%Y-%m-%d')
ONE_YEAR_AGO = (FIXED_END_DATE - timedelta(days=365)).strftime('%Y-%m-%d')
FIVE_YEARS_AGO = (FIXED_END_DATE - timedelta(days=365*5)).strftime('%Y-%m-%d')

# Training period: 5 years ago to 1 year ago (4 years of data)
TRAIN_START_DATE = FIVE_YEARS_AGO
TRAIN_END_DATE = (datetime.strptime(ONE_YEAR_AGO, '%Y-%m-%d') - timedelta(days=1)).strftime('%Y-%m-%d')

# Evaluation period: 1 year ago to 3 days ago (avoid very recent data issues)
EVAL_START_DATE = ONE_YEAR_AGO
EVAL_END_DATE = EVAL_END_BUFFER

logging.info(f"FIXED DATES: Using fixed end date {TODAY} for fair parameter testing")
logging.info(f"Fixed date ranges for reproducible testing:")
logging.info(f"Training period: {TRAIN_START_DATE} to {TRAIN_END_DATE} (4 years)")
logging.info(f"Evaluation period: {EVAL_START_DATE} to {EVAL_END_DATE} (1 year, with 3-day buffer)")
logging.info(f"Total data span: {FIVE_YEARS_AGO} to {TODAY} (5 years)")
logging.info(f"Note: Fixed dates ensure consistent testing across different parameter configurations")

# RL Training Configuration (using config)
TOTAL_TIMESTEPS = config.TOTAL_TIMESTEPS
N_EVAL_EPISODES = config.N_EVAL_EPISODES
EVAL_FREQ_DIVISOR = config.EVAL_FREQ_DIVISOR

# File Paths & Directories (using config)
SCRIPT_VERSION_TAG = config.SCRIPT_VERSION_TAG
# Get the directory where the SPY.py script is located
SCRIPT_DIRECTORY = os.path.dirname(os.path.abspath(__file__))

# Define SB3_LOG_DIR relative to the script's directory
SB3_LOG_DIR = os.path.join(SCRIPT_DIRECTORY, f"rl_logs_yfinance_options_{SCRIPT_VERSION_TAG}")
# Define MODEL_DIR relative to the script's directory
MODEL_DIR = os.path.join(SCRIPT_DIRECTORY, f"models_options_{SCRIPT_VERSION_TAG}")

# Ensure these directories exist when the script starts
if not os.path.exists(SB3_LOG_DIR):
    os.makedirs(SB3_LOG_DIR, exist_ok=True)
if not os.path.exists(MODEL_DIR):
    os.makedirs(MODEL_DIR, exist_ok=True)
SB3_MODEL_FILENAME = f"ppo_{UNDERLYING_TICKER.lower()}_option_trader_{SCRIPT_VERSION_TAG}.zip"
NORMALIZE_STATS_FILENAME = os.path.join(SB3_LOG_DIR, f"vec_normalize_options_yfinance_{SCRIPT_VERSION_TAG}.pkl")
MODEL_WITH_STATS_DIR = os.path.join(MODEL_DIR, "with_norm_stats")  # Directory for saving models with normalization stats
os.makedirs(MODEL_WITH_STATS_DIR, exist_ok=True)
QUANTSTATS_REPORT_FILENAME = os.path.abspath(os.path.join(SB3_LOG_DIR, f'quantstats_report_options_{SCRIPT_VERSION_TAG}_basic.html'))
QUANTSTATS_OUTPUT = True  # Flag to control whether QuantStats reports should be generated

# Option Action Space Definition (using config)
STRIKE_CATEGORIES_PCT = config.STRIKE_CATEGORIES_PCT
EXPIRY_CATEGORIES_DTE = config.EXPIRY_CATEGORIES_DTE
N_STRIKES = len(STRIKE_CATEGORIES_PCT)
N_EXPIRIES = len(EXPIRY_CATEGORIES_DTE)
N_OPTION_TYPES = 2 # Call, Put
ACTION_SPACE_SIZE = config.get_action_space_size()

# Observation Space Features (using config)
N_STATIC_HELD_OPTION_FEATURES = config.N_STATIC_HELD_OPTION_FEATURES
N_STATIC_PORTFOLIO_FEATURES = config.N_STATIC_PORTFOLIO_FEATURES
DEFAULT_STATIC_HELD_OPTION_FEATURES = config.get_default_held_option_features()

# Global backup references for environment components
portfolio_backup = None
action_scheme_backup = None
reward_scheme_backup = None

# --- Setup Logging, Directories, Precision ---
warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", category=UserWarning)
os.makedirs(SB3_LOG_DIR, exist_ok=True)
os.makedirs(MODEL_DIR, exist_ok=True)
getcontext().prec = 28

log_file_path = os.path.join(SB3_LOG_DIR, f'{UNDERLYING_TICKER.lower()}_option_training_{SCRIPT_VERSION_TAG}.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout), logging.FileHandler(log_file_path, mode='w')],
    force=True
)
logging.info(f"Logging configured for Option Trader {SCRIPT_VERSION_TAG}.")
logging.info(f"Log file: {log_file_path}")
logging.info(f"Tickers to fetch: {ALL_TICKERS}")

# --- Initialize Shared yfinance Session ---
def initialize_shared_yf_session():
    """Initialize a shared yfinance session with caching and rate limiting."""
    try:
        # OPTIMIZED: More aggressive rate limiting to reduce wait times
        session = CachedLimiterSession(
            limiter=Limiter(
                RequestRate(1, Duration.SECOND*3),  # max 1 request per 3 seconds (reduced from 8s)
                RequestRate(8, Duration.MINUTE),    # max 8 requests per minute (increased from 4)
                RequestRate(80, Duration.HOUR)      # max 80 requests per hour (increased from 40)
            ),
            bucket_class=MemoryQueueBucket,
            backend=SQLiteCache("yfinance.cache", expire_after=timedelta(days=1)),
        )

        # Use a realistic browser user-agent instead of identifying as a bot
        # This helps avoid being blocked by Yahoo's anti-scraping measures
        session.headers['User-agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'

        # Add browser-like headers to appear more like a regular user
        session.headers['Accept-Language'] = 'en-US,en;q=0.9'
        session.headers['Accept'] = 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8'
        session.headers['Accept-Encoding'] = 'gzip, deflate, br'
        session.headers['Connection'] = 'keep-alive'
        session.headers['Upgrade-Insecure-Requests'] = '1'
        session.headers['Cache-Control'] = 'max-age=0'

        logging.info("Initialized shared yfinance session with browser-like headers and very conservative rate limiting.")
        return session
    except Exception as e:
        logging.error(f"Error initializing shared yfinance session: {e}")
        # Fallback to a regular session with browser-like user agent
        session = RequestsSession()
        session.headers['User-agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        return session

# Create a shared session for all yfinance calls
shared_yf_session = initialize_shared_yf_session()

# --- Helper Functions ---
# (fetch_with_retry, process_yf_data, fetch_historical_data_yf, create_combined_features remain the same as previous version v3.1)
# (OptionActionScheme, OptionRewardScheme, OptionObserver, create_trading_environment, main, generate_trading_signal, _create_fallback_report, _create_equity_curve_chart, MinimalPredictionEnv, UnderlyingStopper, UnderlyingInformer, UnderlyingRenderer, GymnasiumWrapper also remain the same as they correctly use the dynamic RFR and handle index volumes)

def fetch_with_retry(yf_ticker_obj: yf.Ticker, method_name: str, *args, max_retries: int = 5, retry_delay: int = 15, **kwargs) -> Optional[Union[pd.DataFrame, Dict, List, str]]:
    """Fetch data from yfinance with retry logic and timeout protection."""
    # Add timeout protection to prevent hanging
    method_timeout = kwargs.pop('method_timeout', 30)  # Default 30 second timeout for individual calls

    for attempt in range(max_retries):
        try:
            # Get the method from the ticker object
            method = getattr(yf_ticker_obj, method_name)

            # Wrap the method call with timeout protection
            def call_method():
                return method(*args, **kwargs)

            # Use SPY.fetch_with_timeout to add timeout protection
            result, error = SPY.fetch_with_timeout(call_method, method_timeout)

            if error is not None:
                raise error

            return result

        except BucketFullException as rle:
            # Handle rate limit exceptions specifically
            logging.warning(f"Rate limit exceeded on attempt {attempt+1}/{max_retries} for {method_name}: {rle}")
            # Use a longer delay for rate limit errors but cap it
            sleep_time = min(retry_delay * 2 * (attempt + 1), 60)  # Cap at 60 seconds
            logging.info(f"Rate limited. Waiting {sleep_time:.2f} seconds before retry...")
            time.sleep(sleep_time)
        except Exception as e:
            logging.warning(f"Attempt {attempt+1}/{max_retries} failed for {method_name}: {e}")
            if attempt < max_retries - 1:
                # Reduced exponential backoff with cap to prevent excessive delays
                base_delay = min(retry_delay, 10)  # Cap base delay at 10 seconds
                jitter = random.uniform(0.9, 1.1)  # Reduced jitter range
                sleep_time = min(base_delay * (1.5 ** attempt) * jitter, 30)  # Cap at 30 seconds
                logging.info(f"Retrying in {sleep_time:.2f} seconds...")
                time.sleep(sleep_time)
            else:
                logging.error(f"All {max_retries} attempts failed for {method_name}")
                raise
    return None  # Should never reach here due to raise in the loop

# Data validation class for unified validation across financial data
class DataValidator:
    def __init__(self, fix_outliers=True, outlier_threshold=10.0):
        self.fix_outliers = fix_outliers
        self.outlier_threshold = outlier_threshold
    
    def validate_financial_data(self, data, data_name, expected_range):
        """Unified validation for all financial data"""
        min_val, max_val = expected_range
        mask = (data < min_val) | (data > max_val)
        bad_count = mask.sum()
        bad_percentage = (bad_count / len(data)) * 100
        
        if bad_count > 0:
            logging.warning(f"Found {bad_count} ({bad_percentage:.2f}%) outliers in {data_name}")
            
            if self.fix_outliers and bad_percentage < self.outlier_threshold:
                # Fix using interpolation
                data[mask] = np.nan
                data = data.interpolate(method='linear', limit_direction='both')
                data = data.fillna(method='bfill').fillna(method='ffill')
                logging.info(f"Fixed {bad_count} outliers in {data_name}")
            elif bad_percentage >= self.outlier_threshold:
                raise ValueError(f"Too many outliers in {data_name}: {bad_percentage:.2f}%")
        
        return data

# Helper functions for Treasury rate validation and conversion
def _validate_vix_data(df: pd.DataFrame, close_col: str, ticker_type: str, min_plausible: float, max_plausible: float) -> pd.DataFrame:
    """Validate VIX data without format conversion."""
    logging.info(f"Applying VIX validation for {ticker_type}")
    
    # Check for implausible values but don't replace them
    validator = DataValidator(fix_outliers=False)
    df[close_col] = validator.validate_financial_data(df[close_col], ticker_type, (min_plausible, max_plausible))
    
    return df

def _validate_and_convert_treasury_data(df: pd.DataFrame, close_col: str, ticker_type: str,
                                      expected_range_decimal: tuple, expected_range_percent: tuple,
                                      expected_range_bps: tuple, min_plausible: float, max_plausible: float) -> pd.DataFrame:
    """
    Validate and convert Treasury rate data to consistent decimal format.

    STANDARDIZED ASSUMPTION: IRX/TNX data from yfinance is consistently provided in percentage format (e.g., 5.25 for 5.25%).
    This function converts it to decimal format (e.g., 0.0525) and validates against plausible decimal ranges.
    """
    logging.info(f"Applying standardized Treasury rate validation for {ticker_type}")
    logging.info(f"ASSUMPTION: Input data is in percentage format, will convert to decimal format")

    validator = DataValidator(fix_outliers=True, outlier_threshold=10.0)
    
    # Initial validation for extreme outliers
    df[close_col] = validator.validate_financial_data(df[close_col], f"{ticker_type} (pre-conversion)", (-5.0, 1000.0))
    
    # Convert from percentage format to decimal format (e.g., 5.25% -> 0.0525)
    df[close_col] = df[close_col] / 100.0
    logging.info(f"{ticker_type} converted from percentage to decimal format")
    
    # Log statistics after conversion
    logging.info(f"{ticker_type} after conversion to decimal: min={df[close_col].min():.6f}, max={df[close_col].max():.6f}, median={df[close_col].median():.6f}")
    
    # Validate after conversion using decimal range
    decimal_min_plausible = min_plausible / 100.0
    decimal_max_plausible = max_plausible / 100.0
    df[close_col] = validator.validate_financial_data(df[close_col], f"{ticker_type} (post-conversion)", (decimal_min_plausible, decimal_max_plausible))
    
    return df

def process_yf_data(df: pd.DataFrame, ticker: str, min_periods_override: Optional[int] = None) -> pd.DataFrame:
    if df is None or df.empty:
        logging.warning(f"✗ FAIL: Empty DataFrame for processing {ticker}. Returning empty.")
        return pd.DataFrame()

    # Check if this is a critical ticker
    is_vix = ticker == VIX_TICKER
    is_irx = ticker == IRX_TICKER
    is_tnx = ticker == TNX_TICKER
    is_critical = is_vix or is_irx or is_tnx  # Now using this variable

    # Set minimum periods based on override or ticker type
    if min_periods_override is not None:
        min_periods = min_periods_override
        logging.info(f"Using min_periods_override={min_periods} for {ticker}")
    elif is_critical:
        min_periods = 100  # Critical tickers need more data
    else:
        min_periods = 50   # Non-critical tickers can work with less data

    # Handle multi-level columns that may be returned by yfinance
    if isinstance(df.columns, pd.MultiIndex):
        logging.info(f"Detected multi-level columns for {ticker}. Flattening.")
        # Get the first level values and convert to lowercase
        df.columns = [col.lower() for col in df.columns.get_level_values(0)]

    try:
        df = df.copy()
        if not isinstance(df.index, pd.DatetimeIndex): df.index = pd.to_datetime(df.index, utc=True)
        elif df.index.tzinfo is None: df.index = df.index.tz_localize('UTC')
        else: df.index = df.index.tz_convert('UTC')

        clean_ticker = ticker.replace('^', '').replace('=F', '').replace('/', '_').upper()
        is_vix = ticker == VIX_TICKER
        is_irx = ticker == IRX_TICKER

        # Log raw data for VIX and IRX for debugging
        if is_vix or is_irx:
            logging.info(f"Processing {ticker} data with shape {df.shape}")
            logging.info(f"Raw {ticker} columns: {df.columns.tolist()}")
            if 'Close' in df.columns:
                logging.info(f"Raw {ticker} Close values - first: {df['Close'].iloc[0]}, last: {df['Close'].iloc[-1]}")

        # Standardize column names and rename with ticker suffix
        new_columns = {}
        required_cols = {'open': False, 'high': False, 'low': False, 'close': False, 'volume': False}

        for col in df.columns:
            col_lower = str(col).lower()
            if col_lower in required_cols:
                new_columns[col] = f"{col_lower}_{clean_ticker}"
                required_cols[col_lower] = True
            elif 'adj close' == col_lower:
                pass  # Skip adjusted close

        df.rename(columns=new_columns, inplace=True)

        # Define expected column names
        ohlcv_cols_renamed = [f"{c}_{clean_ticker}" for c in ['open', 'high', 'low', 'close', 'volume']]
        missing_cols = [col for col in ohlcv_cols_renamed if col not in df.columns]
        vol_col_name = f'volume_{clean_ticker}'

        # Handle missing volume for known index-like tickers
        if vol_col_name in missing_cols and (clean_ticker in INDEX_LIKE_TICKERS or is_vix or is_irx or ticker == TNX_TICKER):
            logging.debug(f"Volume data missing for index {ticker}, filling with 0.")
            df[vol_col_name] = 0.0
            required_cols['volume'] = True
            missing_cols = [col for col in ohlcv_cols_renamed if col not in df.columns and col != vol_col_name]

        # Check for missing required columns
        if missing_cols:
            missing_keys = [k for k, _v in required_cols.items() if f"{k}_{clean_ticker}" in missing_cols]
            logging.error(f"Missing required OHLC(V) columns for {ticker}: {missing_keys}. Available: {df.columns.tolist()}.")
            return pd.DataFrame()

        # Keep only columns with ticker suffix
        cols_to_keep = [col for col in df.columns if col.endswith(f"_{clean_ticker}")]
        df = df[cols_to_keep]

        # Convert to numeric and forward fill
        for col in ohlcv_cols_renamed:
            if col in df.columns: df[col] = pd.to_numeric(df[col], errors='coerce')
        df[ohlcv_cols_renamed] = df[ohlcv_cols_renamed].ffill()

        # Enhanced validation for VIX, IRX and TNX data with format detection and conversion
        close_col = f"close_{clean_ticker}"
        if (is_vix or is_irx or ticker == TNX_TICKER) and close_col in df.columns:
            # Define plausible ranges and format detection for each ticker
            if is_vix:
                min_plausible = 5.0
                max_plausible = 100.0
                ticker_type = "VIX"
                # VIX doesn't need format conversion
                df = _validate_vix_data(df, close_col, ticker_type, min_plausible, max_plausible)
            elif is_irx or ticker == TNX_TICKER:
                # Treasury rates need special format detection and conversion
                if is_irx:
                    ticker_type = "IRX (3-month Treasury yield)"
                    # Expanded ranges to include historical highs (e.g., early 1980s rates reached ~20%)
                    expected_range_decimal = (0.0, 0.20)  # 0-20% in decimal form
                    expected_range_percent = (0.0, 20.0)   # 0-20% in percentage form
                    expected_range_bps = (0.0, 2000.0)     # 0-2000 basis points
                    min_plausible = 0.0
                    max_plausible = 20.0  # Increased from 8.0 to 20.0 for historical accuracy
                else:  # TNX
                    ticker_type = "TNX (10-year Treasury yield)"
                    # Expanded ranges to include historical highs (e.g., early 1980s rates reached ~15-16%)
                    expected_range_decimal = (0.005, 0.18)  # 0.5-18% in decimal form
                    expected_range_percent = (0.5, 18.0)    # 0.5-18% in percentage form
                    expected_range_bps = (50.0, 1800.0)     # 50-1800 basis points
                    min_plausible = 0.5
                    max_plausible = 18.0  # Increased from 15.0 to 18.0 for historical accuracy

                df = _validate_and_convert_treasury_data(df, close_col, ticker_type,
                                                       expected_range_decimal, expected_range_percent,
                                                       expected_range_bps, min_plausible, max_plausible)

            # Log final validation results
            first_val = df[close_col].iloc[0] if len(df) > 0 else None
            last_val = df[close_col].iloc[-1] if len(df) > 0 else None
            logging.info(f"Processed {ticker_type} close values - first: {first_val}, last: {last_val}")

            # For Treasury rates, log the final decimal format values
            if is_irx or ticker == TNX_TICKER:
                if last_val is not None:
                    # Data is now guaranteed to be in decimal format after _validate_and_convert_treasury_data
                    rfr_decimal = last_val  # Already in decimal format
                    logging.info(f"VALIDATION: Final {ticker_type} rate (decimal format): {rfr_decimal:.6f} ({rfr_decimal*100:.4f}%)")

                    # Check if risk-free rate is suspiciously low or high
                    if rfr_decimal < 0.001:
                        logging.warning(f"VALIDATION WARNING: {ticker_type} implies very low risk-free rate: {rfr_decimal:.6f} ({rfr_decimal*100:.4f}%)")
                    elif rfr_decimal > 0.10:
                        logging.warning(f"VALIDATION WARNING: {ticker_type} implies very high risk-free rate: {rfr_decimal:.6f} ({rfr_decimal*100:.4f}%)")
                else:
                    logging.warning(f"VALIDATION WARNING: No valid {ticker_type} data to calculate risk-free rate")

        # Remove rows with NaN in OHLC columns
        ohlc_cols = [f"{c}_{clean_ticker}" for c in ['open', 'high', 'low', 'close']]
        df.dropna(subset=ohlc_cols, inplace=True)
        if df.empty:
            logging.warning(f"DataFrame empty after NaNs drop for {ticker}.")
            return pd.DataFrame()

        # Calculate additional features only for SPY, not for VIX indices or treasury rates
        open_col, high_col, low_col, close_col, volume_col = ohlcv_cols_renamed

        # Special handling for VIX indices and treasury rates - ONLY keep close columns
        is_market_index = ticker in ['^VIX', '^VIX3M', '^TNX', '^IRX']

        if is_market_index:
            # For market indices, keep only close column (removing OHLV features)
            feature_columns_to_keep = [close_col]
            logging.info(f"Market index {ticker}: Keeping only {feature_columns_to_keep}")
        else:
            # For SPY, keep all OHLCV columns (reverting to full OHLCV features for RL)
            feature_columns_to_keep = [open_col, high_col, low_col, close_col, volume_col]

        # Only calculate additional features for SPY
        if ticker == UNDERLYING_TICKER:
            # Calculate ATR (14) - Average True Range
            if len(df) >= 15:  # ATR(14) needs 14 data points + 1 for initial calculation
                try:
                    df[f'atr_{clean_ticker}'] = ta.volatility.average_true_range(
                        high=df[high_col], low=df[low_col], close=df[close_col], window=14, fillna=False)
                except Exception as e_atr:
                    logging.error(f"Error ATR for {ticker}: {e_atr}. NaN set.")
                    df[f'atr_{clean_ticker}'] = np.nan
            else:
                logging.warning(f"Not enough data for ATR({ticker}).")
                df[f'atr_{clean_ticker}'] = np.nan

            # Calculate RSI (14) - Relative Strength Index
            if len(df) >= 15:  # RSI(14) needs 14 data points + 1 for initial calculation
                try:
                    df[f'rsi_{clean_ticker}'] = ta.momentum.rsi(
                        close=df[close_col], window=14, fillna=False)
                except Exception as e_rsi:
                    logging.error(f"Error RSI for {ticker}: {e_rsi}. NaN set.")
                    df[f'rsi_{clean_ticker}'] = np.nan
            else:
                logging.warning(f"Not enough data for RSI({ticker}).")
                df[f'rsi_{clean_ticker}'] = np.nan

            # Calculate Williams %R (WR) - momentum indicator
            if len(df) >= 14:  # Williams %R needs sufficient data points for lbp=14 calculation
                try:
                    if TA_AVAILABLE:
                        df[f'wr_{clean_ticker}'] = williams_r(
                            high=df[high_col], low=df[low_col], close=df[close_col],
                            lbp=14, fillna=False)
                    else:
                        logging.warning(f"TA library not available for Williams %R calculation")
                        df[f'wr_{clean_ticker}'] = np.nan
                except Exception as e_wr:
                    logging.error(f"Error calculating Williams %R for {ticker}: {e_wr}. NaN set.")
                    df[f'wr_{clean_ticker}'] = np.nan
            else:
                logging.warning(f"Not enough data for Williams %R({ticker}).")
                df[f'wr_{clean_ticker}'] = np.nan

            # Calculate SMA (50) - Simple Moving Average
            if len(df) >= 50:  # SMA(50) needs 50 data points for calculation
                try:
                    df[f'sma_{clean_ticker}'] = df[close_col].rolling(window=50, min_periods=50).mean()
                except Exception as e_sma:
                    logging.error(f"Error SMA(50) for {ticker}: {e_sma}. NaN set.")
                    df[f'sma_{clean_ticker}'] = np.nan
            else:
                logging.warning(f"Not enough data for SMA(50) ({ticker}).")
                df[f'sma_{clean_ticker}'] = np.nan

            # Calculate 3-month price change (approximately 63 trading days)
            if len(df) >= 63:  # 3-month price change needs 63 data points (3 months * 21 trading days)
                try:
                    df[f'price_change_3m_{clean_ticker}'] = df[close_col].pct_change(periods=63)
                except Exception as e_3m:
                    logging.error(f"Error calculating 3-month price change for {ticker}: {e_3m}. NaN set.")
                    df[f'price_change_3m_{clean_ticker}'] = np.nan
            else:
                logging.warning(f"Not enough data for 3-month price change calculation ({ticker}).")
                df[f'price_change_3m_{clean_ticker}'] = np.nan

            # Add these columns to keep list (added Williams %R indicator)
            feature_columns_to_keep.extend([f'atr_{clean_ticker}', f'rsi_{clean_ticker}', f'wr_{clean_ticker}', f'sma_{clean_ticker}', f'price_change_3m_{clean_ticker}'])

            # Debug logging for SPY
            logging.info(f"SPY: Keeping columns {feature_columns_to_keep}")
        feature_columns_to_keep = [col for col in feature_columns_to_keep if col in df.columns]
        df = df[feature_columns_to_keep]

        # Clean up data - CRITICAL FIX: Avoid zero-filling which corrupts observations
        df.replace([np.inf, -np.inf], np.nan, inplace=True)
        df = df.ffill().bfill()

        # CRITICAL FIX: Use interpolation instead of zero-filling for remaining NaN values
        # This preserves the natural data distribution and prevents all-zero observations
        remaining_nan_count = df.isna().sum().sum()

        # Initialize zero_percentage to avoid UnboundLocalError
        zero_percentage = 0

        if remaining_nan_count > 0:
            # Initialize DataCleaner for consistent zero value handling
            data_cleaner = DataCleaner(zero_threshold=1e-10)

            # Clean each column using the DataCleaner
            for col in df.columns:
                df[col] = data_cleaner.clean_financial_data(df[col], f"{ticker}_{col}")

        # Log data quality metrics (moved outside the if block)
        zero_contamination_check = (df == 0).sum().sum()
        total_values = df.size
        zero_percentage = (zero_contamination_check / total_values * 100) if total_values > 0 else 0

        logging.info(f"Processed {ticker} data shape: {df.shape}. Columns: {df.columns.tolist()}")
        logging.info(f"Data quality check: {zero_contamination_check}/{total_values} ({zero_percentage:.1f}%) zero values")

        if zero_percentage > 50:
            logging.error(f"CRITICAL: {ticker} data has {zero_percentage:.1f}% zero values - this will cause all-zero observations!")

        # Final validation using min_periods requirement
        if len(df) < min_periods:
            logging.warning(f"✗ FAIL: Final data insufficient for {ticker}: {len(df)} rows (minimum {min_periods} required)")
            return pd.DataFrame()

        return df
    except Exception as e:
        logging.error(f"Error processing data for {ticker}: {e}", exc_info=True)
        return pd.DataFrame()

def fetch_historical_data_yf(ticker: str, start: str, end: str, interval: str = '1d', session: Optional[CachedLimiterSession] = None, min_periods_override: Optional[int] = None, skip_problematic: bool = False, operation_type: str = 'training') -> pd.DataFrame:
    """
    Main data fetching function - now delegates to refactored version for better maintainability.

    This function is maintained for backward compatibility but uses the new refactored
    implementation which provides better error handling, performance monitoring, and
    standardized logging.
    """
    # Call the refactored function with individual parameters
    return fetch_historical_data_yf_refactored(
        ticker=ticker,
        start=start,
        end=end,
        interval=interval,
        session=session,
        min_periods_override=min_periods_override,
        skip_problematic=skip_problematic,
        operation_type=operation_type
    )

def fetch_historical_data_yf_legacy(ticker: str, start: str, end: str, interval: str = '1d', session: Optional[CachedLimiterSession] = None, min_periods_override: Optional[int] = None, skip_problematic: bool = False) -> pd.DataFrame:
    """Legacy implementation - kept for reference but not used in production."""
    logging.info(f"Fetching {interval} data for {ticker} from {start} to {end}...")

    # Track whether we're getting true data or using fallbacks
    data_source = "unknown"
    fetch_success = False

    # Circuit breaker: Track total time spent on this ticker
    start_time = time.time()
    # Increase max time for treasury rates, reduce for others
    if is_treasury:
        max_total_time = 180  # 3 minutes for treasury rates (increased from 2 minutes)
    elif is_vix3m:
        max_total_time = 120  # 2 minutes for VIX term structure
    else:
        max_total_time = 90   # 1.5 minutes for other tickers

    def check_circuit_breaker():
        elapsed = time.time() - start_time
        if elapsed > max_total_time:
            logging.error(f"CIRCUIT BREAKER: Stopping fetch for {ticker} after {elapsed:.1f}s (max: {max_total_time}s)")
            return True
        return False

    try:
        # Use the provided session or fall back to shared session
        active_session = session if session is not None else shared_yf_session

        # Special handling for VIX, VIX term structure, and treasury yield tickers
        is_vix = ticker == VIX_TICKER
        is_vix3m = ticker == VIX3M_TICKER
        is_irx = ticker == IRX_TICKER
        is_tnx = ticker == TNX_TICKER
        is_spy = ticker == UNDERLYING_TICKER and UNDERLYING_TICKER == "SPY"

        # VIX term structure tickers and treasury rates are often problematic
        is_vix_term = is_vix3m  # Only VIX3M now
        is_treasury = is_irx or is_tnx
        is_critical_ticker = is_vix or is_irx or is_tnx or is_vix_term
        is_problematic = is_vix3m or is_treasury  # VIX term structure tickers and treasury rates can be problematic

        # Critical tickers need special attention
        is_critical = is_critical_ticker

        # Further optimized delay strategy - minimal delays to avoid rate limits
        # Only add delays for problematic tickers
        if is_problematic:
            time.sleep(1.0)  # Reduced from 2.0 to 1.0 seconds
        elif is_critical:
            time.sleep(0.5)  # Reduced from 1.0 to 0.5 seconds
        else:
            time.sleep(0.25)  # Reduced from 0.5 to 0.25 seconds

        # Initialize variables
        df = pd.DataFrame()
        last_exception = None

        # Create ticker object with session
        logging.info(f"Trying ticker: {ticker}")
        yf_ticker = yf.Ticker(ticker, session=active_session)

        # Step 1: Try to get data using Ticker.history() method with timeout handling
        try:
            # For problematic tickers, use timeout handling with retry and exponential backoff
            if is_problematic:
                # Define the history fetching function
                def fetch_history():
                    # Disable repair for ALL tickers to ensure authentic, unmodified data
                    # repair=True can cause data corruption (e.g., Treasury rates multiplied by 100)

                    if interval == '1d':
                        return yf_ticker.history(start=start, end=end, interval=interval,
                                           auto_adjust=True, repair=False)
                    else:
                        df_local = yf_ticker.history(period="max", interval=interval,
                                          auto_adjust=True, repair=False)
                        if not df_local.empty:
                            return df_local.loc[start:end]
                        return df_local

                # Run with retry using REDUCED timeouts to prevent hanging during optimization
                # OPTIMIZATION FIX: Reduced timeouts to prevent trials from hanging
                if is_vix3m: # VIX3M gets moderate timeout
                    timeout = 60   # Reduced from 180s to 60s to prevent hanging
                    max_retries = 2  # Reduced from 4 to 2 retries
                elif is_treasury: # Reasonable timeouts for treasury rates (IRX/TNX)
                    timeout = 45   # Reduced from 120s to 45s to prevent hanging
                    max_retries = 2  # Reduced from 3 to 2 retries
                else: # For other 'is_problematic' tickers
                    timeout = 30   # Reduced from 90s to 30s to prevent hanging
                    max_retries = 2  # Reduced from 3 to 2 retries

                df, error = SPY.fetch_with_retry(
                    fetch_history,
                    max_retries=max_retries,
                    initial_timeout=timeout
                )
                if error is not None:
                    raise error
            else:
                # Standard approach for non-problematic tickers
                # Disable repair for ALL tickers to ensure authentic, unmodified data
                # repair=True can cause data corruption (e.g., Treasury rates multiplied by 100)

                if interval == '1d':
                    # For daily data, specify exact date range
                    df = yf_ticker.history(start=start, end=end, interval=interval,
                                          auto_adjust=True, repair=False)
                else:
                    # For other intervals, use period-based approach
                    df = yf_ticker.history(period="max", interval=interval,
                                          auto_adjust=True, repair=False)
                    # Filter to desired date range after fetching
                    if not df.empty:
                        df = df.loc[start:end]

            if not df.empty:
                data_source = "Ticker.history"
                fetch_success = True
                logging.info(f"SUCCESS: Retrieved {ticker} data using Ticker.history: {len(df)} rows")
                processed_df = process_yf_data(df, ticker, min_periods_override=min_periods_override)

                # Validate critical tickers (VIX, IRX, TNX) have reasonable values
                if is_critical and not processed_df.empty:
                    col_name = f"close_{ticker.replace('^', '').replace('=F', '').upper()}"
                    if col_name in processed_df.columns:
                        last_value = processed_df[col_name].iloc[-1] if len(processed_df) > 0 else None
                        if last_value is not None:
                            logging.info(f"VALIDATION: {ticker} last value: {last_value}")

                return processed_df
        except Exception as hist_e:
            logging.warning(f"Ticker.history failed for {ticker}: {hist_e}, falling back to download()")
            last_exception = hist_e
            # Ensure df is a valid DataFrame after exception
            if df is None:
                df = pd.DataFrame()


            # Step 2: If Ticker.history failed, try download() method with timeout
            if df.empty and not skip_problematic:
                logging.warning(f"Ticker.history failed for {ticker}, falling back to download()")
                # Explicit timeout assignment for yf.download in this primary fallback path
                if is_vix3m: # VIX3M specifically (enhanced from 20s to 25s)
                    current_yf_dl_timeout = 25
                elif is_treasury: # Treasury rates (IRX/TNX) - more reasonable timeout
                    current_yf_dl_timeout = 15 # Increased from 8 to 15 seconds for treasury rates
                elif is_problematic: # Other problematic tickers (not VIX3M, treasury)
                    current_yf_dl_timeout = 15 # Explicit value for "other problematic" in this path
                else: # Non-problematic tickers that failed history() and are in this yf.download fallback
                    current_yf_dl_timeout = 30 # Increased from 20 to 30 seconds for "non-problematic" in this path

                # Use fetch_with_timeout wrapper to ensure reliable timeout handling
                def download_with_timeout():
                    # Disable repair for ALL tickers to ensure authentic, unmodified data
                    # repair=True can cause data corruption (e.g., Treasury rates multiplied by 100)

                    return yf.download(
                        ticker,
                        start=start,
                        end=end,
                        interval=interval,
                        session=active_session,
                        progress=False,
                        auto_adjust=True,
                        prepost=False,
                        repair=False,  # Disabled for ALL tickers to prevent data corruption
                        threads=False,  # Disable threading to avoid rate limit issues
                        timeout=current_yf_dl_timeout
                    )

                # Apply external timeout wrapper for additional safety
                df, timeout_error = SPY.fetch_with_timeout(download_with_timeout, current_yf_dl_timeout + 5)

                if timeout_error is not None:
                    logging.warning(f"Download timed out for {ticker}: {timeout_error}")
                    df = pd.DataFrame()  # Ensure df is empty DataFrame on timeout

            if not df.empty:
                data_source = "yf.download"
                fetch_success = True
                logging.info(f"SUCCESS: Retrieved {ticker} data using download(): {len(df)} rows")
                processed_df = process_yf_data(df, ticker, min_periods_override=min_periods_override)

                # Validate critical tickers (VIX, IRX, TNX) have reasonable values
                if is_critical and not processed_df.empty:
                    col_name = f"close_{ticker.replace('^', '').replace('=F', '').upper()}"
                    if col_name in processed_df.columns:
                        last_value = processed_df[col_name].iloc[-1] if len(processed_df) > 0 else None
                        if last_value is not None:
                            logging.info(f"VALIDATION: {ticker} last value: {last_value}")

                return processed_df

        except Exception as dl_e:
            logging.warning(f"Download failed for {ticker}: {dl_e}")
            last_exception = dl_e
            # Ensure df is a valid DataFrame after exception
            if df is None:
                df = pd.DataFrame()

        # OPTIMIZED: Minimal delay before extended retry
        time.sleep(1.0)  # Reduced from 5.0 to 1.0 seconds

        # Check circuit breaker before extended retry
        if check_circuit_breaker():
            logging.error(f"CIRCUIT BREAKER: Skipping extended retry for {ticker}")
            return pd.DataFrame()

        # Step 3: If standard methods failed, try with extended retry logic
        logging.warning(f"Standard fetch methods failed for {ticker}, trying with extended retry logic...")
        # OPTIMIZED: Much faster retry logic with aggressive timeouts
        if is_vix3m:
            max_retries = 3  # Reduced from 5 to 3 for VIX3M
            retry_delay = 3  # Reduced from 10 to 3 seconds for VIX3M
        elif is_treasury: # Treasury rates (IRX/TNX) - more reasonable settings
            max_retries = 3  # Increased from 2 to 3 retries for treasury rates
            retry_delay = 5  # Increased from 2 to 5 seconds delay for treasury rates
        elif is_problematic: # Other problematic tickers (not VIX3M, treasury,)
            max_retries = 4  # Reduced from 7 to 4 for other problematic
            retry_delay = 5  # Reduced from 15 to 5 seconds for other problematic
        else: # Non-problematic tickers
            max_retries = 3  # Reduced from 5 to 3 for non-problematic
            retry_delay = 5  # Reduced from 15 to 5 seconds for non-problematic

        try:
            yf_ticker = yf.Ticker(ticker, session=active_session)

            # Add timeout protection for the extended retry
            method_timeout = 20  # Increased base timeout for extended retry
            if is_vix3m:
                method_timeout = 25  # Enhanced from 15s to 25s for VIX term structure reliability
            elif is_treasury:
                method_timeout = 20  # Increased from 15 to 20 seconds for treasury rates

            logging.info(f"Extended retry for {ticker} with timeout {method_timeout}s, max_retries {max_retries}, retry_delay {retry_delay}s")

            df = fetch_with_retry(yf_ticker, 'history', start=start, end=end, interval=interval,
                                auto_adjust=True, repair=False, max_retries=max_retries,
                                retry_delay=retry_delay, method_timeout=method_timeout)

            if df is not None and not df.empty:
                data_source = "extended_retry"
                fetch_success = True
                logging.info(f"SUCCESS: Extended retry successful for {ticker}: {len(df)} rows")
                processed_df = process_yf_data(df, ticker, min_periods_override=min_periods_override)

                # Validate critical tickers (VIX, IRX, TNX) have reasonable values
                if is_critical and not processed_df.empty:
                    col_name = f"close_{ticker.replace('^', '').replace('=F', '').upper()}"
                    if col_name in processed_df.columns:
                        last_value = processed_df[col_name].iloc[-1] if len(processed_df) > 0 else None
                        if last_value is not None:
                            logging.info(f"VALIDATION: {ticker} last value: {last_value}")

                return processed_df
            else:
                logging.warning(f"Extended retry returned empty data for {ticker}")

        except Exception as retry_e:
            logging.error(f"Extended retry failed for {ticker}: {retry_e}")
            last_exception = retry_e

        # OPTIMIZED: Minimal delay before next attempt
        time.sleep(1.0)  # Reduced from 3.0 to 1.0 seconds

        # Check circuit breaker before alternative approaches
        if check_circuit_breaker():
            logging.error(f"CIRCUIT BREAKER: Skipping alternative approaches for {ticker}")
            return pd.DataFrame()

        # Step 4: If still no data, try alternative time periods or use fallback data
        logging.warning(f"All standard fetch attempts failed for {ticker}, trying different time periods or fallback...")

        # For problematic VIX term structure tickers, try a more efficient approach
        if is_problematic:
            # Check circuit breaker before starting problematic ticker handling
            if check_circuit_breaker():
                logging.error(f"CIRCUIT BREAKER: Skipping problematic ticker handling for {ticker}")
                return pd.DataFrame()

            logging.warning(f"Trying optimized approach for problematic ticker: {ticker}")
            # OPTIMIZED: Minimal delay for problematic tickers
            time.sleep(0.5)  # Further reduced from 1.0 to 0.5 seconds
            try:
                yf_ticker = yf.Ticker(ticker, session=active_session)
                # Try with shorter time periods - reduced to only 1 year for speed
                for years_back in [1]:  # Only try 1 year back for speed
                    # Check circuit breaker in the loop
                    if check_circuit_breaker():
                        logging.error(f"CIRCUIT BREAKER: Stopping shorter period attempts for {ticker}")
                        break

                    short_start = pd.Timestamp(end) - pd.Timedelta(days=365*years_back)
                    short_start_str = short_start.strftime('%Y-%m-%d')
                    logging.info(f"Trying {ticker} with shorter period: {short_start_str} to {end}")

                    # Use reasonable timeout for treasury rates
                    timeout_for_short = 12 if is_treasury else 8  # 12 seconds for treasury, 8 for others
                    df, error = SPY.fetch_with_timeout(yf_ticker.history, timeout_for_short,
                                               start=short_start_str, end=end, interval=interval,
                                               auto_adjust=True, repair=False)

                    if df is not None and not df.empty:
                        data_source = "shorter_period"
                        fetch_success = True
                        logging.info(f"SUCCESS: Retrieved {ticker} with shorter period: {len(df)} rows")
                        processed_df = process_yf_data(df, ticker, min_periods_override=min_periods_override)

                        # Validate critical tickers (VIX, IRX, TNX) have reasonable values
                        if is_critical and not processed_df.empty:
                            col_name = f"close_{ticker.replace('^', '').replace('=F', '').upper()}"
                            if col_name in processed_df.columns:
                                last_value = processed_df[col_name].iloc[-1] if len(processed_df) > 0 else None
                                if last_value is not None:
                                    logging.info(f"VALIDATION: {ticker} last value: {last_value}")

                        return processed_df

                    time.sleep(0.5)  # Further reduced from 1.0 to 0.5 seconds

                # If shorter periods didn't work, try a direct download with strict timeout
                logging.info(f"Trying direct download with strict timeout for {ticker}")
                # Disable repair for ALL tickers to ensure authentic, unmodified data
                # repair=True can cause data corruption (e.g., Treasury rates multiplied by 100)

                download_func = lambda: yf.download(
                    ticker,
                    start=start,
                    end=end,
                    interval=interval,
                    session=active_session,
                    progress=False,
                    auto_adjust=True,
                    prepost=False,
                    repair=False,  # Disabled for ALL tickers to prevent data corruption
                    threads=False
                )

                direct_df, direct_error = SPY.fetch_with_timeout(download_func, 15)  # OPTIMIZED: Reduced from 30 to 15 seconds
                if direct_df is not None and not direct_df.empty:
                    data_source = "direct_download"
                    fetch_success = True
                    logging.info(f"SUCCESS: Direct download successful for {ticker}: {len(direct_df)} rows")
                    processed_df = process_yf_data(direct_df, ticker, min_periods_override=min_periods_override)

                    # Validate critical tickers (VIX, IRX, TNX) have reasonable values
                    if is_critical and not processed_df.empty:
                        col_name = f"close_{ticker.replace('^', '').replace('=F', '').upper()}"
                        if col_name in processed_df.columns:
                            last_value = processed_df[col_name].iloc[-1] if len(processed_df) > 0 else None
                            if last_value is not None:
                                logging.info(f"VALIDATION: {ticker} last value: {last_value}")

                    return processed_df
            except Exception as alt_e:
                logging.error(f"Alternative approach failed for {ticker}: {alt_e}")
                last_exception = alt_e

        # If fallback didn't work or not applicable, try with different periods
        try:
            # Try with different periods as last resort - optimized list
            periods_to_try = ["1y"]  # Only use 1 year as requested  # Reduced list for faster attempts

            for period in periods_to_try:
                logging.info(f"Trying {ticker} with period={period}")
                yf_ticker = yf.Ticker(ticker, session=active_session)

                # OPTIMIZED: Use faster timeout for period-based fetching
                period_func = lambda: yf_ticker.history(period=period)
                period_df, period_error = SPY.fetch_with_timeout(period_func, 20)  # Increased from 10 to 20 seconds

                if period_df is not None and not period_df.empty:
                    data_source = f"period_{period}"
                    fetch_success = True
                    logging.info(f"SUCCESS: Last-resort fetch successful for {ticker} with period={period}: {len(period_df)} rows")
                    # If we got data for a different period, filter to our target date range if possible
                    if start and end:
                        try:
                            period_df = period_df.loc[start:end]
                        except Exception:
                            # If filtering fails, just use what we have
                            pass
                    processed_df = process_yf_data(period_df, ticker, min_periods_override=min_periods_override)

                    # Validate critical tickers (VIX, IRX, TNX) have reasonable values
                    if is_critical and not processed_df.empty:
                        col_name = f"close_{ticker.replace('^', '').replace('=F', '').upper()}"
                        if col_name in processed_df.columns:
                            last_value = processed_df[col_name].iloc[-1] if len(processed_df) > 0 else None
                            if last_value is not None:
                                logging.info(f"VALIDATION: {ticker} last value: {last_value}")

                    return processed_df
                time.sleep(0.5)  # OPTIMIZED: Reduced from 2.0 to 0.5 seconds
        except Exception as alt_e:
            logging.error(f"Last-resort fetch also failed for {ticker}: {alt_e}")
            last_exception = alt_e

        # Check final circuit breaker before giving up
        elapsed_total = time.time() - start_time
        if elapsed_total > max_total_time:
            logging.error(f"CIRCUIT BREAKER FINAL: Total time {elapsed_total:.1f}s exceeded limit {max_total_time}s for {ticker}")

        # If we reach here, all attempts have failed
        error_msg = f"All fetch attempts failed for {ticker}"
        if last_exception:
            error_msg += f": {last_exception}"

        # Special handling for critical tickers
        if is_critical:
            logging.error(f"CRITICAL FAIL: {error_msg}")
            logging.error(f"CRITICAL FAIL: Unable to fetch {ticker} data. This will affect option pricing accuracy.")

            # No synthetic data generation - fail explicitly when authentic data unavailable
            logging.error(f"FINAL FAIL: No authentic yfinance data available for {ticker}. Synthetic data generation disabled for 100% authentic data compliance.")
            logging.critical(f"STOPPING SCRIPT: Critical ticker {ticker} data unavailable. Cannot proceed without authentic data for all critical tickers.")

            # For critical tickers, we must stop the script to prevent training with incomplete data
            import sys
            logging.critical("SCRIPT TERMINATION: Exiting to prevent training with incomplete authentic data.")
            sys.exit(1)
        else:
            logging.error(f"FAIL: {error_msg}")

        return pd.DataFrame()
    except Exception as e:
        if is_critical:
            logging.error(f"CRITICAL FAIL: Unexpected error in fetch_historical_data_yf for {ticker}: {e}", exc_info=True)
            logging.error(f"CRITICAL FAIL: Unable to fetch {ticker} data. This will affect option pricing accuracy.")
        else:
            logging.error(f"FAIL: Unexpected error in fetch_historical_data_yf for {ticker}: {e}", exc_info=True)
        return pd.DataFrame()
    finally:
        # Final status report
        if is_critical:
            if fetch_success:
                logging.info(f"FINAL STATUS: Successfully fetched {ticker} data from {data_source}")
            else:
                logging.error(f"FAILED FINAL STATUS: Failed to fetch {ticker} data. Option pricing will be affected.")
        elif is_spy and not fetch_success:
            logging.error(f"FAILED FINAL STATUS: Failed to fetch {ticker} data. This is the underlying asset and is critical.")
        elif not fetch_success:
            logging.warning(f"WARNING FINAL STATUS: Failed to fetch {ticker} data.")

def safe_reindex_with_validation(source_df: pd.DataFrame, target_index: pd.DatetimeIndex,
                                ticker: str, min_overlap_pct: float = 0.8) -> pd.DataFrame:
    """
    Safely reindex DataFrame with comprehensive validation to prevent stale data propagation.

    Args:
        source_df: DataFrame to reindex
        target_index: Target index to reindex to
        ticker: Ticker symbol for logging
        min_overlap_pct: Minimum required overlap percentage

    Returns:
        Reindexed DataFrame

    Raises:
        ValueError: If reindexing would corrupt data
    """
    # Check overlap percentage
    overlap = source_df.index.intersection(target_index)
    overlap_pct = len(overlap) / len(target_index) if len(target_index) > 0 else 0

    logging.info(f"Index overlap for {ticker}: {overlap_pct:.1%} ({len(overlap)}/{len(target_index)} dates)")

    if overlap_pct < min_overlap_pct:
        error_msg = f"Data validation failed: {ticker} has insufficient index overlap ({overlap_pct:.1%} < {min_overlap_pct:.1%}). This would require excessive forward-filling of stale data."
        logging.error(error_msg)
        raise ValueError(error_msg)

    # Store original statistics for all numeric columns
    original_stats = {}
    for col in source_df.columns:
        if source_df[col].dtype in ['float64', 'float32', 'int64', 'int32']:
            col_data = source_df[col].dropna()
            if len(col_data) > 0:
                original_stats[col] = {
                    'min': col_data.min(),
                    'max': col_data.max(),
                    'median': col_data.median(),
                    'non_zero_count': (col_data != 0).sum(),
                    'total_count': len(col_data)
                }

    # Perform reindexing with forward fill
    reindexed_df = source_df.reindex(target_index, method='ffill')

    # Validate reindexed data integrity
    for col, stats in original_stats.items():
        if col in reindexed_df.columns:
            new_data = reindexed_df[col].dropna()
            if len(new_data) == 0:
                error_msg = f"AUTHENTIC DATA VALIDATION FAILED: Reindexing {ticker} resulted in all NaN values for {col}"
                logging.error(error_msg)
                raise ValueError(error_msg)

            new_min = new_data.min()
            new_max = new_data.max()
            zero_count = (reindexed_df[col] == 0).sum()

            # Check for introduced zeros (critical for financial data)
            if zero_count > 0 and stats['non_zero_count'] == stats['total_count']:
                error_msg = f"AUTHENTIC DATA VALIDATION FAILED: Reindexing {ticker} introduced {zero_count} zero values in {col}. Original data had no zeros."
                logging.error(error_msg)
                raise ValueError(error_msg)

            # Check for dramatic range changes (indicating corruption)
            range_factor = 1.1  # Allow 10% expansion/contraction

            # Calculate lower bound for min: if original min is positive, allow it to shrink by 1/range_factor.
            # If original min is negative, allow it to become more negative by range_factor.
            lower_bound_min = stats['min'] * (1 / range_factor) if stats['min'] > 0 else stats['min'] * range_factor
            # Calculate upper bound for max: if original max is positive, allow it to grow by range_factor.
            # If original max is negative, allow it to become less negative by 1/range_factor.
            upper_bound_max = stats['max'] * range_factor if stats['max'] > 0 else stats['max'] * (1 / range_factor)

            if (new_min < lower_bound_min or new_max > upper_bound_max):
                error_msg = f"AUTHENTIC DATA VALIDATION FAILED: Reindexing {ticker} corrupted data range for {col}. Original: [{stats['min']:.4f}, {stats['max']:.4f}], Reindexed: [{new_min:.4f}, {new_max:.4f}]"
                logging.error(error_msg)
                raise ValueError(error_msg)

            # Check for excessive forward-filling
            forward_filled_count = len(reindexed_df) - len(overlap)
            if forward_filled_count > len(target_index) * 0.2:  # More than 20% forward-filled
                logging.warning(f"Extensive forward-filling for {ticker} {col}: {forward_filled_count} out of {len(target_index)} dates ({forward_filled_count/len(target_index):.1%})")

    logging.info(f"Successfully reindexed {ticker} with validation passed")
    return reindexed_df

def create_combined_features(data_dict: Dict[str, pd.DataFrame]) -> pd.DataFrame:
    """
    Create combined features from market data with robust validation and error handling.

    This function merges SPY data with VIX, Treasury, and other market indicators while
    maintaining strict data integrity and preventing stale data propagation.
    """
    # Initialize base DataFrame from SPY data
    spy_df = data_dict.get(UNDERLYING_TICKER)
    if spy_df is None or spy_df.empty:
        logging.error("SPY DataFrame missing/empty.")
        return pd.DataFrame()

    # Collect all unique dates from MARKET dataframes to create a master index
    all_dates = pd.DatetimeIndex([])
    market_tickers = list(data_dict.keys())

    for ticker, df in data_dict.items():

        if not df.empty:
            # Ensure index is DatetimeIndex and convert to UTC
            if not isinstance(df.index, pd.DatetimeIndex):
                df.index = pd.to_datetime(df.index, utc=True)
            elif df.index.tzinfo is None:
                df.index = df.index.tz_localize('UTC')
            else:
                df.index = df.index.tz_convert('UTC')

            # Extract just the date part for consistent merging
            df.index = df.index.normalize()
            all_dates = all_dates.union(df.index)

    if all_dates.empty:
        logging.error("No valid dates found across all DataFrames.")
        return pd.DataFrame()

    master_index = pd.to_datetime(all_dates).sort_values()
    logging.info(f"Created master index with {len(master_index)} unique dates from {master_index.min().strftime('%Y-%m-%d')} to {master_index.max().strftime('%Y-%m-%d')}")

    # Reindex all dataframes to the master index before combining
    reindexed_data_dict = {}
    for ticker, df in data_dict.items():
        logging.info(f"Processing ticker {ticker} for reindexing: shape={df.shape if not df.empty else 'EMPTY'}, columns={list(df.columns) if not df.empty else 'NONE'}")
        if not df.empty:
            try:
                # Ensure df index is normalized before reindexing
                df.index = df.index.normalize()

                logging.info(f"About to reindex {ticker} with master_index length {len(master_index)}")
                reindexed_df = safe_reindex_with_validation(df, master_index, ticker)

                reindexed_data_dict[ticker] = reindexed_df
                logging.info(f"Successfully reindexed {ticker} to master index. New shape: {reindexed_df.shape}")
            except Exception as e:
                logging.error(f"Error reindexing {ticker}: {e}. Skipping this ticker for combined features.")
                # For critical tickers, re-raise the exception
                if ticker in [VIX_TICKER, VIX3M_TICKER, IRX_TICKER, TNX_TICKER, UNDERLYING_TICKER]:
                    logging.error(f"CRITICAL TICKER REINDEXING FAILED: {ticker} - {e}")
                    raise
                continue
        else:
            logging.warning(f"Skipping reindexing for empty DataFrame: {ticker}")

    # Initialize combined_df with the underlying ticker's data
    spy_df = reindexed_data_dict.get(UNDERLYING_TICKER)
    if spy_df is None or spy_df.empty:
        logging.error("SPY DataFrame missing/empty after reindexing. Cannot create combined features.")
        return pd.DataFrame()

    combined_df = spy_df.copy()
    logging.info(f"Starting combine features with SPY shape: {combined_df.shape}")
    logging.info(f"Available tickers in reindexed_data_dict: {list(reindexed_data_dict.keys())}")

    # Define tickers to merge with validation (excluding SPY as it's already the base)
    tickers_to_merge = [t for t in [VIX_TICKER, VIX3M_TICKER, IRX_TICKER, TNX_TICKER] if t != UNDERLYING_TICKER]



    for ticker in tickers_to_merge:
        df = reindexed_data_dict.get(ticker)
        if df is None or df.empty:
            logging.warning(f"{ticker} DataFrame missing/empty after reindexing. Skipping merge.")
            continue

        try:
            clean_ticker = ticker.replace('^', '').replace('=F', '').replace('/', '_').upper()
            logging.info(f"Merging {ticker} (Shape: {df.shape}, Columns: {df.columns.tolist()})")

            # Prepare column mapping for consistent naming
            renamed_df = df.copy()
            column_map = {}

            # Check if columns are already renamed (from process_yf_data)
            already_renamed = all(col.startswith(('open_', 'high_', 'low_', 'close_')) for col in df.columns[:4])

            if already_renamed:
                logging.info(f"Columns for {ticker} already appear to be renamed. Using as-is.")
                for col in df.columns:
                    # Only map columns that are relevant for merging (close and derived features only, excluding returns)
                    if any(prefix in col for prefix in ['close_', 'atr_']):
                        column_map[col] = col
            else:
                # Map original Yahoo Finance column names
                for col in df.columns:
                    col_lower = col.lower()
                    if ticker in [VIX_TICKER, VIX3M_TICKER, TNX_TICKER, IRX_TICKER]:
                        # For market indices, only keep close column (removing OHLV features)
                        if col_lower == 'close':
                            new_col = f"{col_lower}_{clean_ticker}"
                            column_map[col] = new_col

                    else: # For other tickers, include relevant columns (removed volume, realized_vol_20d, and returns)
                        if col_lower in ['open', 'high', 'low', 'close', 'atr']:
                            new_col = f"{col_lower}_{clean_ticker}"
                            column_map[col] = new_col
                        elif col.endswith(f'_{clean_ticker}'): # Catch already suffixed columns
                            column_map[col] = col

            if not column_map:
                logging.warning(f"No columns were mapped for {ticker}! Original columns: {df.columns.tolist()}")
                continue

            # Apply column renaming
            # Ensure we only select columns that exist in the DataFrame
            cols_to_select = [c for c in column_map.keys() if c in df.columns]
            if not cols_to_select:
                logging.warning(f"No mappable columns found in {ticker} after filtering. Skipping merge.")
                continue

            renamed_df = df[cols_to_select].rename(columns=column_map)

            # Log index information for debugging
            logging.info(f"SPY index range: {combined_df.index.min().strftime('%Y-%m-%d')} to {combined_df.index.max().strftime('%Y-%m-%d')}, count: {len(combined_df.index)}")
            logging.info(f"{ticker} index range: {renamed_df.index.min().strftime('%Y-%m-%d')} to {renamed_df.index.max().strftime('%Y-%m-%d')}, count: {len(renamed_df.index)}")

            # Use join for index-based combination (indices are now aligned)
            before_merge_shape = combined_df.shape
            combined_df = combined_df.join(renamed_df, how='left')
            after_merge_shape = combined_df.shape

            # Verify the merge actually added columns
            if after_merge_shape[1] <= before_merge_shape[1]:
                logging.warning(f"Join for {ticker} didn't add any columns! Before: {before_merge_shape}, After: {after_merge_shape}")

            # Verify the data is still intact after joining
            clean_ticker_col_name = f'close_{clean_ticker}'

            if clean_ticker_col_name in combined_df.columns and not combined_df[clean_ticker_col_name].empty:
                sample_values = combined_df[clean_ticker_col_name].head(3).tolist()
                last_value = combined_df[clean_ticker_col_name].iloc[-1]
                logging.info(f"{ticker} {clean_ticker_col_name} sample after join (first 3): {sample_values}, last: {last_value}")

                # Check for NaN values in the joined data (should be minimal after reindexing)
                nan_count = combined_df[clean_ticker_col_name].isna().sum()
                if nan_count > 0:
                    logging.warning(f"{ticker} {clean_ticker_col_name} has {nan_count} NaN values after join. Attempting to fill.")
                    # Fill remaining NaNs with ffill/bfill. If still NaNs, it's a critical data issue.
                    combined_df[clean_ticker_col_name] = combined_df[clean_ticker_col_name].fillna(method='ffill').fillna(method='bfill')
                    remaining_nans = combined_df[clean_ticker_col_name].isna().sum()
                    if remaining_nans > 0:
                        error_msg = f"AUTHENTIC DATA VALIDATION FAILED: Critical ticker {ticker} {clean_ticker_col_name} still has {remaining_nans} NaN values after forward/backward fill. Data integrity compromised."
                        logging.error(error_msg)
                        raise ValueError(error_msg)
                    else:
                        logging.info(f"Successfully filled {nan_count} NaN values in {clean_ticker_col_name} after join.")
            else:
                logging.warning(f"Column {clean_ticker_col_name} not found or empty in combined_df after join for {ticker}.")

            logging.info(f"After joining {ticker}, combined_df columns: {combined_df.columns.tolist()}")

        except Exception as e:
            logging.error(f"Error processing {ticker} data during merge: {e}", exc_info=True)
            # For critical tickers, re-raise the exception
            if ticker in [VIX_TICKER, VIX3M_TICKER, IRX_TICKER, TNX_TICKER]:
                logging.error(f"Critical ticker {ticker} processing failed. Cannot proceed.")
                raise
            else:
                logging.warning(f"Non-critical ticker {ticker} processing failed. Continuing without this data.")
                continue
    logging.info(f"Shape after merging all tickers: {combined_df.shape}")

    # Convert all columns to float64 and handle any remaining missing values
    for col in combined_df.columns:
        combined_df[col] = pd.to_numeric(combined_df[col], errors='coerce')

    # Comprehensive NaN validation and filling strategy
    if combined_df.isnull().values.any():
        nan_cols = combined_df.columns[combined_df.isnull().any()].tolist()
        logging.warning(f"NaN values found in columns: {nan_cols}. Applying strict filling strategy.")

        # Apply forward fill then backward fill
        combined_df = combined_df.ffill().bfill()

        # Check for remaining NaNs after standard filling
        if combined_df.isnull().values.any():
            remaining_nan_cols = combined_df.columns[combined_df.isnull().any()].tolist()

            # Define critical financial columns that must never have NaNs
            critical_financial_columns = [
                f'close_{ticker.replace("^", "").upper()}' for ticker in [VIX_TICKER, VIX3M_TICKER, IRX_TICKER, TNX_TICKER, UNDERLYING_TICKER]
            ]

            for col in remaining_nan_cols:
                nan_count = combined_df[col].isna().sum()
                if col in critical_financial_columns:
                    error_msg = f"AUTHENTIC DATA VALIDATION FAILED: Critical financial column '{col}' contains {nan_count} NaN values after forward/backward fill. This indicates corrupted authentic market data."
                    logging.error(error_msg)
                    raise ValueError(error_msg)
                else:
                    # For non-critical columns, use appropriate defaults
                    if 'ratio' in col.lower():
                        combined_df[col].fillna(1.0, inplace=True)
                    elif 'spread' in col.lower() or 'slope' in col.lower() or 'diff' in col.lower():
                        combined_df[col].fillna(0.0, inplace=True)
                    else:
                        combined_df[col].fillna(0.0, inplace=True)
                    logging.info(f"DIAGNOSIS: Filled {nan_count} NaN values in non-critical column '{col}' with 0.0")

    # Create derived features with comprehensive validation
    vix_clean, vix3m_clean, irx_clean, tnx_clean = (t.replace('^','').upper() for t in [VIX_TICKER, VIX3M_TICKER, IRX_TICKER, TNX_TICKER])
    vix_col, v3m_col = (f'close_{c}' for c in [vix_clean, vix3m_clean])
    irx_col = f'close_{irx_clean}'
    tnx_col = f'close_{tnx_clean}'

    # Validate that all required base columns exist
    required_base_cols = [vix_col, v3m_col, irx_col, tnx_col]
    missing_base_cols = [col for col in required_base_cols if col not in combined_df.columns]
    if missing_base_cols:
        error_msg = f"AUTHENTIC DATA VALIDATION FAILED: Missing required base columns for feature creation: {missing_base_cols}"
        logging.error(error_msg)
        raise ValueError(error_msg)

    # Create IV and yield curve features with validation
    _created_features = create_iv_and_yield_features(combined_df, vix_col, v3m_col, irx_col, tnx_col)

    # Verify all required features were created (removed unwanted features)
    required_features = ['vix3m_to_vix_ratio', 'yield_curve_spread']
    missing_features = [feat for feat in required_features if feat not in combined_df.columns]
    if missing_features:
        error_msg = f"AUTHENTIC DATA VALIDATION FAILED: Failed to create required features: {missing_features}"
        logging.error(error_msg)
        raise ValueError(error_msg)

    # Apply volume normalization for SPY before final validation
    volume_col_name = 'volume_SPY'
    if volume_col_name in combined_df.columns:
        logging.info(f"Applying normalization to {volume_col_name} feature...")

        # Get original volume data
        original_volume = combined_df[volume_col_name].copy()

        # Apply log transformation to handle wide range of volume values
        # Add 1 to avoid log(0) issues, then apply log transformation
        log_volume = np.log1p(original_volume)  # log1p(x) = log(1 + x)

        # Apply z-score normalization to the log-transformed volume
        volume_mean = log_volume.mean()
        volume_std = log_volume.std()

        if volume_std > 0:
            normalized_volume = (log_volume - volume_mean) / volume_std
            combined_df[volume_col_name] = normalized_volume

            logging.info(f"Volume normalization applied:")
            logging.info(f"  Original volume range: {original_volume.min():.0f} to {original_volume.max():.0f}")
            logging.info(f"  Log-transformed range: {log_volume.min():.4f} to {log_volume.max():.4f}")
            logging.info(f"  Normalized range: {normalized_volume.min():.4f} to {normalized_volume.max():.4f}")
            logging.info(f"  Normalized mean: {normalized_volume.mean():.4f}, std: {normalized_volume.std():.4f}")
        else:
            logging.warning(f"Volume standard deviation is zero, keeping original values")
    else:
        logging.info(f"Volume column {volume_col_name} not found, skipping volume normalization")

    # Final validation and cleanup
    logging.info("Performing final validation and cleanup...")

    # Validate that all required market data columns are present (updated for removed features)
    expected_cols = []
    spy_features = ['open', 'high', 'low', 'close', 'volume', 'atr', 'rsi', 'wr', 'sma', 'price_change_3m']  # Added Williams %R indicator
    index_features = ['close']  # Removed open, high, low for market indices

    for ticker in tickers_to_merge:
        clean_t = ticker.replace('^','').upper()
        if ticker in ['^VIX', '^VIX3M', '^TNX', '^IRX']:
            features = index_features

        else:
            features = spy_features

        for feat in features:
            col = f"{feat}_{clean_t}"
            expected_cols.append(col)

    # Check for missing columns
    missing_cols = [col for col in expected_cols if col not in combined_df.columns]
    if missing_cols:
        error_msg = f"AUTHENTIC DATA VALIDATION FAILED: Missing required market data columns: {missing_cols}"
        logging.error(error_msg)
        raise ValueError(error_msg)

    # Perform final data integrity checks
    if combined_df.isnull().values.any():
        nan_cols = combined_df.columns[combined_df.isnull().any()].tolist()
        error_msg = f"AUTHENTIC DATA VALIDATION FAILED: Final DataFrame still contains NaN values in columns: {nan_cols}"
        logging.error(error_msg)
        raise ValueError(error_msg)

    # Verify feature distributions
    verify_feature_distributions(combined_df)

    logging.info(f"Final combined features shape: {combined_df.shape}")
    logging.info(f"Final columns: {combined_df.columns.tolist()}")

    return combined_df

def create_iv_and_yield_features(df: pd.DataFrame, vix_col: str, v3m_col: str, irx_col: str, tnx_col: str) -> Dict[str, bool]:
    """
    Create IV ratios and yield curve features with comprehensive validation.

    Returns:
        Dictionary indicating which features were successfully created
    """
    created_features = {}

    # Calculate VIX term structure ratio
    if vix_col in df.columns and v3m_col in df.columns:
        # Validate data ranges before calculation
        vix_data = df[vix_col].dropna()
        v3m_data = df[v3m_col].dropna()

        if len(vix_data) == 0 or len(v3m_data) == 0:
            error_msg = f"AUTHENTIC DATA VALIDATION FAILED: VIX or VIX3M data is empty"
            logging.error(error_msg)
            raise ValueError(error_msg)

        # Check for reasonable VIX values (should be > 0 and < 200)
        if vix_data.min() <= 0 or vix_data.max() > 200 or v3m_data.min() <= 0 or v3m_data.max() > 200:
            error_msg = f"AUTHENTIC DATA VALIDATION FAILED: VIX data outside reasonable range. VIX: [{vix_data.min():.2f}, {vix_data.max():.2f}], VIX3M: [{v3m_data.min():.2f}, {v3m_data.max():.2f}]"
            logging.error(error_msg)
            raise ValueError(error_msg)

        # Calculate ratio with zero protection
        ratio = np.where(df[vix_col] > EPSILON, df[v3m_col] / df[vix_col], 1.0)
        df['vix3m_to_vix_ratio'] = ratio

        # Validate ratio distribution
        ratio_data = pd.Series(ratio).dropna()
        if len(ratio_data) > 0:
            ratio_median = ratio_data.median()
            if ratio_median < 0.5 or ratio_median > 2.0:
                logging.warning(f"VIX3M/VIX ratio has unusual median: {ratio_median:.4f} (expected 0.8-1.2)")

        # Removed vix3m_over_vix_ratio_diff feature as requested

        created_features['vix3m_to_vix_ratio'] = True
        logging.info(f"Created VIX term structure features")
    else:
        error_msg = f"AUTHENTIC DATA VALIDATION FAILED: Cannot create VIX features. Missing columns: VIX={vix_col in df.columns}, VIX3M={v3m_col in df.columns}"
        logging.error(error_msg)
        raise ValueError(error_msg)

    # Calculate Treasury yield features
    if irx_col in df.columns and tnx_col in df.columns:
        # Validate Treasury data
        irx_data = df[irx_col].dropna()
        tnx_data = df[tnx_col].dropna()

        if len(irx_data) == 0 or len(tnx_data) == 0:
            error_msg = f"AUTHENTIC DATA VALIDATION FAILED: Treasury data is empty"
            logging.error(error_msg)
            raise ValueError(error_msg)

        # Check for reasonable Treasury ranges (allow negative rates during COVID)
        if irx_data.min() < -0.005 or irx_data.max() > 0.25:  # -0.5% to 25%
            logging.warning(f"IRX data outside typical range: [{irx_data.min():.4f}, {irx_data.max():.4f}]")
        if tnx_data.min() < 0 or tnx_data.max() > 0.25:  # 0% to 25%
            logging.warning(f"TNX data outside typical range: [{tnx_data.min():.4f}, {tnx_data.max():.4f}]")

        # Calculate yield spread (can be negative during inversions) - keeping this feature
        df['yield_curve_spread'] = df[tnx_col] - df[irx_col]

        # Removed tnx_to_irx_ratio and yield_curve_slope features as requested

        # Validate yield curve features
        spread_data = df['yield_curve_spread'].dropna()
        if len(spread_data) > 0:
            spread_median = spread_data.median()
            if spread_median < -0.05 or spread_median > 0.1:  # -5% to 10%
                logging.warning(f"Yield curve spread has unusual median: {spread_median:.4f}")

        created_features['yield_curve_spread'] = True
        logging.info(f"Created Treasury yield curve features")
    else:
        error_msg = f"AUTHENTIC DATA VALIDATION FAILED: Cannot create Treasury features. Missing columns: IRX={irx_col in df.columns}, TNX={tnx_col in df.columns}"
        logging.error(error_msg)
        raise ValueError(error_msg)

    return created_features

def verify_feature_distributions(df: pd.DataFrame) -> bool:
    """
    Verify that created features have sensible distributions.

    Args:
        df: DataFrame with features to verify

    Returns:
        True if all distributions are reasonable

    Raises:
        ValueError: If any feature has unreasonable distribution
    """
    distribution_checks = {
        'vix3m_to_vix_ratio': (0.7, 1.3, "VIX term structure ratio"),
        'yield_curve_spread': (-0.02, 0.05, "Yield curve spread"),  # Can be negative during inversions
        # Removed unwanted features: yield_curve_slope, vix3m_over_vix_ratio_diff, tnx_to_irx_ratio
    }

    for feature, (min_val, max_val, description) in distribution_checks.items():
        if feature in df.columns:
            feature_data = df[feature].dropna()
            if len(feature_data) > 0:
                median_val = feature_data.median()
                q25 = feature_data.quantile(0.25)
                q75 = feature_data.quantile(0.75)
                outlier_pct = ((feature_data < min_val) | (feature_data > max_val)).mean()

                # Log distribution statistics
                logging.info(f"{description} ({feature}): median={median_val:.4f}, Q25={q25:.4f}, Q75={q75:.4f}, outliers={outlier_pct:.1%}")

                # Check for excessive outliers
                if outlier_pct > 0.1:  # More than 10% outliers
                    logging.warning(f"{description} has {outlier_pct:.1%} outliers outside [{min_val}, {max_val}]")

                # Check for unreasonable median values
                if median_val < min_val or median_val > max_val:
                    error_msg = f"AUTHENTIC DATA VALIDATION FAILED: {description} has unreasonable median value {median_val:.4f} (expected range: [{min_val}, {max_val}])"
                    logging.error(error_msg)
                    raise ValueError(error_msg)
            else:
                error_msg = f"AUTHENTIC DATA VALIDATION FAILED: {description} ({feature}) has no valid data"
                logging.error(error_msg)
                raise ValueError(error_msg)

    logging.info("Feature distribution verification completed successfully")
    return True

# --- TensorTrade Components ---
class OptionActionScheme(ActionScheme):
    registered_name = "option_bsh_categorical_v3.1"
    def __init__(self, cash_wallet: Wallet, underlying_instrument: Instrument, features_df: pd.DataFrame,
                 min_option_premium_per_share: float = None, confidence_threshold: float = None):
        super().__init__(); self._action_space = spaces.Discrete(ACTION_SPACE_SIZE)
        self.cash_wallet = cash_wallet; self.underlying_instrument = underlying_instrument; self.features_df = features_df
        self.held_option: Optional[Dict[str, Any]] = None; self.last_action_details: Optional[Dict[str, Any]] = None
        self._last_known_underlying_price = 0.0; self._last_known_risk_free_rate = RISK_FREE_RATE_FALLBACK

        # Configuration parameters (use provided values or defaults from global config)
        self.min_option_premium_per_share = min_option_premium_per_share if min_option_premium_per_share is not None else MIN_OPTION_PREMIUM_PER_SHARE
        self.confidence_threshold = confidence_threshold if confidence_threshold is not None else 0.0  # Default: no confidence filtering

        # Confidence tracking for high-confidence trading
        self.last_action_confidence = 0.0
        self.confidence_filtered_actions = 0  # Track how many actions were filtered due to low confidence

        # For P&L simulation
        self.simulated_cash = float(INITIAL_CASH)
        self.current_option_market_value = 0.0

        # Trade performance tracking for QuantStatsComparisonCallback
        self.total_profit = 0.0
        self.trade_count = 0
        self.win_count = 0

        logging.info(f"OptionActionScheme v3.1 initialized. Action space size: {self.action_space.n}. Simulated Cash: {self.simulated_cash}")
        logging.info(f"Configuration: min_option_premium_per_share=${self.min_option_premium_per_share:.4f}, confidence_threshold={self.confidence_threshold:.3f}")
    @property
    def action_space(self) -> spaces.Space: return self._action_space

    def set_model_for_confidence(self, model):
        """Set the PPO model reference for confidence extraction during training/evaluation."""
        self.model = model

    def get_action_confidence(self, observation, action):
        """Extract confidence score for a given action from the PPO model's action probabilities."""
        try:
            if not hasattr(self, 'model') or self.model is None:
                return 0.5  # Default confidence when model not available

            # Get action probabilities from the PPO policy
            # PPO uses a categorical distribution for discrete actions
            obs_tensor = self.model.policy.obs_to_tensor(observation.reshape(1, -1))[0]
            with self.model.policy.device:
                distribution = self.model.policy.get_distribution(obs_tensor)
                action_probs = distribution.distribution.probs.cpu().numpy().flatten()

            # Return the probability of the selected action as confidence
            if 0 <= action < len(action_probs):
                confidence = float(action_probs[action])
                logging.debug(f"Action {action} confidence: {confidence:.3f}")
                return confidence
            else:
                logging.warning(f"Invalid action {action} for confidence extraction")
                return 0.0

        except Exception as e:
            logging.warning(f"Could not extract action confidence: {e}")
            return 0.5  # Default confidence on error
    def perform(self, portfolio: 'Portfolio', action: int) -> List['Order']:
        current_step = getattr(portfolio.clock, 'step', 'unknown')
        logging.debug(f"Step {current_step}: ActionScheme.perform action {action}")

        # OPTIMIZED: Reduce logging frequency to prevent KeyboardInterrupt during training
        verbose_logging = (current_step % 100 == 0) or (current_step < 5)

        if verbose_logging:
            logging.info(f"ACTIONSCHEME_PERFORM_START: Step={current_step}, Action={action}, "
                        f"SimCash={self.simulated_cash:.2f}, OptVal={self.current_option_market_value:.2f}, "
                        f"NetWorth={self.get_current_portfolio_value():.2f}, "
                        f"ActionScheme_ID={id(self)}")

        try:
            # Execute the trading logic first
            orders = self.get_orders(action=action, portfolio=portfolio)

            if verbose_logging:
                logging.info(f"ACTIONSCHEME_PERFORM_AFTER_ORDERS: Step={current_step}, "
                            f"SimCash={self.simulated_cash:.2f}, OptVal={self.current_option_market_value:.2f}, "
                            f"NetWorth={self.get_current_portfolio_value():.2f}, "
                            f"OrdersCount={len(orders)}")

            # CRITICAL FIX: Always update global state after each action, with error handling
            try:
                updated_net_worth = self.update_global_net_worth_history()
                if verbose_logging:
                    logging.info(f"ACTIONSCHEME_PERFORM_GLOBAL_UPDATE_SUCCESS: Step={current_step}, "
                                f"UpdatedNetWorth={updated_net_worth:.2f}, "
                                f"GlobalStateConceptualHistory_Length={len(global_state.conceptual_net_worth_history)}")
            except Exception as global_update_error:
                logging.error(f"ACTIONSCHEME_PERFORM_GLOBAL_UPDATE_FAILED: Step={current_step}, Error={global_update_error}")
                # Fallback: Try to update global state manually
                try:
                    current_value = self.get_current_portfolio_value()
                    global_state.add_conceptual_net_worth(current_value)
                    logging.warning(f"ACTIONSCHEME_PERFORM_GLOBAL_UPDATE_FALLBACK_SUCCESS: Step={current_step}, Value={current_value:.2f}")
                except Exception as fallback_error:
                    logging.error(f"ACTIONSCHEME_PERFORM_GLOBAL_UPDATE_FALLBACK_FAILED: Step={current_step}, Error={fallback_error}")

            if verbose_logging:
                logging.info(f"ACTIONSCHEME_PERFORM_END: Step={current_step}, "
                            f"FinalNetWorth={self.get_current_portfolio_value():.2f}, "
                            f"GlobalStateLength={len(global_state.conceptual_net_worth_history)}")

            return orders

        except Exception as perform_error:
            logging.error(f"ACTIONSCHEME_PERFORM_CRITICAL_ERROR: Step={current_step}, Error={perform_error}", exc_info=True)
            # Return empty orders list to prevent crash
            return []
    def get_orders(self, action: int, portfolio: 'Portfolio') -> List['Order']:
        self.last_action_details = {'action_taken': action, 'premium_paid': 0.0, 'payoff_received': 0.0, 'commission_paid': 0.0, 'option_closed': False, 'greeks_at_purchase': None}
        current_step = portfolio.clock.step

        # CONFIDENCE FILTERING: Check if action meets confidence threshold for new positions
        if action != 0 and self.confidence_threshold > 0.0 and hasattr(self, 'model') and self.model is not None:
            # Get current observation for confidence calculation
            try:
                # Access the current observation from the environment
                if hasattr(portfolio, 'env') and hasattr(portfolio.env, 'observer'):
                    current_obs = portfolio.env.observer.observe(portfolio.env)
                    action_confidence = self.get_action_confidence(current_obs, action)
                    self.last_action_confidence = action_confidence

                    if action_confidence < self.confidence_threshold:
                        logging.info(f"Step {current_step}: Action {action} confidence {action_confidence:.3f} below threshold {self.confidence_threshold:.3f}. Filtering to HOLD.")
                        action = 0  # Override to hold
                        self.confidence_filtered_actions += 1
                        self.last_action_details['action_taken'] = 0
                    else:
                        logging.debug(f"Step {current_step}: Action {action} confidence {action_confidence:.3f} meets threshold {self.confidence_threshold:.3f}. Proceeding.")
                else:
                    self.last_action_confidence = 0.5  # Default when observer not available
            except Exception as e:
                logging.warning(f"Could not apply confidence filtering: {e}")
                self.last_action_confidence = 0.5  # Default confidence
        else:
            self.last_action_confidence = 0.5  # Default for hold actions or when confidence filtering disabled

        current_risk_free_rate = self._get_robust_risk_free_rate(portfolio, current_step)
        current_underlying_price_for_step = self._get_robust_underlying_price(portfolio, current_step) # Cache for this step
        current_vix_for_step = self._get_robust_vix_value(portfolio, current_step) # Cache for this step

        if self.held_option: # Handle existing option
            is_expired = current_step >= self.held_option['expiry_step']
            is_closing_action = action != 0 # Any action other than hold implies closing the current option to open a new one or hold cash

            if is_expired or is_closing_action:
                reason = "expired" if is_expired else "closed by new action"
                logging.info(f"Step {current_step}: Option {self.held_option['type']} K={self.held_option['strike']:.2f} {reason}.")

                payoff_per_share = 0.0
                commission_on_close = COMMISSION_PER_CONTRACT # Standard commission for closing

                if is_expired:
                    if self.held_option['type'] == 'call': payoff_per_share = max(0, current_underlying_price_for_step - self.held_option['strike'])
                    else: payoff_per_share = max(0, self.held_option['strike'] - current_underlying_price_for_step)
                    logging.info(f"Expired option payoff (intrinsic): {payoff_per_share * OPTION_CONTRACT_UNIT:.2f}")
                else: # Closing early due to new action
                    dte_rem = self.held_option['expiry_step'] - current_step; ttm = max(EPSILON, dte_rem / 252.0)
                    logging.debug(f"BS EarlyClose: S={current_underlying_price_for_step:.2f},K={self.held_option['strike']:.2f},TTM={ttm:.4f},R={current_risk_free_rate:.4f},Sig={current_vix_for_step:.4f}")
                    if current_underlying_price_for_step > 0 and self.held_option['strike'] > 0 and ttm > 0 and current_vix_for_step > 0:
                        # Price it at mid, then apply spread for selling
                        option_mid_price = black_scholes(self.held_option['type'][0].lower(), current_underlying_price_for_step, self.held_option['strike'], ttm, current_risk_free_rate, current_vix_for_step)
                        payoff_per_share = option_mid_price * (1 - SPREAD_PERCENTAGE / 2.0) # Selling at bid
                    else: # Fallback to intrinsic if BS params invalid
                        if self.held_option['type'] == 'call': payoff_per_share = max(0, current_underlying_price_for_step - self.held_option['strike'])
                        else: payoff_per_share = max(0, self.held_option['strike'] - current_underlying_price_for_step)
                    logging.info(f"Closed early. Sell price/share (bid): {payoff_per_share:.2f}")

                total_payoff_received = payoff_per_share * OPTION_CONTRACT_UNIT
                self.simulated_cash += (total_payoff_received - commission_on_close)

                self.last_action_details['payoff_received'] = total_payoff_received
                self.last_action_details['commission_paid'] = commission_on_close
                self.last_action_details['option_closed'] = True

                # Update trade performance tracking
                self.trade_count += 1
                trade_profit = total_payoff_received - self.held_option.get('total_cost', 0.0)
                self.total_profit += trade_profit
                if trade_profit > 0:
                    self.win_count += 1

                logging.info(f"Simulated Cash after closing option: {self.simulated_cash:.2f} (+{total_payoff_received:.2f} payoff, -{commission_on_close:.2f} comm)")
                logging.info(f"Trade performance: Profit=${trade_profit:.2f}, Total trades={self.trade_count}, Win rate={self.win_count/self.trade_count:.2%}")
                self.held_option = None
                self.current_option_market_value = 0.0

        if action != 0 and self.held_option is None: # Buy new option (action is not hold, and no option is currently held)
            opt_idx = action - 1; opt_type_val = opt_idx // (N_STRIKES * N_EXPIRIES); strike_expiry_idx = opt_idx % (N_STRIKES * N_EXPIRIES)
            strike_cat_idx = strike_expiry_idx // N_EXPIRIES; expiry_cat_idx = strike_expiry_idx % N_EXPIRIES
            opt_type = 'call' if opt_type_val == 0 else 'put'
            strike_pct = STRIKE_CATEGORIES_PCT[strike_cat_idx]; dte_days = EXPIRY_CATEGORIES_DTE[expiry_cat_idx]

            K = current_underlying_price_for_step * (1 + strike_pct)
            TTM = max(EPSILON, dte_days / 252.0)

            logging.debug(f"BS Buy: S={current_underlying_price_for_step:.2f},K={K:.2f},TTM={TTM:.4f},R={current_risk_free_rate:.4f},Sig={current_vix_for_step:.4f}")
            prem_mid = 0.0
            if current_underlying_price_for_step > 0 and K > 0 and TTM > 0 and current_vix_for_step > 0:
                try:
                    prem_mid = black_scholes(opt_type[0].lower(), current_underlying_price_for_step, K, TTM, current_risk_free_rate, current_vix_for_step)
                    logging.debug(f"Black-Scholes calculation successful: premium=${prem_mid:.4f}")
                except Exception as bs_error:
                    logging.error(f"Black-Scholes calculation failed: {bs_error}. Using intrinsic value fallback.")
                    # Fallback to intrinsic value
                    if opt_type == 'call':
                        prem_mid = max(0, current_underlying_price_for_step - K)
                    else:
                        prem_mid = max(0, K - current_underlying_price_for_step)
                    logging.debug(f"Intrinsic value fallback: premium=${prem_mid:.4f}")
            else:
                logging.warning(f"Invalid Black-Scholes parameters: S={current_underlying_price_for_step}, K={K}, TTM={TTM}, R={current_risk_free_rate}, Sig={current_vix_for_step}")

            # ENHANCED: Better premium threshold handling with detailed logging
            if prem_mid >= self.min_option_premium_per_share:
                logging.debug(f"Premium check passed: ${prem_mid:.4f} ≥ ${self.min_option_premium_per_share:.4f} minimum threshold")
                ask_prem = prem_mid * (1 + SPREAD_PERCENTAGE / 2.0) # Buying at ask
                comm_on_open = COMMISSION_PER_CONTRACT
                total_cost = (ask_prem * OPTION_CONTRACT_UNIT) + comm_on_open

                if self.simulated_cash >= total_cost:
                    greeks = self._calculate_greeks(opt_type, current_underlying_price_for_step, K, TTM, current_risk_free_rate, current_vix_for_step)
                    self.held_option = {'type':opt_type,'strike':K,'dte_trading_days_initial':dte_days,'purchase_step':current_step,'expiry_step':current_step+dte_days,'premium_paid_per_share':ask_prem,'total_cost':total_cost,'commission_paid_on_open': comm_on_open, 'theoretical_premium_at_purchase':prem_mid,'underlying_price_at_purchase':current_underlying_price_for_step,'vix_at_purchase':current_vix_for_step*100,'rfr_at_purchase':current_risk_free_rate, **greeks}

                    self.simulated_cash -= total_cost
                    self.current_option_market_value = prem_mid * OPTION_CONTRACT_UNIT # Value at theoretical mid post-purchase

                    self.last_action_details['premium_paid'] = ask_prem * OPTION_CONTRACT_UNIT
                    self.last_action_details['commission_paid'] = comm_on_open
                    self.last_action_details['greeks_at_purchase'] = greeks
                    logging.info(f"Step {current_step}: Buying {opt_type} K={K:.2f} Exp@{self.held_option['expiry_step']} DTE={dte_days}. AskPrem:${ask_prem:.2f}. Comm:${comm_on_open:.2f}. TotalCost:${total_cost:.2f}")
                    logging.info(f"    Greeks: Delta={greeks['delta']:.4f}, Gamma={greeks['gamma']:.4f}, Theta={greeks['theta']:.4f}, Vega={greeks['vega']:.4f}")
                    logging.info(f"Simulated Cash after buying option: {self.simulated_cash:.2f}")
                else:
                    logging.warning(f"Step {current_step}: Insufficient cash ({self.simulated_cash:.2f}) to buy option (cost: {total_cost:.2f}). Holding cash. Option details: {opt_type} K={K:.2f} DTE={dte_days} premium=${ask_prem:.2f}")
                    self.last_action_details['action_taken'] = 0 # Override to hold
                    # Ensure market value is 0 if no option held
                    self.current_option_market_value = 0.0
            else:
                # ENHANCED: More detailed premium threshold warning with analysis
                moneyness = "ITM" if (opt_type == 'call' and current_underlying_price_for_step > K) or (opt_type == 'put' and current_underlying_price_for_step < K) else "OTM"
                strike_distance = abs(K - current_underlying_price_for_step) / current_underlying_price_for_step * 100

                logging.warning(f"Step {current_step}: Calculated premium (${prem_mid:.4f}) is below minimum threshold (${self.min_option_premium_per_share:.4f}). Buy skipped. Holding cash.")
                logging.warning(f"    Option details: {opt_type.upper()} K=${K:.2f} ({moneyness}, {strike_distance:.1f}% from spot), DTE={dte_days}, S=${current_underlying_price_for_step:.2f}")
                logging.warning(f"    BS Parameters: TTM={TTM:.4f}, R={current_risk_free_rate:.4f}, Vol={current_vix_for_step:.4f}")
                logging.warning(f"    Reason: Option likely too far OTM or too close to expiry for meaningful premium")

                self.last_action_details['action_taken'] = 0 # Override to hold
                self.current_option_market_value = 0.0
        elif action == 0:
            if not self.held_option:
                logging.info(f"Step {current_step}: Action 0 (Hold) - Agent explicitly chose to hold cash rather than enter a position. Cash available: ${self.simulated_cash:.2f}")
            else:
                logging.info(f"Step {current_step}: Action 0 (Hold) - Agent chose to continue holding existing {self.held_option['type']} option with strike ${self.held_option['strike']:.2f}, expiring in {self.held_option['expiry_step'] - current_step} days.")
            # If holding, update market value of existing option
            if self.held_option:
                dte_rem_val = self.held_option['expiry_step'] - current_step
                ttm_val = max(EPSILON, dte_rem_val / 252.0)
                if current_underlying_price_for_step > 0 and self.held_option['strike'] > 0 and ttm_val > 0 and current_vix_for_step > 0:
                    option_price_now = black_scholes(
                        self.held_option['type'][0].lower(),
                        current_underlying_price_for_step,
                        self.held_option['strike'],
                        ttm_val,
                        current_risk_free_rate,
                        current_vix_for_step
                    )
                    # Value at mid, then apply bid spread for conservative valuation
                    self.current_option_market_value = option_price_now * (1 - SPREAD_PERCENTAGE / 2.0) * OPTION_CONTRACT_UNIT
                    logging.debug(f"Hold: Updated held option market value to: {self.current_option_market_value:.2f}")
                else: # Fallback for BS params invalid (e.g., very near expiry or zero vol)
                    intrinsic_val = 0.0
                    if self.held_option['type'] == 'call': intrinsic_val = max(0, current_underlying_price_for_step - self.held_option['strike'])
                    else: intrinsic_val = max(0, self.held_option['strike'] - current_underlying_price_for_step)
                    self.current_option_market_value = intrinsic_val * OPTION_CONTRACT_UNIT
                    logging.debug(f"Hold (BS fallback): Updated held option market value to intrinsic: {self.current_option_market_value:.2f}")
            else: # Not holding an option
                self.current_option_market_value = 0.0
                logging.debug(f"Step {current_step}: Holding cash position. Current cash: ${self.simulated_cash:.2f}")
        elif action != 0 and self.held_option is not None:
            logging.warning(f"Step {current_step}: Tried buy action {action} while already holding an option. This implies closing current first. This case should have been handled by 'is_closing_action'. Review logic if this prints.")
            self.last_action_details['action_taken'] = 0 # Treat as hold if this unexpected state occurs

        # Final safety check for market value if no option is held
        if not self.held_option:
            self.current_option_market_value = 0.0

        return [] # Still returning no actual orders for TensorTrade exchange

    def _get_robust_underlying_price(self, portfolio: Portfolio, current_step: int) -> float:
        price = None; source = "unknown"

        # Try to get price from exchange
        try:
            quote = portfolio.exchange.quote_price(TradingPair(self.underlying_instrument, self.cash_wallet.instrument));
            if quote is not None:
                price = quote.as_float()
                source = "exchange"
                logging.info(f"SUCCESS: Got underlying price from exchange: {price:.2f}")
        except Exception as e:
            logging.debug(f"Could not get quote from exchange: {e}")
            quote = None

        # If exchange price not available, try features dataframe
        if price is None:
            underlying_col = f'close_{self.underlying_instrument.symbol}'
            if self.features_df is not None and underlying_col in self.features_df.columns and current_step < len(self.features_df):
                price = float(self.features_df[underlying_col].iloc[current_step])
                source = "features_df"
                logging.info(f"SUCCESS: Got underlying price from features_df: {price:.2f}")

        # If still no price, try cached value
        if price is None:
            price = self._last_known_underlying_price
            if price is not None and price > 0:
                source = "cache"
                logging.warning(f"PARTIAL FAIL: Using cached underlying price: {price:.2f}")

        # Fail explicitly if no authentic price available
        if price is None or price <= 0:
            error_msg = f"AUTHENTIC DATA VALIDATION FAILED: No valid underlying price available at step {current_step}"
            logging.error(error_msg)
            raise ValueError(error_msg)

        # Update cache and return
        self._last_known_underlying_price = price
        return price
    def _get_robust_vix_value(self, portfolio: Portfolio, current_step: int) -> float:
        # CRITICAL FIX: Standardized VIX validation range (consistent across all validation functions)
        min_vix_pct = 5.0; max_vix_pct = 100.0
        vix_val_pct = None; src = "unknown"; vix_clean = VIX_TICKER.replace("^","").upper(); vix_col = f'close_{vix_clean}'

        if self.features_df is not None and vix_col in self.features_df.columns and current_step < len(self.features_df):
            val = self.features_df[vix_col].iloc[current_step];
            if pd.notna(val): vix_val_pct = float(val); src = "features_df"
        if vix_val_pct is None and hasattr(portfolio,'env') and hasattr(portfolio.env,'feed'):
            try: feed_val_obj = portfolio.env.feed.inputs.get(vix_col);
            except AttributeError: feed_val_obj = None
            if feed_val_obj is not None and hasattr(feed_val_obj,'value') and feed_val_obj.value is not None: vix_val_pct = float(feed_val_obj.value); src = "env.feed"

        if vix_val_pct is not None:
            if min_vix_pct <= vix_val_pct <= max_vix_pct:
                sig = vix_val_pct/100.0
                logging.info(f"SUCCESS: Authentic VIX value obtained from {src}: {vix_val_pct:.2f}%, Sig:{sig:.4f}")
                return sig
            else:
                error_msg = f"AUTHENTIC DATA VALIDATION FAILED: VIX value {vix_val_pct:.2f}% from {src} outside valid range ({min_vix_pct}-{max_vix_pct}%)"
                logging.error(error_msg)
                raise ValueError(error_msg)
        else:
            error_msg = f"AUTHENTIC DATA VALIDATION FAILED: No VIX data available at step {current_step}"
            logging.error(error_msg)
            raise ValueError(error_msg)
    def _get_robust_risk_free_rate(self, portfolio: Portfolio, current_step: int) -> float:
        """
        Get risk-free rate from IRX data.

        STANDARDIZED ASSUMPTION: IRX data in features_df is already in canonical decimal format
        (e.g., 0.0525 for 5.25%) after processing by _validate_and_convert_treasury_data.
        """
        rfr = None; src = "unknown"; irx_clean = IRX_TICKER.replace("^","").upper(); irx_col = f'close_{irx_clean}'

        logging.debug(f"_get_robust_risk_free_rate called with current_step={current_step}, features_df shape: {self.features_df.shape if self.features_df is not None else 'None'}")

        # Try to get risk-free rate from features_df (should already be in decimal format)
        if self.features_df is not None and irx_col in self.features_df.columns and current_step < len(self.features_df):
            val = self.features_df[irx_col].iloc[current_step]
            logging.debug(f"IRX access at step {current_step}: val={val} (type: {type(val)}), irx_col='{irx_col}'")

            # STANDARDIZED: Assume data is already in decimal format from process_yf_data
            if pd.notna(val) and val >= 0:  # Allow zero and small positive values (COVID period)
                rfr = float(val)
                logging.info(f"SUCCESS: Retrieved IRX value (decimal format) from features_df at step {current_step}: {rfr:.6f} ({rfr*100:.4f}%)")
                src = "features_df"
            else:
                # Log the problematic value and fail with authentic data validation
                if pd.notna(val) and val < 0:
                    # Allow small negative rates during COVID but log them
                    if val >= -0.002:  # Allow up to -0.2% (COVID period saw negative rates)
                        rfr = float(val)
                        logging.warning(f"AUTHENTIC DATA: Negative IRX rate during COVID period at step {current_step}: {rfr:.6f} ({rfr*100:.4f}%)")
                        src = "features_df"
                    else:
                        error_msg = f"AUTHENTIC DATA VALIDATION FAILED: IRX value {val:.6f} at step {current_step} is too negative (< -0.2%)"
                        logging.error(error_msg)
                        raise ValueError(error_msg)
                else:
                    error_msg = f"AUTHENTIC DATA VALIDATION FAILED: IRX value at step {current_step} is NaN or invalid: {val}"
                    logging.error(error_msg)
                    raise ValueError(error_msg)
        else:
            error_msg = f"AUTHENTIC DATA VALIDATION FAILED: Cannot access IRX data. features_df is None: {self.features_df is None}, irx_col in columns: {irx_col in self.features_df.columns if self.features_df is not None else 'N/A'}, current_step < len: {current_step < len(self.features_df) if self.features_df is not None else 'N/A'}"
            logging.error(error_msg)
            raise ValueError(error_msg)

        # Validate authentic risk-free rate data (already in decimal format)
        if rfr is not None and src == "features_df":
            # Validate plausible range for risk-free rate in decimal format
            # During COVID-19, 3-month Treasury rates went as low as -0.105% and near-zero values
            min_rfr = -0.002  # Allow small negative rates (COVID period)
            max_rfr = 0.20    # 20% in decimal format

            # For extremely low rates (below 0.001%), use a small positive floor for options pricing
            if 0 <= rfr < 0.00001:  # Less than 0.001%
                original_rfr = rfr
                rfr = 0.00001  # Set floor at 0.001% for options pricing stability
                logging.warning(f"AUTHENTIC DATA: Extremely low risk-free rate {original_rfr:.6f} ({original_rfr*100:.4f}%) adjusted to minimum floor {rfr:.6f} ({rfr*100:.4f}%) for options pricing stability")

            if min_rfr <= rfr <= max_rfr:
                logging.info(f"SUCCESS: Authentic risk-free rate obtained from {src}: {rfr:.6f} ({rfr*100:.4f}%)")
                self._last_known_risk_free_rate = rfr
                return rfr
            else:
                error_msg = f"AUTHENTIC DATA VALIDATION FAILED: Risk-free rate {rfr:.6f} ({rfr*100:.4f}%) from {src} outside valid decimal range ({min_rfr:.6f}-{max_rfr:.6f})"
                logging.error(error_msg)
                raise ValueError(error_msg)
        else:
            error_msg = f"AUTHENTIC DATA VALIDATION FAILED: No valid risk-free rate data available at step {current_step}. IRX column: {irx_col}, features_df shape: {self.features_df.shape if self.features_df is not None else 'None'}, src: {src}"
            logging.error(error_msg)
            raise ValueError(error_msg)
    def _calculate_greeks(self, option_type: str, S: float, K: float, T: float, R: float, sigma: float) -> Dict[str, float]:
        greeks = {'delta':0.0,'gamma':0.0,'theta':0.0,'vega':0.0}; flag=option_type[0].lower()
        if not (S>0 and K>0 and T>EPSILON and sigma>0 and R>=0): logging.warning(f"Invalid params for Greeks: S,K,T,R,sig = {S},{K},{T},{R},{sigma}. Zeros returned."); return greeks
        try:
            greeks['delta'] = max(-0.999,min(0.999,bs_delta_analytic(flag,S,K,T,R,sigma)))
            greeks['gamma'] = max(0.0001,min(2.0,abs(bs_gamma_analytic(flag,S,K,T,R,sigma))))
            greeks['theta'] = max(-1.0,min(0.01,bs_theta_analytic(flag,S,K,T,R,sigma)/365.0))
            greeks['vega'] = max(0.0001,min(1.0,abs(bs_vega_analytic(flag,S,K,T,R,sigma)/100.0)))
        except Exception as e: logging.warning(f"Error calc Greeks: {e}. Defaults used."); greeks['delta']=0.5 if flag=='c' else -0.5; greeks['gamma']=0.01; greeks['theta']=-0.01; greeks['vega']=0.05
        return greeks
    def get_current_portfolio_value(self) -> float:
        # Returns the sum of simulated cash and current market value of the held option.
        # This is the official source of truth for the current portfolio value
        return self.simulated_cash + self.current_option_market_value

    def update_global_net_worth_history(self):
        """
        Update the official net worth history in global_state.

        CRITICAL FIX: During evaluation mode, DO NOT add entries here to prevent double entries.
        The evaluation loop will handle updates manually. During training, use step-based deduplication.

        Returns:
            float: Current portfolio value
        """
        current_value = self.get_current_portfolio_value()

        # CRITICAL FIX: During evaluation mode, DO NOT add entries to prevent double entries
        # The evaluation loop handles updates manually to ensure proper tracking
        if global_state.is_evaluation_mode:
            mode_str = "EVALUATION"
            logging.debug(f"{mode_str} MODE: Skipping automatic update (evaluation loop handles updates manually). Current value: {current_value:.2f}")
        else:
            # During training, use step-based deduplication to prevent multiple entries per step
            current_step = -1
            if hasattr(global_state, 'portfolio') and global_state.portfolio is not None:
                clock = getattr(global_state.portfolio, 'clock', None)
                if clock is not None:
                    current_step = getattr(clock, 'step', -1)

            # Fallback: use length of history as step indicator if clock unavailable
            if current_step == -1:
                current_step = len(global_state.conceptual_net_worth_history)

            # Only update if this is a new step to prevent multiple entries per step
            if current_step != global_state.last_history_update_step:
                global_state.add_conceptual_net_worth(current_value)
                global_state.last_history_update_step = current_step

                mode_str = "TRAINING"
                logging.debug(f"{mode_str} MODE: Added {current_value:.2f} to global state history at step {current_step} (length: {len(global_state.conceptual_net_worth_history)})")
            else:
                logging.debug(f"TRAINING MODE: Skipping duplicate update for step {current_step} (current value: {current_value:.2f})")

        return current_value

    def get_daily_return_pct(self, previous_portfolio_value: float) -> float:
        # Calculates the percentage return based on the change from the previous portfolio value.
        current_value = self.get_current_portfolio_value()
        if previous_portfolio_value == 0: # Avoid division by zero
            # If previous value was 0, and current is >0, it's infinite return (treat as 100% for practical scaling)
            # If current is also 0, return 0. If current is <0 from 0, it's problematic but treat as -100%.
            if current_value > 0: return 1.0
            elif current_value < 0: return -1.0
            return 0.0
        daily_return = (current_value - previous_portfolio_value) / previous_portfolio_value
        return daily_return

    def reset(self):
        super().reset()
        self.held_option=None
        self.last_action_details=None
        self._last_known_underlying_price=0.0
        self._last_known_risk_free_rate=RISK_FREE_RATE_FALLBACK

        # CRITICAL FIX: During evaluation mode, preserve portfolio state to continue from training
        if not global_state.is_evaluation_mode:
            # Training mode: Reset P&L simulation states to start fresh
            self.simulated_cash = float(INITIAL_CASH)
            self.current_option_market_value = 0.0

            # Reset trade performance tracking
            self.total_profit = 0.0
            self.trade_count = 0
            self.win_count = 0

            # Reset global state history to start fresh
            current_value = self.get_current_portfolio_value()
            global_state.conceptual_net_worth_history = [current_value]
            logging.debug(f"OptionActionScheme v3.1 reset (TRAINING MODE). Simulated cash: {self.simulated_cash:.2f}, Option Value: {self.current_option_market_value:.2f}")
            logging.debug(f"Global state history reset to: {global_state.conceptual_net_worth_history}")
        else:
            # Evaluation mode: PRESERVE portfolio state to continue from training
            # Get the last portfolio value from global state history to continue seamlessly
            if global_state.conceptual_net_worth_history:
                last_portfolio_value = global_state.conceptual_net_worth_history[-1]
                # Set simulated cash to the last portfolio value (assuming no held options at start of evaluation)
                self.simulated_cash = float(last_portfolio_value)
                self.current_option_market_value = 0.0
                logging.info(f"OptionActionScheme v3.1 reset (EVALUATION MODE). Portfolio state PRESERVED from training.")
                logging.info(f"Continuing with simulated cash: ${self.simulated_cash:.2f} (from last portfolio value)")
            else:
                # Fallback if no history available
                self.simulated_cash = float(INITIAL_CASH)
                self.current_option_market_value = 0.0
                logging.warning(f"OptionActionScheme v3.1 reset (EVALUATION MODE). No history available, using initial cash: ${self.simulated_cash:.2f}")

            # CRITICAL FIX: For evaluation mode, we need to accumulate trade statistics across episodes
            # Only reset trade counters at the START of evaluation, not between episodes
            if not hasattr(self, '_evaluation_started') or not self._evaluation_started:
                # First evaluation episode - reset counters
                self.total_profit = 0.0
                self.trade_count = 0
                self.win_count = 0
                self._evaluation_started = True
                logging.info(f"OptionActionScheme v3.1 reset (EVALUATION MODE). Trade performance tracking RESET for fresh evaluation metrics.")
            else:
                # Subsequent evaluation episodes - preserve counters to accumulate statistics
                logging.info(f"OptionActionScheme v3.1 reset (EVALUATION MODE). Trade performance tracking PRESERVED: Profit=${self.total_profit:.2f}, Trades={self.trade_count}, Wins={self.win_count}")

            # DO NOT reset global state history to preserve accumulated data
            logging.debug(f"Global state history PRESERVED during evaluation reset. Length: {len(global_state.conceptual_net_worth_history)}")
            if global_state.conceptual_net_worth_history:
                logging.debug(f"History range: {global_state.conceptual_net_worth_history[0]:.2f} -> {global_state.conceptual_net_worth_history[-1]:.2f}")
            else:
                logging.warning("Global state history is empty during evaluation reset - this should not happen")

class OptionRewardScheme(RewardScheme):
    registered_name = "option_pnl_v3.2"
    def __init__(self, window_size:int=REWARD_WINDOW, initial_cash:float=INITIAL_CASH,
                 sharpe_ratio_weight:float=SHARPE_RATIO_WEIGHT,
                 theta_penalty_weight:float=THETA_PENALTY_COEFF,
                 vega_penalty_weight:float=VEGA_PENALTY_COEFF,
                 profit_reward_multiplier:float=PROFIT_REWARD_MULTIPLIER,
                 consistent_profit_bonus:float=CONSISTENT_PROFIT_BONUS,
                 loss_penalty_multiplier:float=LOSS_PENALTY_MULTIPLIER,
                 drawdown_penalty_coeff:float=DRAWDOWN_PENALTY_COEFF,
                 delta_alignment_weight:float=0.25,  # New: weight for delta alignment reward
                 gamma_scalping_weight:float=0.15,   # New: weight for gamma scalping reward
                 max_reward_per_step:float=5.0):
        super().__init__(); self.window_size=window_size; self.initial_cash=float(initial_cash)
        self.sharpe_ratio_weight=sharpe_ratio_weight; self.theta_penalty_weight=theta_penalty_weight
        self.vega_penalty_weight=vega_penalty_weight; self.profit_reward_multiplier=profit_reward_multiplier
        self.consistent_profit_bonus=consistent_profit_bonus; self.loss_penalty_multiplier=loss_penalty_multiplier
        self.drawdown_penalty_coeff = drawdown_penalty_coeff # Store new coeff
        self.delta_alignment_weight = delta_alignment_weight  # Store delta alignment weight
        self.gamma_scalping_weight = gamma_scalping_weight    # Store gamma scalping weight
        self.max_reward_per_step = max_reward_per_step

        self._internal_cash:float=self.initial_cash; self._internal_option_value:float=0.0
        self._internal_net_worth_history:List[float]=[self.initial_cash]
        self._step_pnl_history:List[float]=[]; self._previous_conceptual_net_worth:float=self.initial_cash
        self.previous_net_worth_for_reward: float = float(initial_cash) # ADDED for new P&L
        self._step_count:int=0; self._profitable_steps_count:int=0
        self._held_option_details_for_reward:Optional[Dict[str,Any]]=None
        self._cumulative_pnl:float=0.0; self._cumulative_costs:float=0.0
        self._episode_peak_net_worth:float = self.initial_cash # New: Track peak NW per episode
        self._prev_underlying_price:float = 0.0  # Track previous underlying price for delta alignment
        self._realized_volatility:List[float] = []  # Track realized volatility for gamma scalping
        self._last_update_step = 0  # Initialize last update step tracking
        logging.info(f"OptionRewardScheme v3.2 (Enhanced with Delta/Gamma rewards, Drawdown Penalty: {self.drawdown_penalty_coeff}) initialized.")

    @property
    def cumulative_pnl(self) -> float: return self._cumulative_pnl
    @property
    def cumulative_costs(self) -> float: return self._cumulative_costs
    def reset(self):
        logging.debug("OptionRewardScheme (Enhanced) reset.")
        logging.info(f"RewardScheme RESET: id(self)={id(self)}") # ADDED LOGGING
        self._internal_cash=self.initial_cash
        self._internal_option_value=0.0; self._internal_net_worth_history=[self.initial_cash]
        self._step_pnl_history=[]; self._previous_conceptual_net_worth=self.initial_cash
        self.previous_net_worth_for_reward = float(self.initial_cash) # ADDED for new P&L
        self._step_count=0; self._profitable_steps_count=0
        self._held_option_details_for_reward=None
        self._cumulative_pnl=0.0; self._cumulative_costs=0.0
        self._episode_peak_net_worth = self.initial_cash # Reset peak NW
        self._last_update_step = 0  # Initialize last update step
        self._consecutive_profits = 0  # Initialize consecutive profits counter
        self._consecutive_losses = 0   # Initialize consecutive losses counter
        self._last_reported_step = 0   # Initialize last reported step
        self._prev_underlying_price = 0.0  # Reset previous underlying price
        self._realized_volatility = []  # Reset realized volatility

    def _get_performance_metrics(self, base_env, action_scheme):
        """Extract performance metrics from the environment."""
        try:
            # OPTIMIZED: Reduce logging frequency to prevent KeyboardInterrupt during training
            verbose_logging = (self._step_count % 100 == 0) or (self._step_count < 5)

            if verbose_logging:
                logging.info(f"REWARDSCHEME_GET_PERF_METRICS_START: RewardScheme_ID={id(self)}, "
                            f"ActionScheme_ID={id(action_scheme)}, "
                            f"ActionScheme_Type={type(action_scheme).__name__}")

            # Ensure action_scheme is the correct type
            if not isinstance(action_scheme, OptionActionScheme):
                if verbose_logging:
                    logging.error(f"RewardScheme: action_scheme is type {type(action_scheme)}, not OptionActionScheme. Using fallback P&L.")
                # Fallback to RewardScheme's own internal state if action_scheme is incorrect
                internal_cash_val = self._internal_cash if hasattr(self, '_internal_cash') else INITIAL_CASH
                internal_opt_val = self._internal_option_value if hasattr(self, '_internal_option_value') else 0.0

                if verbose_logging:
                    logging.info(f"REWARDSCHEME_GET_PERF_METRICS_FALLBACK: "
                                f"InternalCash={internal_cash_val:.2f}, InternalOptVal={internal_opt_val:.2f}")

                return {
                    'current_cash': internal_cash_val,
                    'current_option_value': internal_opt_val,
                    'current_net_worth': internal_cash_val + internal_opt_val,
                    'held_option': None # Cannot get held_option details reliably
                }

            # Extract values from the action_scheme (the official scorekeeper)
            current_cash = action_scheme.simulated_cash
            current_option_value = action_scheme.current_option_market_value
            current_net_worth = current_cash + current_option_value
            held_option_details = action_scheme.held_option # For Greeks penalties (can be None)

            if verbose_logging:
                logging.info(f"REWARDSCHEME_GET_PERF_METRICS_SUCCESS: "
                            f"ExtractedCash={current_cash:.2f}, ExtractedOptVal={current_option_value:.2f}, "
                            f"ExtractedNetWorth={current_net_worth:.2f}, "
                            f"HeldOption={held_option_details is not None}")

            return {
                'current_cash': current_cash,
                'current_option_value': current_option_value,
                'current_net_worth': current_net_worth,
                'held_option': held_option_details
            }
        except Exception as e:
            logging.error(f"RewardScheme Error: Exception in _get_performance_metrics(): {e}", exc_info=True)
            # Fallback to RewardScheme's own potentially stale values to avoid crashing
            internal_cash_val = self._internal_cash if hasattr(self, '_internal_cash') else INITIAL_CASH
            internal_opt_val = self._internal_option_value if hasattr(self, '_internal_option_value') else 0.0

            logging.info(f"REWARDSCHEME_GET_PERF_METRICS_EXCEPTION_FALLBACK: "
                        f"InternalCash={internal_cash_val:.2f}, InternalOptVal={internal_opt_val:.2f}")

            return {
                'current_cash': internal_cash_val,
                'current_option_value': internal_opt_val,
                'current_net_worth': internal_cash_val + internal_opt_val,
                'held_option': None
            }

    def _update_performance_history(self, performance):
        """Update internal performance history."""
        try:
            current_net_worth = performance['current_net_worth']
            current_cash = performance['current_cash']
            current_option_value = performance['current_option_value']

            # ADDED LOGGING
            logging.info(f"RewardScheme UPDATE_HISTORY start: id(self)={id(self)}, self._step_count={self._step_count}, self._last_update_step={self._last_update_step}, condition=({self._last_update_step} != {self._step_count})")

            # Initialize _last_update_step if not present
            if not hasattr(self, '_last_update_step'):
                self._last_update_step = 0
                logging.debug("Initialized missing _last_update_step attribute")

            # Update internal state for short-term reward calculations only
            # We don't maintain a full history here anymore - that's the responsibility of global_state
            if self._last_update_step != self._step_count:
                self._internal_cash = current_cash
                self._internal_option_value = current_option_value
                # Only keep the last value for reward calculations
                if len(self._internal_net_worth_history) > 1:
                    self._internal_net_worth_history = [self._internal_net_worth_history[0], current_net_worth]
                else:
                    self._internal_net_worth_history.append(current_net_worth)
                self._last_update_step = self._step_count

                # Update peak net worth if needed
                if current_net_worth > self._episode_peak_net_worth:
                    self._episode_peak_net_worth = current_net_worth

            # Initialize initial cash if first step
            if len(self._internal_net_worth_history) <= 1:
                self.initial_cash = current_cash

        except Exception as e:
            logging.error(f"RewardScheme Error: Exception in _update_performance_history(): {e}")
            # Handle missing attributes if any
            if not hasattr(self, '_last_update_step'):
                self._last_update_step = 0

    def _calculate_reward(self, performance, action_scheme, current_step):
        """Calculate the reward based on performance metrics."""
        try:
            # Always get the current net worth directly from the action_scheme (the official scorekeeper)
            current_net_worth = action_scheme.get_current_portfolio_value() if isinstance(action_scheme, OptionActionScheme) else performance.get('current_net_worth', self.initial_cash)
            logging.debug(f"Reward calc: Current net worth from action_scheme: {current_net_worth:.2f}")

            # Ensure action_scheme is the correct type for P&L logic
            if not isinstance(action_scheme, OptionActionScheme) or \
               not hasattr(action_scheme, 'get_daily_return_pct') or \
               not hasattr(action_scheme, 'get_current_portfolio_value'):
                logging.warning("RewardScheme: action_scheme is not a compatible OptionActionScheme for P&L. Defaulting to simple reward.")
                # Fallback to simple calculation if critical components missing
                if len(self._internal_net_worth_history) <= 1: return 0.0
                prev_net_worth = self._internal_net_worth_history[0] if len(self._internal_net_worth_history) > 0 else self.initial_cash
                absolute_pnl = current_net_worth - prev_net_worth
                pct_return = absolute_pnl / prev_net_worth if prev_net_worth > 0 else 0.0
            else:
                # Get P&L calculation directly from action_scheme
                pct_return = action_scheme.get_daily_return_pct(self.previous_net_worth_for_reward)
                logging.debug(f"Reward calc: Using action_scheme P&L: PrevNW={self.previous_net_worth_for_reward:.2f}, RetPct={pct_return:.4f}")

            held_option = performance.get('held_option', None)

            # Initialize consecutive counters if not present
            if not hasattr(self, '_consecutive_profits'):
                self._consecutive_profits = 0
                logging.debug("Initialized missing _consecutive_profits attribute")
            if not hasattr(self, '_consecutive_losses'):
                self._consecutive_losses = 0
                logging.debug("Initialized missing _consecutive_losses attribute")

            # Initialize last_reported_step if not present
            if not hasattr(self, '_last_reported_step'):
                self._last_reported_step = 0
                logging.debug("Initialized missing _last_reported_step attribute")

            # If first step or no history, return 0
            if len(self._internal_net_worth_history) <= 1:
                return 0.0

            # Calculate step reward based on P&L
            net_worth_history = self._internal_net_worth_history
            lookback = min(self.window_size, len(net_worth_history) - 1)  # Adjust lookback if not enough history

            # CRITICAL FIX: Robust history handling for reward calculation
            min_required_history = max(2, lookback + 1)  # Ensure we have at least 2 points for any calculation

            if len(net_worth_history) < min_required_history:
                logging.warning(f"Insufficient history for reward calculation: {len(net_worth_history)} < {min_required_history}. Using zero reward.")
                return 0.0  # Return zero reward if insufficient history

            # Use the available history, but cap lookback to what we actually have
            effective_lookback = min(lookback, len(net_worth_history) - 1)
            prev_net_worth = net_worth_history[-effective_lookback - 1]

            # Calculate absolute profit/loss
            absolute_pnl = current_net_worth - prev_net_worth

            logging.debug(f"Reward calculation: current={current_net_worth:.2f}, prev={prev_net_worth:.2f} (lookback={effective_lookback}), pnl={absolute_pnl:.2f}")

            # Track for reporting
            if current_step > 0 and self._last_reported_step != current_step:
                self._cumulative_pnl += absolute_pnl
                self._last_reported_step = current_step

            # Convert to percentage return for reward scaling
            if prev_net_worth > 0:
                pct_return = absolute_pnl / prev_net_worth
            else:
                pct_return = 0

            # Base reward on percentage return
            base_reward = pct_return # pct_return is now from the new logic if action_scheme was compatible

            # Add profit reward multiplier for profitable trades
            if base_reward > 0:
                base_reward *= self.profit_reward_multiplier

                # Add bonus for consistent profits
                if self._consecutive_profits > 0:
                    consistency_bonus = min(self.consistent_profit_bonus * self._consecutive_profits, 0.5)
                    base_reward += consistency_bonus

                self._consecutive_profits += 1
                self._consecutive_losses = 0
            # Apply penalty multiplier for losses
            elif base_reward < 0:
                base_reward *= self.loss_penalty_multiplier

                # Track consecutive losses (will be used for increasing penalties)
                self._consecutive_profits = 0
                self._consecutive_losses += 1

                # Add drawdown penalty if applicable
                if len(net_worth_history) > 2:
                    max_net_worth = max(net_worth_history[:-1])  # Max excluding current
                    if max_net_worth > current_net_worth:
                        drawdown_pct = (max_net_worth - current_net_worth) / max_net_worth
                        drawdown_penalty = drawdown_pct * self.drawdown_penalty_coeff
                        base_reward -= drawdown_penalty

            # Get current underlying price from action_scheme if possible
            current_underlying_price = 0.0
            previous_underlying_price = self._prev_underlying_price
            try:
                if isinstance(action_scheme, OptionActionScheme) and hasattr(action_scheme, '_get_robust_underlying_price'):
                    current_underlying_price = action_scheme._get_robust_underlying_price(None, current_step)
                    # If this is the first step with price data, initialize previous price
                    if previous_underlying_price == 0.0:
                        previous_underlying_price = current_underlying_price
                        self._prev_underlying_price = current_underlying_price
            except Exception as e:
                logging.warning(f"Error getting underlying price: {e}")

            # Calculate market direction and volatility
            market_direction = 0.0
            realized_vol = 0.0
            if current_underlying_price > 0 and previous_underlying_price > 0:
                # Determine market direction (up = positive, down = negative)
                price_change_pct = (current_underlying_price - previous_underlying_price) / previous_underlying_price
                market_direction = price_change_pct

                # Keep track of recent price changes for realized volatility calculation
                self._realized_volatility.append(abs(price_change_pct))
                if len(self._realized_volatility) > 5:  # Keep only recent history
                    self._realized_volatility.pop(0)

                # Calculate realized volatility as average of absolute price changes
                if self._realized_volatility:
                    realized_vol = sum(self._realized_volatility) / len(self._realized_volatility)

            # Update previous price for next step
            if current_underlying_price > 0:
                self._prev_underlying_price = current_underlying_price

            # Add Greeks-based penalties and rewards if holding an option
            if held_option:
                try:
                    # Theta penalty (time decay)
                    theta = held_option.get('theta', 0)
                    theta_penalty = abs(theta) * self.theta_penalty_weight

                    # Vega penalty (volatility risk)
                    vega = held_option.get('vega', 0)
                    vega_penalty = abs(vega) * self.vega_penalty_weight

                    # Delta alignment reward
                    delta = held_option.get('delta', 0)
                    option_type = held_option.get('type', '')

                    # Calculate delta alignment reward
                    delta_alignment_reward = 0.0
                    if market_direction != 0 and delta != 0:
                        # For a call option (positive delta), positive market direction is aligned
                        # For a put option (negative delta), negative market direction is aligned
                        is_call = option_type.lower() == 'call'
                        delta_sign = 1 if is_call else -1

                        # Reward if delta sign matches market direction sign
                        if (delta_sign > 0 and market_direction > 0) or (delta_sign < 0 and market_direction < 0):
                            # Scale reward by both the strength of the delta and the magnitude of the move
                            delta_alignment_reward = abs(delta) * abs(market_direction) * self.delta_alignment_weight
                            logging.debug(f"Delta alignment reward: {delta_alignment_reward:.4f} (Delta: {delta:.4f}, Market direction: {market_direction:.4f})")

                    # Gamma scalping reward
                    gamma_scalping_reward = 0.0
                    gamma = held_option.get('gamma', 0)
                    if gamma > 0 and realized_vol > 0:
                        # Reward higher gamma during periods of high realized volatility
                        gamma_scalping_reward = gamma * realized_vol * self.gamma_scalping_weight
                        logging.debug(f"Gamma scalping reward: {gamma_scalping_reward:.4f} (Gamma: {gamma:.4f}, Realized vol: {realized_vol:.4f})")

                    # Apply Greeks penalties and rewards
                    total_greeks_effect = delta_alignment_reward + gamma_scalping_reward - (theta_penalty + vega_penalty)
                    base_reward += total_greeks_effect

                    # Track costs and rewards for reporting
                    self._cumulative_costs += (theta_penalty + vega_penalty) # Penalties

                    # Log the components if significant
                    if abs(total_greeks_effect) > 0.01:
                        logging.info(f"Greeks effect: {total_greeks_effect:.4f} = Delta alignment: {delta_alignment_reward:.4f} + Gamma scalping: {gamma_scalping_reward:.4f} - Penalties: {(theta_penalty + vega_penalty):.4f}")

                except Exception as e:
                    logging.warning(f"Error calculating Greeks rewards/penalties: {e}", exc_info=True)



            # Sharpe ratio reward component if we have enough history
            if lookback >= 3:  # Need some history for meaningful calculation
                returns = []
                for i in range(1, lookback + 1):
                    if net_worth_history[-i-1] > 0:  # Avoid division by zero
                        ret = (net_worth_history[-i] - net_worth_history[-i-1]) / net_worth_history[-i-1]
                        returns.append(ret)

                if returns and len(returns) > 1:
                    returns_mean = sum(returns) / len(returns)

                    # Calculate std dev with small epsilon to avoid division by zero
                    returns_std = max(
                        math.sqrt(sum((r - returns_mean) ** 2 for r in returns) / len(returns)),
                        EPSILON
                    )

                    # Simple Sharpe-like reward component
                    sharpe_ratio = returns_mean / returns_std if returns_std > 0 else 0
                    sharpe_reward = sharpe_ratio * self.sharpe_ratio_weight
                    base_reward += sharpe_reward

            # Apply reward clipping if specified
            if self.max_reward_per_step is not None:
                base_reward = max(min(base_reward, self.max_reward_per_step), -self.max_reward_per_step)

            # IMPORTANT: Update previous_net_worth_for_reward for the next step
            if isinstance(action_scheme, OptionActionScheme) and hasattr(action_scheme, 'get_current_portfolio_value'):
                self.previous_net_worth_for_reward = action_scheme.get_current_portfolio_value()
                logging.debug(f"Reward calc: Updated previous_net_worth_for_reward to {self.previous_net_worth_for_reward:.2f}")
            else:
                # If not using new P&L, update based on old mechanism (though this path should be less common)
                self.previous_net_worth_for_reward = performance['current_net_worth']

            return base_reward

        except Exception as e:
            logging.error(f"RewardScheme Error: Exception in _calculate_reward(): {e}", exc_info=True)
            return 0.0

    def reward(self, env_wrapper:'TradingEnv') -> float:
        """Calculate and return the reward for the current step."""
        try:
            # First check if env_wrapper is a GymnasiumWrapper with direct component access
            if hasattr(env_wrapper, '_action_scheme') and env_wrapper._action_scheme is not None:
                # Use direct component access from GymnasiumWrapper
                base_env = env_wrapper
                logging.debug("RewardScheme: Using direct component access from GymnasiumWrapper")
            # Next check if env_wrapper has portfolio and action_scheme directly
            elif hasattr(env_wrapper, 'portfolio') and hasattr(env_wrapper, 'action_scheme') and \
                 getattr(env_wrapper, 'portfolio') is not None and getattr(env_wrapper, 'action_scheme') is not None:
                base_env = env_wrapper
                logging.debug("RewardScheme: Using direct component access from environment")
            else:
                # Fall back to unwrapping if direct access isn't available
                base_env = unwrap_env(env_wrapper)
                logging.debug("RewardScheme: Using unwrapped environment for component access")

            if base_env is None:
                logging.error("RewardScheme Error: Unwrapping environment failed, base_env is None.")
                return 0.0

            # ENHANCED LOGGING for state synchronization verification
            logging.info(f"REWARDSCHEME_REWARD_INIT: RewardScheme_ID={id(self)}, "
                        f"Clock_ID={id(base_env.clock) if hasattr(base_env, 'clock') else 'N/A'}, "
                        f"Clock_Step={base_env.clock.step if hasattr(base_env, 'clock') else 'N/A'}, "
                        f"Self_StepCount={self._step_count}, Self_LastUpdateStep={self._last_update_step}")

            # Access portfolio - raise an error if not available instead of using fallback
            if not hasattr(base_env, 'portfolio') or base_env.portfolio is None:
                logging.error("RewardScheme Error: Environment missing portfolio attribute.")
                raise ValueError("Missing portfolio in environment - cannot calculate reward")

            # Check portfolio has wallets
            portfolio = base_env.portfolio
            if not hasattr(portfolio, 'wallets'):
                logging.error("RewardScheme Error: portfolio has no wallets attribute. Proceeding with empty wallets.")
                portfolio.wallets = []

            # Access action scheme - raise an error if not available instead of using fallback
            if not hasattr(base_env, 'action_scheme') or base_env.action_scheme is None:
                logging.error("RewardScheme Error: Environment missing action_scheme attribute.")
                raise ValueError("Missing action_scheme in environment - cannot calculate reward")

            action_scheme = base_env.action_scheme
            if not isinstance(action_scheme, OptionActionScheme):
                logging.error("RewardScheme Error: action_scheme is not OptionActionScheme. Returning 0.")
                return 0.0

            # Get the current step
            current_step_from_clock = 0
            if hasattr(base_env, 'clock') and base_env.clock is not None:
                current_step_from_clock = base_env.clock.step

            # OPTIMIZED: Reduce logging frequency to prevent KeyboardInterrupt during training
            verbose_logging = (current_step_from_clock % 100 == 0) or (current_step_from_clock < 5)

            if verbose_logging:
                logging.info(f"REWARDSCHEME_REWARD_START: RewardScheme_ID={id(self)}, Step={current_step_from_clock}, "
                            f"ActionScheme_ID={id(action_scheme)}, "
                            f"ActionScheme_SimCash={action_scheme.simulated_cash:.2f}, "
                            f"ActionScheme_OptVal={action_scheme.current_option_market_value:.2f}, "
                            f"ActionScheme_NetWorth={action_scheme.get_current_portfolio_value():.2f}")

            self._step_count = current_step_from_clock # Synchronize self._step_count

            # Get the performance metrics directly from the action_scheme (official scorekeeper)
            perf = self._get_performance_metrics(base_env, action_scheme)

            if verbose_logging:
                logging.info(f"REWARDSCHEME_REWARD_PERF_METRICS: "
                            f"PerfCash={perf['current_cash']:.2f}, "
                            f"PerfOptVal={perf['current_option_value']:.2f}, "
                            f"PerfNetWorth={perf['current_net_worth']:.2f}")

            # Update internal state for reward calculations
            self._update_performance_history(perf)

            # Calculate the reward based on the performance
            reward = self._calculate_reward(perf, action_scheme, current_step_from_clock)

            if verbose_logging:
                logging.info(f"REWARDSCHEME_REWARD_END: CalculatedReward={reward:.4f}")

            # Cap the reward if exceeding limits
            if reward > self.max_reward_per_step:
                reward = self.max_reward_per_step
            elif reward < -self.max_reward_per_step:
                reward = -self.max_reward_per_step

            return reward

        except Exception as e:
            logging.error(f"RewardScheme Error: Exception in reward calculation: {e}", exc_info=True)
            return 0.0

class OptionObserver(Observer):
    registered_name = "option_observer_v3.1"
    def __init__(self, feed:DataFeed, action_scheme:OptionActionScheme, reward_scheme:OptionRewardScheme, window_size:int=WINDOW_SIZE):
        super().__init__(); self.feed=feed; self.action_scheme=action_scheme; self.reward_scheme=reward_scheme; self.window_size=window_size; self.market_history=[]

        # --- Enhanced Diagnostic Logging for OptionObserver Init ---
        logging.info(f"OptionObserver.__init__: Initializing with window_size={window_size}")

        # Check feed
        if self.feed is None:
            logging.critical("CRITICAL: OptionObserver.__init__: self.feed is None!")
        else:
            logging.info(f"OptionObserver.__init__: self.feed (ID: {id(self.feed)}) is present")
            if hasattr(self.feed, 'inputs'):
                logging.info(f"OptionObserver.__init__: self.feed.inputs has {len(self.feed.inputs)} streams")
            else:
                logging.critical("CRITICAL: OptionObserver.__init__: self.feed has no 'inputs' attribute!")

        # Check action_scheme
        if self.action_scheme is None:
            logging.critical("CRITICAL: OptionObserver.__init__: self.action_scheme is None!")
        else:
            logging.info(f"OptionObserver.__init__: self.action_scheme (ID: {id(self.action_scheme)}) is present")

        # Check reward_scheme
        if self.reward_scheme is None:
            logging.critical("CRITICAL: OptionObserver.__init__: self.reward_scheme is None!")
        else:
            logging.info(f"OptionObserver.__init__: self.reward_scheme (ID: {id(self.reward_scheme)}) is present")
        # --- End Enhanced Diagnostic Logging ---

        # Dynamically determine number of market features from the feed
        if hasattr(feed,'inputs') and feed.inputs:
            self.n_market_features=len(feed.inputs)
            # CRITICAL FIX: Don't sort here since DataFeed streams are now created in alphabetical order
            # This preserves the exact order that DataFeed.inputs provides
            self._feature_names = [inp.name for inp in feed.inputs]  # Preserve DataFeed order
            self.market_feature_columns = self._feature_names # CRITICAL FIX: Assign to market_feature_columns
            logging.info(f"Observer detected {self.n_market_features} market features from feed in DataFeed order.")
            logging.debug(f"Feature names from DataFeed: {self._feature_names}")
        else:
            logging.error("Observer: Feed has no inputs! Cannot determine market features. Defaulting to 10, but this is likely wrong.")
            self.n_market_features=10 # Fallback, but likely problematic
            self._feature_names = [f"unknown_feature_{i}" for i in range(self.n_market_features)]
            self.market_feature_columns = self._feature_names # CRITICAL FIX: Assign to market_feature_columns (also in fallback)

        # Calculate the total observation size accounting for all components
        self._flat_market_obs_size = self.window_size * self.n_market_features
        portfolio_obs_size = N_STATIC_PORTFOLIO_FEATURES
        options_obs_size = N_STATIC_HELD_OPTION_FEATURES  # Uses updated N_STATIC_HELD_OPTION_FEATURES

        # Total observation size is the sum of all components
        self._total_obs_size = self._flat_market_obs_size + portfolio_obs_size + options_obs_size

        self._observation_space=spaces.Box(low=-np.inf,high=np.inf,shape=(self._total_obs_size,),dtype=np.float32)

        # ENHANCED LOGGING FOR OBSERVATION STRUCTURE VERIFICATION
        logging.info(f"OptionObserver.__init__: OBSERVATION STRUCTURE VERIFICATION")
        logging.info(f"  - Window size: {self.window_size}")
        logging.info(f"  - Market features: {self.n_market_features}")
        logging.info(f"  - Market obs size: {self._flat_market_obs_size}")
        logging.info(f"  - Portfolio features: {portfolio_obs_size}")
        logging.info(f"  - Option features: {options_obs_size}")
        logging.info(f"  - Total obs size: {self._total_obs_size}")
        logging.info(f"  - Market feature columns: {self.market_feature_columns}")
        logging.info(f"  - Feature names: {self._feature_names}")
        logging.info(f"OptionObserver.__init__: Initialization complete")

    @property
    def observation_space(self)->spaces.Space: return self._observation_space

    def reset(self):
        super().reset()
        self.market_history = []
        # --- Enhanced Diagnostic Logging for OptionObserver.reset ---
        logging.debug(f"OptionObserver.reset: Resetting observer state")

        if self.feed:
            logging.debug(f"OptionObserver.reset: self.feed (ID: {id(self.feed)}) is present")

            # Check for feed.inputs which is the correct attribute
            if hasattr(self.feed, 'inputs'):
                input_count = len(self.feed.inputs) if self.feed.inputs else 0
                logging.debug(f"OptionObserver.reset: self.feed.inputs has {input_count} streams")
            else:
                logging.warning(f"OptionObserver.reset: self.feed has no 'inputs' attribute!")
        else:
            logging.warning("OptionObserver.reset: self.feed is None, cannot reset feed.")
        # --- End Enhanced Diagnostic Logging ---

    def observe(self, env_wrapper:'TradingEnv')->np.ndarray:
        try:
            # unwrap_env is still useful to get a consistent handle on portfolio, clock, action_scheme etc.
            # for _get_portfolio_obs and _get_options_obs.
            base_env_for_components = unwrap_env(env_wrapper)

            # Market observations are now primarily sourced from self.feed via _get_market_obs.
            # _get_market_obs uses the passed env_wrapper (which is the TradingEnv instance)
            # to get the current clock step.
            try:
                # _get_market_obs returns a 2D array (window_size, num_market_features)
                market_obs_2d = self._get_market_obs(self.window_size, env_wrapper)
            except Exception as market_err:
                logging.error(f"Observer Error in observe: Failed during _get_market_obs call: {market_err}")
                market_obs_shape = (self.window_size, len(self.market_feature_columns))
                market_obs_2d = np.zeros(market_obs_shape, dtype=np.float32)

            market_obs_flat = market_obs_2d.flatten() # Flatten for concatenation

            # Get portfolio-related observations (using base_env_for_components for portfolio access)
            try:
                portfolio_obs = self._get_portfolio_obs(base_env_for_components)
            except Exception as portfolio_err:
                logging.error(f"Observer Error in observe: Failed to get portfolio observations: {portfolio_err}")
                portfolio_obs = np.zeros(N_STATIC_PORTFOLIO_FEATURES, dtype=np.float32)

            # Get options-specific observations (using base_env_for_components for action_scheme access)
            try:
                option_obs = self._get_options_obs(base_env_for_components)
            except Exception as option_err:
                logging.error(f"Observer Error in observe: Failed to get option observations: {option_err}")
                option_obs = np.zeros(N_STATIC_HELD_OPTION_FEATURES, dtype=np.float32)

            # Combine all observations
            combined_obs = np.concatenate([market_obs_flat, portfolio_obs, option_obs])

            # ENHANCED LOGGING FOR OBSERVATION STRUCTURE VERIFICATION DURING TRAINING
            current_step = getattr(env_wrapper.clock, 'step', -1) if hasattr(env_wrapper, 'clock') else -1
            if current_step % 50 == 0 or current_step < 5:  # Log every 50 steps or first 5 steps
                logging.info(f"OptionObserver.observe: STEP {current_step} OBSERVATION VERIFICATION")
                logging.info(f"  - Market obs shape: {market_obs_flat.shape}, expected: ({self._flat_market_obs_size},)")
                logging.info(f"  - Portfolio obs shape: {portfolio_obs.shape}, expected: ({N_STATIC_PORTFOLIO_FEATURES},)")
                logging.info(f"  - Option obs shape: {option_obs.shape}, expected: ({N_STATIC_HELD_OPTION_FEATURES},)")
                logging.info(f"  - Combined obs shape: {combined_obs.shape}, expected: ({self._total_obs_size},)")
                logging.info(f"  - Market feature columns: {self.market_feature_columns}")
                # Log a sample of the combined observation for verification
                if len(combined_obs) > 0:
                    sample_size = min(10, len(combined_obs))
                    logging.info(f"  - Combined obs sample (first {sample_size}): {combined_obs[:sample_size]}")

            # Final check for NaNs, infs, and shape (already in existing code, kept for safety)
            if not np.isfinite(combined_obs).all():
                logging.warning("Observer Warning: NaN or inf detected in combined_obs. Sanitizing.")
                combined_obs = np.nan_to_num(combined_obs, nan=0.0, posinf=0.0, neginf=0.0)

            if combined_obs.shape[0] != self._total_obs_size:
                logging.error(f"Observer Error: Output shape mismatch. Expected {self._total_obs_size}, got {combined_obs.shape[0]}. Adjusting.")
                if combined_obs.shape[0] < self._total_obs_size:
                    combined_obs = np.pad(combined_obs, (0, self._total_obs_size - combined_obs.shape[0]), mode='constant', constant_values=0)
                else:
                    combined_obs = combined_obs[:self._total_obs_size]

            return combined_obs

        except Exception as e:
            logging.critical(f"Observer CRITICAL Error: Unexpected error in observe method: {str(e)}\n{traceback.format_exc()}")
            return np.zeros(self._total_obs_size, dtype=np.float32)

    def _get_market_obs(self, window_size, env_wrapper):
        """
        CRITICAL FIX: Get market observations from features_df with proper data handling
        to prevent all-zero observations that break learning.
        """
        try:
            if not hasattr(env_wrapper, 'clock') or not hasattr(env_wrapper.clock, 'step'):
                logging.error("Observer Error in _get_market_obs: env_wrapper.clock.step is not available.")
                return np.zeros((window_size, self.n_market_features), dtype=np.float32)

            current_step = env_wrapper.clock.step
            logging.debug(f"_get_market_obs: Processing step {current_step}")

            # Check for features_df with detailed diagnostics
            if not hasattr(env_wrapper, 'features_df'):
                logging.error(f"Observer Error in _get_market_obs: env_wrapper has no features_df attribute. Env type: {type(env_wrapper).__name__}")
                return np.zeros((window_size, self.n_market_features), dtype=np.float32)
            elif env_wrapper.features_df is None:
                logging.error(f"Observer Error in _get_market_obs: env_wrapper.features_df is None. Env: {type(env_wrapper)}")
                return np.zeros((window_size, self.n_market_features), dtype=np.float32)

            source_df = env_wrapper.features_df

            # Check source_df properties
            if not isinstance(source_df, pd.DataFrame):
                logging.error(f"Observer Error in _get_market_obs: env_wrapper.features_df is not a DataFrame. Type: {type(source_df)}.")
                return np.zeros((window_size, self.n_market_features), dtype=np.float32)

            if source_df.empty:
                logging.error("Observer Error in _get_market_obs: env_wrapper.features_df is empty.")
                return np.zeros((window_size, self.n_market_features), dtype=np.float32)

            logging.debug(f"_get_market_obs: source_df shape: {source_df.shape}, current_step: {current_step}")

            if not hasattr(self, 'market_feature_columns') or not self.market_feature_columns:
                 logging.error("_get_market_obs: self.market_feature_columns is not set or is empty.")
                 # Attempt to infer from source_df if possible, or return zeros
                 if source_df.shape[1] > 0 :
                     self.market_feature_columns = source_df.columns.tolist()
                     self.n_market_features = len(self.market_feature_columns)
                     logging.warning(f"_get_market_obs: Inferred market_feature_columns from source_df: {self.n_market_features} features.")
                 else:
                    return np.zeros((window_size, self.n_market_features if hasattr(self, 'n_market_features') and self.n_market_features > 0 else 10), dtype=np.float32)

            # Slice the DataFrame using the current step
            # Ensure current_step is within the bounds of source_df
            if not (0 <= current_step < len(source_df)):
                 logging.error(f"_get_market_obs: current_step {current_step} is out of bounds for source_df (len: {len(source_df)}).")
                 # Return last valid window or zeros
                 end_idx = len(source_df)
                 start_idx = max(0, end_idx - window_size)
                 market_obs_df = source_df.iloc[start_idx:end_idx]
                 if market_obs_df.shape[0] < window_size: # Pad if necessary
                     # CRITICAL FIX: Use forward-fill instead of zero-padding
                     if market_obs_df.shape[0] > 0:
                         last_row = market_obs_df.iloc[-1:]
                         padding_rows = window_size - market_obs_df.shape[0]
                         padding_df = pd.concat([last_row] * padding_rows, ignore_index=True)
                         market_obs_df = pd.concat([padding_df, market_obs_df], ignore_index=True)
                     else:
                         return np.zeros((window_size, self.n_market_features), dtype=np.float32)
            else:
                start_idx = max(0, current_step - window_size + 1)
                end_idx = current_step + 1
                market_obs_df = source_df.iloc[start_idx:end_idx]

            # Ensure the observation has the correct number of features and window size
            if market_obs_df.shape[1] == 0 and self.n_market_features > 0: # No columns but expected some
                 logging.warning(f"_get_market_obs: Sliced market_obs_df has 0 columns, expected {self.n_market_features}. Returning zeros.")
                 return np.zeros((window_size, self.n_market_features), dtype=np.float32)

            # Select only the market_feature_columns
            try:
                market_obs_df_selected = market_obs_df[self.market_feature_columns]
            except KeyError as e:
                logging.error(f"_get_market_obs: KeyError selecting columns: {e}. Available: {market_obs_df.columns.tolist()}. Expected: {self.market_feature_columns}")
                # Fallback: try to use all columns if shapes mismatch, or return zeros
                if market_obs_df.shape[1] == self.n_market_features:
                    market_obs_df_selected = market_obs_df
                    logging.warning("_get_market_obs: Using all columns from sliced df due to KeyError but matching feature count.")
                else:
                    return np.zeros((window_size, self.n_market_features), dtype=np.float32)

            # CRITICAL FIX: Use forward-fill instead of zero-padding for incomplete windows
            if market_obs_df_selected.shape[0] < window_size:
                if market_obs_df_selected.shape[0] > 0:
                    # Forward-fill the first available observation to fill the window
                    first_row = market_obs_df_selected.iloc[0:1]
                    padding_rows = window_size - market_obs_df_selected.shape[0]
                    padding_df = pd.concat([first_row] * padding_rows, ignore_index=True)
                    market_obs_final = pd.concat([padding_df, market_obs_df_selected], ignore_index=True)
                else:
                    # If no data available, use small non-zero values instead of zeros
                    market_obs_final = pd.DataFrame(
                        np.full((window_size, self.n_market_features), 0.001),
                        columns=self.market_feature_columns
                    )
                    logging.warning("_get_market_obs: No data available, using small non-zero fallback values")
            else:
                market_obs_final = market_obs_df_selected

            # Validate that we don't have all-zero observations
            obs_array = market_obs_final.values.astype(np.float32)
            if np.all(obs_array == 0):
                logging.warning("_get_market_obs: Detected all-zero observations, replacing with small non-zero values")
                obs_array = np.full_like(obs_array, 0.001)

            # Log sample values for debugging (only occasionally)
            if current_step % 50 == 0 or current_step < 5:
                sample_values = obs_array[-1][:5] if len(obs_array[-1]) >= 5 else obs_array[-1]
                logging.info(f"_get_market_obs step {current_step}: Sample values {sample_values}")

            return obs_array

        except Exception as e:
            logging.error(f"Observer Error in _get_market_obs: {e}", exc_info=True)
            return np.zeros((window_size, self.n_market_features), dtype=np.float32)

        except AttributeError as ae:
            # This might catch issues like env_wrapper not having 'features_df' or 'clock'
            logging.error(f"Observer Error in _get_market_obs (AttributeError): {ae}. Env type: {type(env_wrapper)}. Feed ID: {id(self.feed) if self.feed else 'N/A'}", exc_info=True)
            return np.zeros((window_size, self.n_market_features if hasattr(self, 'n_market_features') and self.n_market_features > 0 else 10), dtype=np.float32) # Fallback n_market_features
        except Exception as e:
            logging.error(f"Observer Error in _get_market_obs (General Exception): {e}. Feed ID: {id(self.feed) if self.feed else 'N/A'}", exc_info=True)
            # Provide a default shape if n_market_features is not available or zero
            num_features = self.n_market_features if hasattr(self, 'n_market_features') and self.n_market_features > 0 else 10 # Default to 10 features if unknown
            return np.zeros((window_size, num_features), dtype=np.float32)

    def _get_portfolio_obs(self, base_env):
        """Extract portfolio observations."""
        try:
            # Get portfolio values
            portfolio = getattr(base_env, 'portfolio', None)
            if portfolio is None:
                logging.error("Observer Error: No portfolio available for observation")
                return np.zeros(N_STATIC_PORTFOLIO_FEATURES, dtype=np.float32)

            # Extract basic portfolio stats
            portfolio_obs = np.zeros(N_STATIC_PORTFOLIO_FEATURES, dtype=np.float32)

            # Try to access key portfolio attributes
            try:
                # Check if portfolio has wallets
                if hasattr(portfolio, 'wallets') and portfolio.wallets:
                    # Get cash wallet
                    cash_wallet = None
                    for wallet in portfolio.wallets:
                        if wallet.instrument.symbol == 'USD':
                            cash_wallet = wallet
                            break

                    if cash_wallet is not None:
                        # Get available cash (position 0)
                        portfolio_obs[0] = float(cash_wallet.balance.as_float())

                        # Get net worth from the reward scheme if available
                        if hasattr(base_env, 'reward_scheme') and base_env.reward_scheme is not None:
                            reward_scheme = base_env.reward_scheme
                            if hasattr(reward_scheme, '_conceptual_net_worth'):
                                portfolio_obs[1] = float(reward_scheme._conceptual_net_worth)
                            elif hasattr(reward_scheme, '_internal_net_worth_history') and reward_scheme._internal_net_worth_history:
                                portfolio_obs[1] = float(reward_scheme._internal_net_worth_history[-1])

                        # Default net worth to cash if not available (position 1)
                        if portfolio_obs[1] == 0.0:
                            portfolio_obs[1] = portfolio_obs[0]
            except Exception as port_err:
                logging.error(f"Observer Error: Failed to extract portfolio data: {port_err}")

            # Position 2: Cash percentage of initial cash
            if INITIAL_CASH > 0:
                portfolio_obs[2] = portfolio_obs[0] / INITIAL_CASH

            # Position 3: Net worth percentage of initial cash
            if INITIAL_CASH > 0:
                portfolio_obs[3] = portfolio_obs[1] / INITIAL_CASH

            # Position 4: P&L as percentage
            if INITIAL_CASH > 0:
                portfolio_obs[4] = (portfolio_obs[1] / INITIAL_CASH) - 1.0

            return portfolio_obs

        except Exception as e:
            logging.error(f"Observer Error: Failed to get portfolio data: {e}")
            return np.zeros(N_STATIC_PORTFOLIO_FEATURES, dtype=np.float32)

    def _get_options_obs(self, base_env):
        """Extract options-specific observations."""
        try:
            # Initialize observation vector with zeros
            option_obs = np.zeros(N_STATIC_HELD_OPTION_FEATURES, dtype=np.float32)

            # Get action scheme
            action_scheme = getattr(base_env, 'action_scheme', None)
            if not isinstance(action_scheme, OptionActionScheme):
                logging.error(f"Observer Error: action_scheme is not OptionActionScheme. Option features will be zeros.")
                return option_obs

            # Get current step
            current_step = 0
            if hasattr(base_env, 'clock') and base_env.clock is not None:
                current_step = base_env.clock.step

            # Check if there's a held option
            held_option = getattr(action_scheme, 'held_option', None)

            # If no option held, return zeros (but ensure option_obs[0] is explicitly 0.0 for no position)
            if held_option is None or (hasattr(held_option, 'closed') and held_option.closed):
                option_obs[0] = 0.0  # Explicitly set to 0.0 for no position
                return option_obs

            # Get current underlying price - safely
            try:
                underlying_price = action_scheme._get_robust_underlying_price(None, current_step)
                # No fallback - let the validation error propagate
            except Exception as e:
                error_msg = f"AUTHENTIC DATA VALIDATION FAILED: Observer failed to get underlying price: {e}"
                logging.error(error_msg)
                raise ValueError(error_msg)

            # CRITICAL FIX: Validate data access but don't store unused variables
            # Pass the portfolio from the environment to ensure proper data access
            portfolio = getattr(base_env, 'portfolio', None)
            try:
                # Validate that we can access VIX and risk-free rate data
                action_scheme._get_robust_vix_value(portfolio, current_step)
                action_scheme._get_robust_risk_free_rate(portfolio, current_step)
            except Exception as e:
                error_msg = f"AUTHENTIC DATA VALIDATION FAILED: Observer failed to get volatility or risk-free rate: {e}"
                logging.error(error_msg)
                raise ValueError(error_msg)

            # Extract features based on held_option type
            try:
                # Check if held_option is an object with attributes or a dictionary with keys
                if hasattr(held_option, 'option_type'):
                    # === Object-style held option ===

                    # Feature 0: Option type (0 for call, 1 for put)
                    option_type = getattr(held_option, 'option_type', '').lower()
                    option_obs[0] = 0.0 if option_type == 'call' else 1.0

                    # Feature 1: Normalized moneyness (K/S - 1) - shows how far from ATM
                    strike_price = getattr(held_option, 'strike_price', underlying_price)
                    option_obs[1] = (strike_price / underlying_price) - 1.0

                    # Feature 2: Strike price (absolute value)
                    option_obs[2] = strike_price

                    # Feature 3: Normalized strike price (K/S)
                    option_obs[3] = strike_price / underlying_price

                    # Feature 4: Time to expiry (in trading days)
                    days_to_expiry = getattr(held_option, 'days_to_expiry', 0)
                    option_obs[4] = max(0, days_to_expiry - current_step)

                    # Feature 5: Contract amount (always 1.0 for consistency)
                    option_obs[5] = 1.0

                    # Feature 6: Normalized time to expiry (dte/252)
                    option_obs[6] = option_obs[4] / 252.0

                    # Feature 7: Entry premium paid
                    option_obs[7] = getattr(held_option, 'premium', 0.0)

                    # Feature 8: Current option value (estimated)
                    # This would typically be calculated using Black-Scholes
                    option_obs[8] = getattr(held_option, 'current_value', option_obs[7])

                    # Features 9-10: Strike and expiry categories (normalized)
                    option_obs[9] = getattr(held_option, 'strike_category', 0) / max(1, N_STRIKES)
                    option_obs[10] = getattr(held_option, 'expiry_category', 0) / max(1, N_EXPIRIES)

                    # Features 11-14: Greeks
                    # Calculate or retrieve Greeks
                    try:
                        # Try to get Greeks from held_option if available
                        option_obs[11] = getattr(held_option, 'delta', 0.0)
                        option_obs[12] = getattr(held_option, 'gamma', 0.0)
                        option_obs[13] = getattr(held_option, 'theta', 0.0)
                        option_obs[14] = getattr(held_option, 'vega', 0.0)
                    except Exception as greek_err:
                        logging.debug(f"Observer: Could not get Greeks from option object: {greek_err}")
                        # Greeks default to 0.0 from initialization

                else:
                    # === Dictionary-style held option ===

                    # Feature 0: Option type (0 for call, 1 for put)
                    option_type = held_option.get('type', '').lower()
                    option_obs[0] = 0.0 if 'call' in option_type else 1.0

                    # Feature 1: Normalized moneyness (K/S - 1)
                    strike_price = held_option.get('strike', underlying_price)
                    option_obs[1] = (strike_price / underlying_price) - 1.0

                    # Feature 2: Strike price (absolute value)
                    option_obs[2] = strike_price

                    # Feature 3: Normalized strike price (K/S)
                    option_obs[3] = strike_price / underlying_price

                    # Feature 4: Time to expiry (in trading days)
                    if 'expiry_step' in held_option:
                        option_obs[4] = max(0, held_option['expiry_step'] - current_step)

                    # Feature 5: Contract amount (always 1.0 for consistency)
                    option_obs[5] = 1.0

                    # Feature 6: Normalized time to expiry (dte/252)
                    option_obs[6] = option_obs[4] / 252.0

                    # Feature 7: Entry premium paid
                    option_obs[7] = held_option.get('premium_paid_per_share', 0.0)

                    # Feature 8: Current option value
                    option_obs[8] = held_option.get('current_value', option_obs[7])

                    # Features 9-10: Strike and expiry categories (normalized)
                    option_obs[9] = held_option.get('strike_category', 0) / max(1, N_STRIKES)
                    option_obs[10] = held_option.get('expiry_category', 0) / max(1, N_EXPIRIES)

                    # Features 11-14: Greeks
                    option_obs[11] = held_option.get('delta', 0.0)
                    option_obs[12] = held_option.get('gamma', 0.0)
                    option_obs[13] = held_option.get('theta', 0.0)
                    option_obs[14] = held_option.get('vega', 0.0)
            except Exception as option_attr_err:
                logging.error(f"Observer Error: Failed to extract option attributes: {option_attr_err}")
                # Ensure option_obs[0] is set even in case of error
                if option_obs[0] == 0.0:  # If not already set
                    option_type = ''
                    if held_option:
                        option_type = getattr(held_option, 'option_type', held_option.get('type', '')).lower()
                    option_obs[0] = 0.0 if 'call' in option_type else 1.0

            # Final check to ensure all values are finite
            option_obs = np.nan_to_num(option_obs, nan=0.0, posinf=0.0, neginf=0.0)
            return option_obs

        except Exception as e:
            logging.error(f"Observer Error: Failed to build option features: {e}")
            option_obs = np.zeros(N_STATIC_HELD_OPTION_FEATURES, dtype=np.float32)
            option_obs[0] = 0.0  # Explicitly set to 0.0 for no position
            return option_obs

# --- Environment Creation Function ---
def create_trading_environment(
    features_df: pd.DataFrame,
    initial_cash: float = INITIAL_CASH,
    window_size: int = WINDOW_SIZE,
    reward_scheme_config: Optional[Dict[str, Any]] = None, # Added for Optuna
    action_scheme_config: Optional[Dict[str, Any]] = None # Added for Optuna optimization
) -> Optional['GymnasiumWrapper']:
    """
    Create a TradingEnv instance for options trading wrapped in a GymnasiumWrapper.
    Ensures consistent window_size across all components to prevent inconsistencies.

    Args:
        features_df: DataFrame containing market features for trading
        initial_cash: Starting cash amount for the portfolio
        window_size: Lookback window size for observations, used consistently across components
        reward_scheme_config: Optional configuration for reward scheme parameters
        action_scheme_config: Optional configuration for action scheme parameters

    Returns:
        A gymnasium-compatible environment or None if creation fails
    """
    if features_df is None or features_df.empty:
        logging.error("Empty features DF for env.")
        return None

    underlying_col = f'close_{UNDERLYING_TICKER}'
    if underlying_col not in features_df.columns:
        logging.error(f"Missing '{underlying_col}'.")
        return None

    try:
        # Create base instruments
        base_instrument = USD
        underlying_instrument = Instrument(UNDERLYING_TICKER, 2, "Underlying")
        pair = TradingPair(underlying_instrument, base_instrument)

        # Extract price stream for underlying
        price_stream_vals = features_df[underlying_col].tolist()
        if not price_stream_vals:
            logging.error("Underlying price stream empty.")
            return None

        # Create price stream
        underlying_price_stream = Stream.source(price_stream_vals, dtype="float").rename(str(pair))

        # Create exchange
        exchange = Exchange("sim-option-exchange", service=execute_order)(underlying_price_stream)

        # Create wallets and portfolio
        cash_wallet = Wallet(exchange, initial_cash * base_instrument)
        underlying_wallet = Wallet(exchange, 0 * underlying_instrument)
        portfolio = Portfolio(base_instrument, [cash_wallet, underlying_wallet])

        # Sort feature columns alphabetically to ensure consistency
        # between DataFeed stream order and OptionObserver expectations
        sorted_feature_columns = sorted(features_df.columns)
        logging.info(f"Creating DataFeed streams in alphabetical order: {len(sorted_feature_columns)} features")
        logging.debug(f"Original column order: {list(features_df.columns)}")
        logging.debug(f"Sorted column order: {sorted_feature_columns}")

        # Create feature streams for all columns in alphabetical order
        feature_streams = []
        for col_name in sorted_feature_columns:
            try:
                # Convert to numeric but don't fill NaN with 0 for critical data columns
                feature_data = pd.to_numeric(features_df[col_name], errors='coerce')

                # Check for critical columns that should not have NaN values filled with 0
                irx_clean = IRX_TICKER.replace("^","").upper()
                vix_clean = VIX_TICKER.replace("^","").upper()
                critical_columns = [f'close_{irx_clean}', f'close_{vix_clean}', f'close_{UNDERLYING_TICKER}']

                if col_name in critical_columns:
                    # For critical columns, check if we have NaN values
                    if feature_data.isna().any():
                        nan_count = feature_data.isna().sum()
                        logging.error(f"AUTHENTIC DATA VALIDATION FAILED: Critical column '{col_name}' contains {nan_count} NaN values. Cannot proceed with corrupted data.")
                        return None
                    # Use forward fill for any remaining NaN values in critical columns
                    feature_data = feature_data.ffill()
                    # If still NaN after forward fill, use backward fill
                    feature_data = feature_data.bfill()
                    # Final check - if still NaN, this is a data integrity issue
                    if feature_data.isna().any():
                        remaining_nan = feature_data.isna().sum()
                        logging.error(f"AUTHENTIC DATA VALIDATION FAILED: Critical column '{col_name}' still contains {remaining_nan} NaN values after forward/backward fill. Data integrity compromised.")
                        return None
                else:
                    # CRITICAL FIX: For non-critical columns, avoid zero-filling
                    # Use forward fill, then interpolation, then median fill as last resort
                    feature_data = feature_data.ffill().bfill()

                    # If still NaN, use interpolation
                    if feature_data.isna().any():
                        feature_data = feature_data.interpolate(method='linear', limit_direction='both')

                    # As absolute last resort, use median instead of zeros
                    if feature_data.isna().any():
                        median_val = feature_data.median()
                        if pd.notna(median_val) and median_val != 0:
                            feature_data = feature_data.fillna(median_val)
                        else:
                            # If median is 0 or NaN, use mean
                            mean_val = feature_data.mean()
                            if pd.notna(mean_val) and mean_val != 0:
                                feature_data = feature_data.fillna(mean_val)
                            else:
                                # Absolute last resort: small non-zero value
                                feature_data = feature_data.fillna(0.001)
                                logging.warning(f"Used fallback value 0.001 for non-critical column {col_name}")

                feature_data_list = feature_data.tolist()
                feature_streams.append(Stream.source(feature_data_list, dtype="float").rename(col_name))
            except Exception as e:
                logging.error(f"Stream creation fail for '{col_name}': {e}. Skip.")
                continue

        if not feature_streams:
            logging.error("No valid feature streams.")
            return None

        # Create data feed
        feed = DataFeed(feature_streams)
        feed.compile()
        logging.info(f"DataFeed compiled with {len(feed.inputs)} streams.")

        # CRITICAL VERIFICATION: Ensure DataFeed input order matches sorted feature columns
        datafeed_input_names = [inp.name for inp in feed.inputs]
        if datafeed_input_names != sorted_feature_columns:
            logging.error(f"CRITICAL ERROR: DataFeed input order mismatch!")
            logging.error(f"Expected (sorted): {sorted_feature_columns}")
            logging.error(f"DataFeed inputs: {datafeed_input_names}")
            return None
        else:
            logging.info(f"SUCCESS: DataFeed input order matches sorted feature columns ({len(datafeed_input_names)} features)")

        # Create action scheme with configuration parameters
        action_scheme_params = {}
        if action_scheme_config:
            for key, value in action_scheme_config.items():
                if key == 'min_option_premium_per_share':
                    action_scheme_params['min_option_premium_per_share'] = value
                    logging.info(f"Using optimized action_scheme {key}={value}")
                elif key == 'confidence_threshold':
                    action_scheme_params['confidence_threshold'] = value
                    logging.info(f"Using optimized action_scheme {key}={value}")
                else:
                    logging.warning(f"Unknown action_scheme parameter: {key}")

        action_scheme = OptionActionScheme(
            cash_wallet=cash_wallet,
            underlying_instrument=underlying_instrument,
            features_df=features_df,
            **action_scheme_params
        )

        # Create and use reward scheme with optimized parameters if available
        try:
            reward_scheme_params = {}

            # Default parameters
            # CRITICAL: This ensures the reward_scheme window_size matches the global window_size
            # to prevent inconsistencies between components
            reward_scheme_params['window_size'] = window_size  # Use provided window_size
            reward_scheme_params['initial_cash'] = float(initial_cash)
            reward_scheme_params['sharpe_ratio_weight'] = SHARPE_RATIO_WEIGHT
            reward_scheme_params['theta_penalty_weight'] = THETA_PENALTY_COEFF
            reward_scheme_params['vega_penalty_weight'] = VEGA_PENALTY_COEFF
            reward_scheme_params['profit_reward_multiplier'] = PROFIT_REWARD_MULTIPLIER
            reward_scheme_params['consistent_profit_bonus'] = CONSISTENT_PROFIT_BONUS
            reward_scheme_params['loss_penalty_multiplier'] = LOSS_PENALTY_MULTIPLIER
            reward_scheme_params['drawdown_penalty_coeff'] = DRAWDOWN_PENALTY_COEFF

            if reward_scheme_config:
                for key, value in reward_scheme_config.items():
                    if key in reward_scheme_params:
                        # If this is the window_size parameter and it's not equal to the observer's window_size,
                        # log a warning but ensure consistency by using the global window_size
                        if key == 'window_size' and value != window_size:
                            logging.warning(f"Overriding reward_scheme window_size={value} to maintain consistency with global window_size={window_size}")
                            reward_scheme_params[key] = window_size
                        else:
                            reward_scheme_params[key] = value
                            logging.info(f"Overriding reward_scheme {key}={value}")

                    else:
                        logging.warning(f"Unknown reward_scheme parameter: {key}")

            reward_scheme = OptionRewardScheme(**reward_scheme_params)
        except Exception as e:
            logging.error(f"Failed to create reward scheme: {e}")
            return None

        # Create observer
        observer = OptionObserver(
            window_size=window_size,
            feed=feed,
            action_scheme=action_scheme,
            reward_scheme=reward_scheme
        )

        # Save observation metadata for consistency verification during signal generation
        obs_metadata_path = os.path.join(MODEL_DIR, "observation_metadata.json")
        try:
            save_observation_metadata(observer, obs_metadata_path)
        except Exception as e:
            logging.warning(f"Failed to save observation metadata: {e}")

        # Create other components
        stopper = UnderlyingStopper(
            max_steps=min(MAX_STEPS_PER_EPISODE, len(features_df)-1),
            max_loss_pct=MAX_ALLOWED_LOSS
        )
        informer = UnderlyingInformer()
        # CRITICAL FIX: Disable renderer during training to prevent excessive logging
        # The renderer was causing detailed step-by-step logs that made it appear like manual execution
        renderer = UnderlyingRenderer(step_interval=10000)  # Very high interval to minimize output during training

        # Create TradingEnv
        tt_env = TradingEnv(
            portfolio=portfolio,
            exchange=exchange,
            feed=feed,
            action_scheme=action_scheme,
            reward_scheme=reward_scheme,
            observer=observer,
            stopper=stopper,
            informer=informer,
            renderer=renderer
        )

        # Explicitly attach critical components to the environment for GymnasiumWrapper
        tt_env.portfolio = portfolio
        tt_env.action_scheme = action_scheme
        tt_env.observer = observer
        tt_env.reward_scheme = reward_scheme
        tt_env.feed = feed
        tt_env.features_df = features_df
        tt_env.clock = getattr(tt_env, 'clock', None)  # May not exist in some versions

        # CRITICAL FIX: Simplified error handling that doesn't interfere with PPO
        # Store original step method but don't override it unless absolutely necessary
        original_step = tt_env.step

        # Only add minimal error recovery without changing the step signature
        def safe_step(action):
            try:
                result = original_step(action)
                # CRITICAL FIX: Always return new Gymnasium format (5 values) for consistency
                if isinstance(result, tuple) and len(result) == 4:
                    # Old format: obs, reward, done, info
                    obs, reward, done, info = result
                    return obs, reward, done, False, info  # terminated, truncated, info
                elif isinstance(result, tuple) and len(result) == 5:
                    # Already new format
                    return result
                else:
                    # Unexpected format, convert to new format
                    logging.warning(f"Unexpected step result format: {len(result) if hasattr(result, '__len__') else type(result)}")
                    return result
            except Exception as e:
                logging.error(f"Critical error in TradingEnv.step: {e}", exc_info=True)
                # Return minimal fallback in new Gymnasium format
                try:
                    obs = tt_env.observer.observe(tt_env)
                except:
                    obs = tt_env.observation_space.sample() * 0
                reward = -1.0  # Penalty for error
                terminated = True  # End episode on error
                truncated = False
                info = {"error": str(e), "recovered": True}
                return obs, reward, terminated, truncated, info

        tt_env.step = safe_step
        logging.info("Applied minimal safe_step wrapper to TradingEnv.")

        # Create Gymnasium wrapper
        gym_env = GymnasiumWrapper(tt_env)

        # Explicit assignment of important attributes
        gym_env._trading_env = tt_env

        # Verify unwrapping works
        test_env = unwrap_env(gym_env)
        if not test_env or not hasattr(test_env, 'portfolio'):
            logging.warning("Environment unwrapping test failed. Environment might not work correctly.")

        # Update global state for fallback before use in Observer and RewardScheme
        global_state.portfolio = portfolio
        global_state.action_scheme = action_scheme
        global_state.reward_scheme = reward_scheme
        logging.info("Option trading env v3.1 created and global state updated.")
        return gym_env

    except Exception as e:
        logging.error(f"Error creating env v3.1: {e}", exc_info=True)
        return None

# --- Optuna Hyperparameter Optimization ---
class ResourceManager:
    """Enhanced context manager for robust resource cleanup with timeout protection"""
    def __init__(self):
        self.resources = []
        self.cleanup_timeout = 30  # 30 seconds max for cleanup

    def register(self, resource, cleanup_method='close'):
        """Register a resource for cleanup"""
        if resource is not None:
            self.resources.append((resource, cleanup_method))
        return resource

    def cleanup(self):
        """Clean up all resources with error handling and timeout protection"""
        import time

        cleanup_start = time.time()
        errors = []

        def cleanup_resource(resource, method_name):
            try:
                if hasattr(resource, method_name):
                    cleanup_func = getattr(resource, method_name)
                    if callable(cleanup_func):
                        cleanup_func()
                        logging.debug(f"Successfully cleaned up {type(resource).__name__}")
                elif hasattr(resource, 'close'):
                    # Fallback to close method
                    resource.close()
                    logging.debug(f"Successfully closed {type(resource).__name__}")
            except Exception as e:
                error_msg = f"Failed to clean up {type(resource).__name__}: {e}"
                errors.append(error_msg)
                logging.warning(error_msg)

        # Clean up resources in reverse order with timeout protection
        for resource, method_name in reversed(self.resources):
            if time.time() - cleanup_start > self.cleanup_timeout:
                logging.warning(f"Cleanup timeout reached ({self.cleanup_timeout}s). Stopping cleanup.")
                break

            cleanup_resource(resource, method_name)

        # Force garbage collection to free memory
        try:
            import gc
            gc.collect()
        except Exception as e:
            logging.debug(f"Garbage collection failed: {e}")

        if errors:
            logging.error(f"Cleanup completed with {len(errors)} errors: {'; '.join(errors[:3])}")  # Limit error messages
        else:
            logging.debug("All resources cleaned up successfully")

        # Clear the resources list
        self.resources.clear()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.cleanup()
        # Log any exception that occurred
        if exc_type is not None:
            logging.error(f"Exception in ResourceManager context: {exc_type.__name__}: {exc_val}")
        return False  # Don't suppress exceptions

def optimize_hyperparameters(train_features_df, eval_features_df, n_trials=50, study_name=None, storage=None):
    """
    Optimize hyperparameters for the trading agent using Optuna.

    Args:
        train_features_df: DataFrame for training the model
        eval_features_df: DataFrame for evaluating the model
        n_trials: Number of Optuna trials to run
        study_name: Name for the Optuna study
        storage: Optuna storage string (e.g., "sqlite:///optuna.db")

    Returns:
        Best hyperparameters as a dictionary, or sensible defaults if optimization fails
    """
    # Define refined parameter spaces for effective optimization
    param_distributions = {
        # PPO algorithm hyperparameters - Refined ranges based on RL best practices
        "learning_rate": (5e-5, 3e-4),        # Learning rate for PPO optimizer (log scale recommended)
        "n_epochs": (3, 10),                   # PPO epochs - reasonable range to avoid overfitting
        "gamma": (0.95, 0.999),               # Discount factor - standard range for financial RL
        "gae_lambda": (0.9, 0.99),            # GAE lambda - bias-variance trade-off
        "clip_range": (0.1, 0.3),            # PPO clipping - conservative range for stability
        "ent_coef": (1e-5, 0.03),             # Entropy coefficient - 
        "vf_coef": (0.2, 0.8),                # Value function coefficient - balanced range

        # Custom reward scheme hyperparameters - Refined for trading effectiveness
        "sharpe_ratio_weight": (0.1, 1.0),     # Sharpe ratio weight - allow up to 100% weight
        "theta_penalty_weight": (0.001, 0.1),  # Theta penalty - refined upper bound
        "vega_penalty_weight": (0.001, 0.05),  # Vega penalty - conservative range
        "profit_reward_multiplier": (0.5, 3.0), # Profit multiplier - reasonable range
        "consistent_profit_bonus": (0.05, 1),  # Consistency bonus - moderate range
        "loss_penalty_multiplier": (1.0, 6.0),  # Loss penalty - balanced with profit multiplier
        "drawdown_penalty_coeff": (0.01, 1.0),  # Drawdown penalty - full range maintained

        # Action scheme hyperparameters - Refined for practical trading
        "min_option_premium_per_share": (0.0, 0.5),  # Premium threshold - practical upper bound
        "confidence_threshold": (0.5, 0.9)           # Confidence threshold - meaningful range only
    }

    # Create or load Optuna study
    if study_name and storage:
        try:
            # Disable pruning completely to ensure trials complete
            study = optuna.create_study(
                storage=storage,
                study_name=study_name,
                direction="maximize",  # Maximize Sharpe ratio
                sampler=optuna.samplers.TPESampler(),
                pruner=optuna.pruners.NopPruner()  # No pruning - ensure all trials complete
            )
            logging.info(f"Created new study '{study_name}'")
        except:
            study = optuna.load_study(study_name=study_name, storage=storage)
            logging.info(f"Loaded existing study '{study_name}'")
    else:
        # Disable pruning completely to ensure trials complete
        study = optuna.create_study(
            direction="maximize",
            sampler=optuna.samplers.TPESampler(),
            pruner=optuna.pruners.NopPruner()  # No pruning - ensure all trials complete
        )
        logging.info("Created new in-memory study")

    # Define objective function
    def objective(trial):
        # Sample hyperparameters from defined parameter spaces with appropriate sampling methods
        learning_rate = trial.suggest_float("learning_rate", *param_distributions["learning_rate"], log=True)
        n_epochs = trial.suggest_int("n_epochs", *param_distributions["n_epochs"])
        gamma = trial.suggest_float("gamma", *param_distributions["gamma"])
        gae_lambda = trial.suggest_float("gae_lambda", *param_distributions["gae_lambda"])
        clip_range = trial.suggest_float("clip_range", *param_distributions["clip_range"])
        ent_coef = trial.suggest_float("ent_coef", *param_distributions["ent_coef"], log=True)  # Log scale for small values
        vf_coef = trial.suggest_float("vf_coef", *param_distributions["vf_coef"])

        # Sample reward scheme hyperparameters from defined parameter spaces
        reward_scheme_config = {
            "sharpe_ratio_weight": trial.suggest_float("sharpe_ratio_weight", *param_distributions["sharpe_ratio_weight"]),
            "theta_penalty_weight": trial.suggest_float("theta_penalty_weight", *param_distributions["theta_penalty_weight"]),
            "vega_penalty_weight": trial.suggest_float("vega_penalty_weight", *param_distributions["vega_penalty_weight"]),
            "profit_reward_multiplier": trial.suggest_float("profit_reward_multiplier", *param_distributions["profit_reward_multiplier"]),
            "consistent_profit_bonus": trial.suggest_float("consistent_profit_bonus", *param_distributions["consistent_profit_bonus"]),
            "loss_penalty_multiplier": trial.suggest_float("loss_penalty_multiplier", *param_distributions["loss_penalty_multiplier"]),
            "drawdown_penalty_coeff": trial.suggest_float("drawdown_penalty_coeff", *param_distributions["drawdown_penalty_coeff"]),
        }

        # Sample action scheme hyperparameters from defined parameter spaces
        action_scheme_config = {
            "min_option_premium_per_share": trial.suggest_float("min_option_premium_per_share", *param_distributions["min_option_premium_per_share"]),
            "confidence_threshold": trial.suggest_float("confidence_threshold", *param_distributions["confidence_threshold"]),
        }

        # Environment parameters
        # window_size = trial.suggest_int("window_size", 5, 40) # Use global WINDOW_SIZE

        # Create training directory for this trial
        trial_dir = os.path.join(SB3_LOG_DIR, f"trial_{trial.number}")
        os.makedirs(trial_dir, exist_ok=True)
        log_path = os.path.join(trial_dir, "logs")
        os.makedirs(log_path, exist_ok=True)
        model_path = os.path.join(trial_dir, "models")
        os.makedirs(model_path, exist_ok=True)

        # Set evaluation frequency - much less frequent evaluations to allow substantial training time
        eval_freq = TOTAL_TIMESTEPS // 3  # Only evaluate 3 times during training to allow more learning

        try:
            # Use ResourceManager for environment cleanup
            with ResourceManager() as rm:
                # Create training environment
                train_env = rm.register(create_trading_environment(
                    features_df=train_features_df,
                    window_size=WINDOW_SIZE, # Use global WINDOW_SIZE
                    reward_scheme_config=reward_scheme_config,
                    action_scheme_config=action_scheme_config
                ))

                if train_env is None:
                    logging.error("Failed to create training environment for trial")
                    raise optuna.exceptions.TrialPruned()

                # Create evaluation environment
                eval_env = rm.register(create_trading_environment(
                    features_df=eval_features_df,
                    window_size=WINDOW_SIZE, # Use global WINDOW_SIZE
                    reward_scheme_config=reward_scheme_config,
                    action_scheme_config=action_scheme_config
                ))

                if eval_env is None:
                    logging.error("Failed to create evaluation environment for trial")
                    raise optuna.exceptions.TrialPruned()

            # Wrap environments for monitoring
            train_env = rm.register(Monitor(train_env, os.path.join(log_path, "train_env")))
            eval_env = rm.register(Monitor(eval_env, os.path.join(log_path, "eval_env")))

            # Create vectors of environments
            train_env = rm.register(DummyVecEnv([lambda: train_env]))
            eval_env = rm.register(DummyVecEnv([lambda: eval_env]))

            # Enable observation normalization with proper configuration
            train_env = rm.register(VecNormalize(
                train_env,
                norm_obs=True,  # Re-enabled with proper configuration
                norm_reward=True,
                clip_obs=10.0,
                clip_reward=10.0,
                gamma=0.99,  # Add gamma for proper normalization
                epsilon=1e-8  # Prevent division by zero in normalization
            ))

            eval_env = rm.register(VecNormalize(
                eval_env,
                norm_obs=True,  # Re-enabled for consistency with training
                norm_reward=True,
                clip_obs=10.0,
                clip_reward=10.0,
                training=False,
                gamma=0.99,
                epsilon=1e-8
            ))

            # Allow VecNormalize to collect initial statistics before training
            # This helps stabilize normalization during early training steps
            logging.info("Collecting initial normalization statistics...")
            for _ in range(min(100, TOTAL_TIMESTEPS // 100)):  # Collect stats for 100 steps or 1% of training
                _obs = train_env.reset()
                action = train_env.action_space.sample()
                try:
                    train_env.step([action])
                except:
                    break  # Stop if environment fails
            logging.info("Initial normalization statistics collected")

            # Create PPO model with updated architecture
            policy_kwargs = {
                "net_arch": dict(pi=[256, 256, 128], vf=[512, 256]),
            }

            # Set up a separate directory for each trial's best model
            best_model_dir = os.path.join(model_path, "best_model")
            os.makedirs(best_model_dir, exist_ok=True)

            model = PPO(
                "MlpPolicy",
                train_env,
                learning_rate=learning_rate,  # From trial
                n_steps=2048,
                batch_size=64,
                n_epochs=n_epochs,  # From trial
                gamma=gamma,  # From trial
                gae_lambda=gae_lambda,  # From trial
                clip_range=clip_range,  # From trial
                clip_range_vf=None,
                ent_coef=ent_coef,  # From trial
                vf_coef=vf_coef,  # From trial
                max_grad_norm=0.5,
                use_sde=False,
                sde_sample_freq=-1,
                target_kl=None,
                policy_kwargs=policy_kwargs,
                tensorboard_log=os.path.join(log_path, "tensorboard"),
                verbose=0,
                seed=None,
                device="auto"
            )

            # Set model reference in action scheme for confidence extraction
            try:
                base_env = unwrap_env(train_env)
                if hasattr(base_env, 'action_scheme') and hasattr(base_env.action_scheme, 'set_model_for_confidence'):
                    base_env.action_scheme.set_model_for_confidence(model)
                    logging.info(f"Trial {trial.number}: Set model reference in action scheme for confidence extraction")
            except Exception as e:
                logging.warning(f"Trial {trial.number}: Could not set model reference in action scheme: {e}")

            # Set up the Optuna pruning callback using the OptunaPruningEvalCallback
            # This will properly evaluate the model on the eval_env at regular intervals
            # This is the PRIMARY metric source that Optuna will use to judge model performance
            logging.info(f"Trial {trial.number}: Setting up OptunaPruningEvalCallback with {N_EVAL_EPISODES} evaluation episodes")

            # CRITICAL FIX: Set evaluation mode and reset evaluation flag in action scheme before starting evaluation
            # This ensures trade statistics are properly reset at the start of each trial
            try:
                # Set global evaluation mode for the evaluation environment
                global_state.set_evaluation_mode(True)
                logging.info(f"Trial {trial.number}: Set global evaluation mode to True")

                # Access the action scheme through the environment wrapper layers
                if hasattr(eval_env, 'envs') and len(eval_env.envs) > 0:
                    base_env = eval_env.envs[0]
                    if hasattr(base_env, 'env'):
                        base_env = base_env.env
                    if hasattr(base_env, 'action_scheme'):
                        base_env.action_scheme._evaluation_started = False
                        logging.info(f"Trial {trial.number}: Reset evaluation flag in action scheme for fresh trade statistics")
            except Exception as e:
                logging.warning(f"Trial {trial.number}: Could not reset evaluation flag: {e}")

            optuna_eval_callback = OptunaPruningEvalCallback(
                eval_env=eval_env,
                trial=trial,
                eval_freq=eval_freq,
                log_path=os.path.join(log_path, "eval_results"),
                best_model_save_path=os.path.join(model_path, "best_model"),
                n_eval_episodes=N_EVAL_EPISODES,
                deterministic=True,
                verbose=1
            )
            logging.info(f"Trial {trial.number}: OptunaPruningEvalCallback will evaluate every {eval_freq} steps")

            # Set up the QuantStats Comparison callback
            comparison_metrics_csv_path = os.path.join(trial_dir, "quantstats_comparison_metrics.csv")
            quantstats_comparison_callback = QuantStatsComparisonCallback(
                eval_env=eval_env,
                train_env=train_env,
                train_features_df=train_features_df,
                eval_features_df=eval_features_df,
                log_path=trial_dir, # Base directory for QuantStats HTML reports
                comparison_metrics_log_path=comparison_metrics_csv_path,
                eval_freq=eval_freq,
                n_eval_episodes=N_EVAL_EPISODES,
                deterministic=True,
                verbose=1
            )

            # Store reference to OptunaPruningEvalCallback for comparison validation
            quantstats_comparison_callback.optuna_callback = optuna_eval_callback

            # Train the model - using OptunaPruningEvalCallback for proper evaluation
            # This callback will report mean_reward from evaluation episodes to Optuna
            logging.info(f"Trial {trial.number}: Starting model training with evaluation-based callbacks")
            model.learn(
                total_timesteps=TOTAL_TIMESTEPS,
                callback=[optuna_eval_callback, quantstats_comparison_callback],
                progress_bar=False
            )
            logging.info(f"Trial {trial.number}: Model training completed")

            # Check if we have a valid last_mean_reward from OptunaPruningEvalCallback
            if hasattr(optuna_eval_callback, 'last_mean_reward') and optuna_eval_callback.last_mean_reward is not None:
                logging.info(f"Trial {trial.number}: Final OptunaPruningEvalCallback.last_mean_reward = {optuna_eval_callback.last_mean_reward}")
            else:
                logging.warning(f"Trial {trial.number}: OptunaPruningEvalCallback.last_mean_reward is None after training!")

            # Save model and normalization stats
            model.save(os.path.join(model_path, "final_model.zip"))
            train_env.save(os.path.join(model_path, "vec_normalize.pkl"))

            # Evaluate the trained model
            mean_reward, _ = evaluate_policy(
                model=model,
                env=eval_env,
                n_eval_episodes=N_EVAL_EPISODES,
                deterministic=True,
                render=False,
                return_episode_rewards=False
            )

            # CRITICAL FIX: Remove unused variables to improve code quality
            # Evaluation completed, proceed with trial completion

            # Note: Hyperparameter saving is now done only after study completion
            # to avoid confusion between individual trial params and study best params

            # Get final performance metrics from the official global state history
            net_worth_history = global_state.conceptual_net_worth_history
            if len(net_worth_history) > 1:
                # Calculate returns series
                returns = []
                for i in range(1, len(net_worth_history)):
                    daily_return = (net_worth_history[i] - net_worth_history[i-1]) / net_worth_history[i-1]
                    returns.append(daily_return)

                # Convert to pandas Series for stats
                returns_series = pd.Series(returns)

                # Calculate Sharpe ratio
                sharpe_ratio = 0.0
                if len(returns_series) > 1 and returns_series.std() > 0:
                    sharpe_ratio = returns_series.mean() / returns_series.std() * np.sqrt(252)

                # Calculate other performance metrics
                try:
                    final_portfolio_value = net_worth_history[-1]
                    roi = (final_portfolio_value - INITIAL_CASH) / INITIAL_CASH
                    max_drawdown = 0.0
                    peak = net_worth_history[0]

                    for value in net_worth_history:
                        if value > peak:
                            peak = value
                        drawdown = (peak - value) / peak
                        max_drawdown = max(max_drawdown, drawdown)

                    # Log performance metrics for this trial
                    trial.set_user_attr("final_portfolio_value", float(final_portfolio_value))
                    trial.set_user_attr("roi", float(roi))
                    trial.set_user_attr("max_drawdown", float(max_drawdown))
                    trial.set_user_attr("sharpe_ratio", float(sharpe_ratio))

                    # ENHANCED: Use composite score if available, otherwise fall back to mean reward
                    # Composite score combines mean reward with win rate for better trial evaluation
                    if hasattr(optuna_eval_callback, 'last_composite_score') and optuna_eval_callback.last_composite_score is not None:
                        logging.info(f"Trial {trial.number}: Using composite_score {optuna_eval_callback.last_composite_score:.2f} as objective "
                                   f"(Mean Reward: {optuna_eval_callback.last_mean_reward:.2f}, "
                                   f"Win Rate: {optuna_eval_callback.last_win_rate:.1%}, "
                                   f"Total Trades: {optuna_eval_callback.last_total_trades})")

                        # Store all metrics as trial attributes for analysis
                        trial.set_user_attr("composite_score", float(optuna_eval_callback.last_composite_score))
                        trial.set_user_attr("last_mean_reward", float(optuna_eval_callback.last_mean_reward))
                        trial.set_user_attr("win_rate", float(optuna_eval_callback.last_win_rate))
                        trial.set_user_attr("total_trades", int(optuna_eval_callback.last_total_trades))
                        trial.set_user_attr("total_profit", float(optuna_eval_callback.last_total_profit))

                        return optuna_eval_callback.last_composite_score
                    elif hasattr(optuna_eval_callback, 'last_mean_reward') and optuna_eval_callback.last_mean_reward is not None:
                        logging.info(f"Trial {trial.number}: Using last_mean_reward {optuna_eval_callback.last_mean_reward} as objective (composite score unavailable)")
                        trial.set_user_attr("last_mean_reward", float(optuna_eval_callback.last_mean_reward))
                        return optuna_eval_callback.last_mean_reward
                    else:
                        # If last_mean_reward is not available (which shouldn't happen if callbacks worked correctly),
                        # this is likely an error state. We'll log it but still use our best performance metric.
                        logging.warning(f"Trial {trial.number}: OptunaPruningEvalCallback.last_mean_reward is None! This indicates a potential issue with evaluation.")
                        # Perform one final evaluation to get a reliable metric
                        logging.info(f"Trial {trial.number}: Performing final evaluation over {N_EVAL_EPISODES} episodes to get reliable mean reward")
                        final_mean_reward, _ = evaluate_policy(model, eval_env, n_eval_episodes=N_EVAL_EPISODES, deterministic=True)
                        logging.info(f"Trial {trial.number}: Final evaluation result: {final_mean_reward}")
                        trial.set_user_attr("final_mean_reward", float(final_mean_reward))
                        # Also store Sharpe ratio as a metric, but don't use it as the primary objective
                        trial.set_user_attr("sharpe_ratio", float(sharpe_ratio))
                        return final_mean_reward
                except Exception as e:
                    logging.error(f"Error calculating performance metrics: {e}")
                    raise optuna.exceptions.TrialPruned()

            # If code reaches here, we couldn't compute the performance metrics from net_worth_history
            # ENHANCED: Prioritize composite score, then fall back to mean reward
            if hasattr(optuna_eval_callback, 'last_composite_score') and optuna_eval_callback.last_composite_score is not None:
                logging.info(f"Trial {trial.number}: Using composite_score {optuna_eval_callback.last_composite_score:.2f} as objective from fallback path "
                           f"(Mean Reward: {optuna_eval_callback.last_mean_reward:.2f}, "
                           f"Win Rate: {optuna_eval_callback.last_win_rate:.1%}, "
                           f"Total Trades: {optuna_eval_callback.last_total_trades})")

                # Store all metrics as trial attributes for analysis
                trial.set_user_attr("composite_score_fallback", float(optuna_eval_callback.last_composite_score))
                trial.set_user_attr("last_mean_reward_fallback", float(optuna_eval_callback.last_mean_reward))
                trial.set_user_attr("win_rate_fallback", float(optuna_eval_callback.last_win_rate))
                trial.set_user_attr("total_trades_fallback", int(optuna_eval_callback.last_total_trades))
                trial.set_user_attr("total_profit_fallback", float(optuna_eval_callback.last_total_profit))

                return optuna_eval_callback.last_composite_score
            elif hasattr(optuna_eval_callback, 'last_mean_reward') and optuna_eval_callback.last_mean_reward is not None:
                logging.info(f"Trial {trial.number}: Using last_mean_reward {optuna_eval_callback.last_mean_reward} as objective from fallback path (composite score unavailable)")
                trial.set_user_attr("last_mean_reward_fallback", float(optuna_eval_callback.last_mean_reward))
                return optuna_eval_callback.last_mean_reward
            else:
                # If last_mean_reward is not available even here, we need to log this unusual circumstance
                logging.warning(f"Trial {trial.number}: No last_mean_reward available from OptunaPruningEvalCallback even in fallback path!")
                # Return mean_reward from our earlier evaluate_policy call
                logging.info(f"Trial {trial.number}: Using mean_reward {mean_reward} from final evaluate_policy call")
                trial.set_user_attr("mean_reward_final_eval", float(mean_reward))
                return mean_reward

        except optuna.exceptions.TrialPruned:
            raise
        except Exception as e:
            logging.error(f"Error in trial {trial.number}: {e}")
            logging.error(traceback.format_exc())
            raise optuna.exceptions.TrialPruned()
        finally:
            # CRITICAL FIX: Reset evaluation mode after trial completion
            try:
                global_state.set_evaluation_mode(False)
                logging.info(f"Trial {trial.number}: Reset global evaluation mode to False")
            except Exception as e:
                logging.warning(f"Trial {trial.number}: Could not reset evaluation mode: {e}")

            # ResourceManager will automatically clean up environments in __exit__
            pass

    # Optimize with optuna - ENHANCED: Add adaptive timeout and circuit breaker to prevent hanging
    import signal
    import time

    optimization_start_time = time.time()

    # ADAPTIVE TIMEOUT: Calculate based on total timesteps and number of trials
    # Estimate: ~1-3 seconds per 1000 timesteps for training + evaluation overhead
    base_time_per_trial = max(TOTAL_TIMESTEPS / 1000 * 2.5, 300)  # Minimum 5 minutes per trial
    max_optimization_time = int(base_time_per_trial * n_trials * 1.5)  # 50% buffer

    # Reasonable bounds: minimum 2 hours, NO MAXIMUM for large-scale runs
    max_optimization_time = max(7200, max_optimization_time)

    logging.info(f"ADAPTIVE TIMEOUT: Set optimization timeout to {max_optimization_time/3600:.1f} hours for {n_trials} trials with {TOTAL_TIMESTEPS} timesteps")

    def optimization_timeout_handler(_signum, _frame):
        logging.error(f"OPTIMIZATION TIMEOUT: Stopping optimization after {max_optimization_time/3600:.1f} hours")
        raise TimeoutError("Optimization timed out")

    try:
        # Set up timeout signal (Unix-like systems only)
        if hasattr(signal, 'SIGALRM'):
            signal.signal(signal.SIGALRM, optimization_timeout_handler)
            signal.alarm(max_optimization_time)

        # Add intelligent progress tracking
        def progress_callback(study, trial):
            elapsed_time = time.time() - optimization_start_time
            completed_trials = len([t for t in study.trials if t.state == optuna.trial.TrialState.COMPLETE])
            logging.info(f"Trial {trial.number} completed. Total completed: {completed_trials}/{n_trials}. Elapsed: {elapsed_time/3600:.2f}h")

            # Adaptive circuit breaker based on timesteps
            if completed_trials > 0:
                avg_time_per_trial = elapsed_time / completed_trials
                # Expected time per trial scales with timesteps: ~2-5 seconds per 1000 timesteps
                expected_time_per_trial = max(TOTAL_TIMESTEPS / 1000 * 3.5, 300)  # Minimum 5 minutes

                if avg_time_per_trial > expected_time_per_trial * 2:  # 2x expected time is concerning
                    logging.warning(f"Trials taking longer than expected: avg {avg_time_per_trial/60:.1f}min vs expected {expected_time_per_trial/60:.1f}min")
                    logging.warning(f"This may indicate hanging issues or insufficient resources for {TOTAL_TIMESTEPS} timesteps")
                else:
                    logging.info(f"Trial timing healthy: avg {avg_time_per_trial/60:.1f}min per trial")

        study.optimize(objective, n_trials=n_trials, show_progress_bar=True, callbacks=[progress_callback])

        # Disable timeout signal
        if hasattr(signal, 'SIGALRM'):
            signal.alarm(0)

        # Check if we have any completed trials
        completed_trials = [t for t in study.trials if t.state == optuna.trial.TrialState.COMPLETE]
        if not completed_trials:
            # No forced trials - only use real completed trials
            logging.error("No trials completed successfully. Cannot proceed without completed trials.")
            raise RuntimeError("No completed trials found. Try increasing n_trials or adjusting the environment parameters.")

        # Get best parameters from completed trials only
        best_params = study.best_params
        best_value = study.best_value
        best_trial = study.best_trial

        # Log best parameters and metrics
        logging.info(f"Best trial: {best_trial.number}")
        logging.info(f"Best value (Mean reward): {best_value:.4f}")
        logging.info(f"Best parameters: {best_params}")

        # Get user attributes (performance metrics)
        user_attrs = best_trial.user_attrs
        for key, value in user_attrs.items():
            logging.info(f"{key}: {value}")

        # Save best hyperparameters to file (AUTHORITATIVE - only saved after study completion)
        # This eliminates confusion between individual trial params and study's best params
        best_params_path = os.path.join(SB3_LOG_DIR, "best_hyperparameters.json")
        logging.info(f"Saving study's best hyperparameters to {best_params_path}")
        with open(best_params_path, 'w') as f:
            json.dump({
                "reward_scheme_config": {
                    "sharpe_ratio_weight": best_params.get("sharpe_ratio_weight", 0.6),
                    "theta_penalty_weight": best_params.get("theta_penalty_weight", 0.03),
                    "vega_penalty_weight": best_params.get("vega_penalty_weight", 0.015),
                    "profit_reward_multiplier": best_params.get("profit_reward_multiplier", 4.0),
                    "consistent_profit_bonus": best_params.get("consistent_profit_bonus", 0.8),
                    "loss_penalty_multiplier": best_params.get("loss_penalty_multiplier", 1.0),
                    "drawdown_penalty_coeff": best_params.get("drawdown_penalty_coeff", 0.2),
                    "window_size": WINDOW_SIZE
                },
                "action_scheme_config": {
                    "min_option_premium_per_share": best_params.get("min_option_premium_per_share", 0.30),
                    "confidence_threshold": best_params.get("confidence_threshold", 0.0)
                },
                "ppo_hyperparameters": {
                    "learning_rate": best_params.get("learning_rate", 0.00025),
                    "n_epochs": best_params.get("n_epochs", 10),
                    "gamma": best_params.get("gamma", 0.99),
                    "gae_lambda": best_params.get("gae_lambda", 0.95),
                    "clip_range": best_params.get("clip_range", 0.2),
                    "ent_coef": best_params.get("ent_coef", 0.01),
                    "vf_coef": best_params.get("vf_coef", 0.5)
                },
                "window_size": WINDOW_SIZE,
                "total_timesteps": TOTAL_TIMESTEPS
            }, f, indent=4)

        return {
            "reward_scheme_config": {
                "sharpe_ratio_weight": best_params.get("sharpe_ratio_weight", 0.6),
                "theta_penalty_weight": best_params.get("theta_penalty_weight", 0.03),
                "vega_penalty_weight": best_params.get("vega_penalty_weight", 0.015),
                "profit_reward_multiplier": best_params.get("profit_reward_multiplier", 4.0),
                "consistent_profit_bonus": best_params.get("consistent_profit_bonus", 0.8),
                "loss_penalty_multiplier": best_params.get("loss_penalty_multiplier", 1.0),
                "drawdown_penalty_coeff": best_params.get("drawdown_penalty_coeff", 0.2),
                "window_size": WINDOW_SIZE
            },
            "action_scheme_config": {
                "min_option_premium_per_share": best_params.get("min_option_premium_per_share", 0.30),
                "confidence_threshold": best_params.get("confidence_threshold", 0.0)
            },
            "ppo_hyperparameters": {
                "learning_rate": best_params.get("learning_rate", 0.00025),
                "n_epochs": best_params.get("n_epochs", 10),
                "gamma": best_params.get("gamma", 0.99),
                "gae_lambda": best_params.get("gae_lambda", 0.95),
                "clip_range": best_params.get("clip_range", 0.2),
                "ent_coef": best_params.get("ent_coef", 0.01),
                "vf_coef": best_params.get("vf_coef", 0.5)
            },
            "window_size": WINDOW_SIZE,
            "total_timesteps": TOTAL_TIMESTEPS
        }

    except (KeyboardInterrupt, TimeoutError) as e:
        # ENHANCED: Handle both KeyboardInterrupt and TimeoutError gracefully
        if isinstance(e, TimeoutError):
            logging.warning("Optimization timed out")
        else:
            logging.warning("Optimization interrupted by user")

        # Disable timeout signal if it was set
        if hasattr(signal, 'SIGALRM'):
            signal.alarm(0)

        if study.trials:
            completed_trials = [t for t in study.trials if t.state == optuna.trial.TrialState.COMPLETE]
            if completed_trials:
                logging.info(f"Returning best result from {len(completed_trials)} completed trials")
                best_params = study.best_params
                return {
                    "reward_scheme_config": {
                        "sharpe_ratio_weight": best_params.get("sharpe_ratio_weight", 0.6),
                        "theta_penalty_weight": best_params.get("theta_penalty_weight", 0.03),
                        "vega_penalty_weight": best_params.get("vega_penalty_weight", 0.015),
                        "profit_reward_multiplier": best_params.get("profit_reward_multiplier", 4.0),
                        "consistent_profit_bonus": best_params.get("consistent_profit_bonus", 0.8),
                        "loss_penalty_multiplier": best_params.get("loss_penalty_multiplier", 1.0),
                        "drawdown_penalty_coeff": best_params.get("drawdown_penalty_coeff", 0.2),
                        "window_size": WINDOW_SIZE
                    },
                    "action_scheme_config": {
                        "min_option_premium_per_share": best_params.get("min_option_premium_per_share", 0.30),
                        "confidence_threshold": best_params.get("confidence_threshold", 0.0)
                    },
                    "ppo_hyperparameters": {
                        "learning_rate": best_params.get("learning_rate", 0.00025),
                        "n_epochs": best_params.get("n_epochs", 10),
                        "gamma": best_params.get("gamma", 0.99),
                        "gae_lambda": best_params.get("gae_lambda", 0.95),
                        "clip_range": best_params.get("clip_range", 0.2),
                        "ent_coef": best_params.get("ent_coef", 0.01),
                        "vf_coef": best_params.get("vf_coef", 0.5)
                    },
                    "window_size": WINDOW_SIZE,
                    "total_timesteps": TOTAL_TIMESTEPS
                }
            else:
                logging.warning("No completed trials before interruption")
                return {}
        else:
            logging.warning("No trials started before interruption")
            return {}
    except Exception as e:
        logging.error(f"Error during hyperparameter optimization: {e}")
        logging.error(traceback.format_exc())
        return {}

# --- Main Execution Logic ---
def main():
    parser = argparse.ArgumentParser(description='SPY Options Trading PPO RL v3.1')
    parser.add_argument('--train', action='store_true', help='Train the model')
    parser.add_argument('--signal', action='store_true', help='Generate trading signal using trained model')
    parser.add_argument('--signal_config', type=str, default='', help='Configuration file for signal generation')
    parser.add_argument('--eval_only', action='store_true', help='Run evaluation on the trained model only')
    parser.add_argument('--optimize', action='store_true', help='Run Optuna hyperparameter optimization')
    parser.add_argument('--n_trials', type=int, default=50, help='Number of Optuna trials to run')
    parser.add_argument('--study_name', type=str, default='spy_options_study', help='Name for the Optuna study')
    parser.add_argument('--storage', type=str, default=None, help='Optuna storage string (e.g., "sqlite:///optuna.db")')
    parser.add_argument('--use_optimized', action='store_true', help='Use optimized hyperparameters for training')
    parser.add_argument('--strict_validation', action='store_true', help='Enable strict market data validation (fail on missing data)')
    args = parser.parse_args()
    logging.info(f"--- Option Trader v3.1 ---"); run_start_time=time.time()

    # For production runs - warn about long runtime due to 10-year data and increased timesteps
    if args.train:
        est_hours = TOTAL_TIMESTEPS / 10000  # rough estimate: 1 hour per 10k steps
        logging.warning(f"PRODUCTION RUN NOTICE: Training with {TOTAL_TIMESTEPS:,} timesteps on 10 years of data.")
        logging.warning(f"This may take approximately {est_hours:.1f} hours to complete.")
        logging.warning(f"Data range: {TRAIN_START_DATE} to {EVAL_END_DATE}")

    if args.signal:
        logging.info("Signal generation mode...")
        config = {'WINDOW_SIZE':WINDOW_SIZE, 'ALL_TICKERS':ALL_TICKERS, 'UNDERLYING_TICKER':UNDERLYING_TICKER, 'VIX_TICKER':VIX_TICKER, 'VIX3M_TICKER':VIX3M_TICKER, 'IRX_TICKER':IRX_TICKER, 'TNX_TICKER':TNX_TICKER, 'STRIKE_CATEGORIES_PCT':STRIKE_CATEGORIES_PCT, 'EXPIRY_CATEGORIES_DTE':EXPIRY_CATEGORIES_DTE, 'N_STRIKES':N_STRIKES, 'N_EXPIRIES':N_EXPIRIES, 'ACTION_SPACE_SIZE':ACTION_SPACE_SIZE, 'MIN_PERIODS_INDICATORS':MIN_PERIODS_INDICATORS, 'INTERVAL':INTERVAL, 'N_STATIC_HELD_OPTION_FEATURES':N_STATIC_HELD_OPTION_FEATURES, 'N_STATIC_PORTFOLIO_FEATURES':N_STATIC_PORTFOLIO_FEATURES}

        # CRITICAL FIX: Load hyperparameters for signal generation when --use_optimized flag is used
        signal_action_scheme_config = None
        signal_reward_scheme_config = None

        if args.use_optimized:
            # Find the best hyperparameters file
            script_dir = os.path.dirname(os.path.abspath(__file__))
            potential_paths = [
                os.path.join(script_dir, "rl_logs_yfinance_options_v3.1", "best_hyperparameters.json"),
                os.path.join(script_dir, "sb3_logs", "best_hyperparameters.json")
            ]

            signal_params_found = False
            for path in potential_paths:
                if os.path.exists(path):
                    try:
                        with open(path, 'r') as f:
                            signal_best_params = json.load(f)
                            logging.info(f"SIGNAL MODE: Loaded best hyperparameters from {path}: {signal_best_params}")
                            signal_params_found = True

                            # Extract action scheme config for signal generation
                            if 'action_scheme_config' in signal_best_params:
                                signal_action_scheme_config = signal_best_params['action_scheme_config']
                                logging.info(f"SIGNAL MODE: Using optimized action scheme config: {signal_action_scheme_config}")

                            # Extract reward scheme config for signal generation
                            if 'reward_scheme_config' in signal_best_params:
                                signal_reward_scheme_config = signal_best_params['reward_scheme_config']
                                logging.info(f"SIGNAL MODE: Using optimized reward scheme config: {signal_reward_scheme_config}")

                            break
                    except Exception as e:
                        logging.error(f"SIGNAL MODE: Error loading hyperparameters from {path}: {e}")

            if not signal_params_found:
                logging.warning("SIGNAL MODE: Could not find best hyperparameters file. Using default parameters for signal generation.")

        try:
            # CONSISTENCY FIX: Use same date system as signal generation for feature counting
            temp_start=(FIXED_END_DATE-timedelta(days=90)).strftime('%Y-%m-%d'); temp_end=FIXED_END_DATE.strftime('%Y-%m-%d')
            # CRITICAL FIX: Use reduced minimum periods for signal generation (90 days = ~62 trading days)
            # Override minimum periods to allow signal generation with shorter data periods
            data_dict_temp={}
            for t in config['ALL_TICKERS']:
                # Use min_periods_override=50 for signal generation to allow shorter data periods
                df = fetch_historical_data_yf_refactored(t, temp_start, temp_end, min_periods_override=50)
                data_dict_temp[t] = df
            temp_features=create_combined_features(data_dict_temp)
            if not temp_features.empty: config['N_MARKET_FEATURES']=temp_features.shape[1]; logging.info(f"N_MARKET_FEATURES: {config['N_MARKET_FEATURES']}")
            else: raise ValueError("Failed to create temp features for N_MARKET_FEATURES.")
        except Exception as e: logging.error(f"Could not get N_MARKET_FEATURES: {e}. Cannot generate signal."); return

        # CRITICAL FIX: Use the best trained model when --use_optimized flag is provided
        if args.use_optimized:
            # First try the latest combined model+stats file (preferred)
            latest_combined_path = os.path.join(MODEL_WITH_STATS_DIR, "latest_model_with_stats.zip")
            if os.path.exists(latest_combined_path):
                logging.info(f"Using optimized combined model+stats: {latest_combined_path}")
                model_path = latest_combined_path
                # For combined approach, we don't need separate stats file
                signal=generate_trading_signal(model_path,NORMALIZE_STATS_FILENAME,config,signal_action_scheme_config,signal_reward_scheme_config)
            else:
                # Fall back to separate best model file
                best_model_path = os.path.join(MODEL_DIR, "best_model.zip")
                if os.path.exists(best_model_path):
                    logging.info(f"Using optimized best model: {best_model_path}")
                    model_path = best_model_path
                else:
                    logging.warning(f"Best model not found at {best_model_path}, using default model")
                    model_path=os.path.join(MODEL_DIR,SB3_MODEL_FILENAME)

                if not os.path.exists(model_path): logging.error(f"Model not found: {model_path}"); return
                if not os.path.exists(NORMALIZE_STATS_FILENAME): logging.error(f"Stats not found: {NORMALIZE_STATS_FILENAME}"); return
                signal=generate_trading_signal(model_path,NORMALIZE_STATS_FILENAME,config,signal_action_scheme_config,signal_reward_scheme_config)
        else:
            # Use default model path when --use_optimized is not specified
            model_path=os.path.join(MODEL_DIR,SB3_MODEL_FILENAME)
            if not os.path.exists(model_path): logging.error(f"Model not found: {model_path}"); return
            if not os.path.exists(NORMALIZE_STATS_FILENAME): logging.error(f"Stats not found: {NORMALIZE_STATS_FILENAME}"); return
            signal=generate_trading_signal(model_path,NORMALIZE_STATS_FILENAME,config,signal_action_scheme_config,signal_reward_scheme_config)
        print("\n--- Trading Signal ---"); print(json.dumps(signal,indent=2)); print("--------------------\n")
        signal_file=f"trading_signal_{SCRIPT_VERSION_TAG}.json"
        try:
            with open(signal_file,'w') as f: json.dump(signal,f,indent=2)
            logging.info(f"Signal saved to {signal_file}")
        except Exception as e: logging.error(f"Failed to save signal: {e}")
        return

    logging.info(ErrorMessageFormatter.info("Main", "Preparing Data for Training"))
    data_dict_train = {}
    train_fetch_start_time = time.time()
    max_train_fetch_time = 600  # Maximum 10 minutes for training data fetching

    for ticker in ALL_TICKERS:
        # Check if we've exceeded the total time limit for training data fetching
        elapsed_train_time = time.time() - train_fetch_start_time
        if elapsed_train_time > max_train_fetch_time:
            logging.error(f"TRAIN FETCH TIMEOUT: Exceeded {max_train_fetch_time}s total time for training data. Script will stop to prevent hanging.")
            logging.critical("STOPPING SCRIPT: Training data fetch timed out. Cannot proceed without authentic data.")
            return

        logging.info(f"Fetching train data for {ticker} (elapsed: {elapsed_train_time:.1f}s)")

        # Add timeout protection around individual ticker fetch
        def fetch_train_ticker():
            return fetch_historical_data_yf_refactored(ticker, TRAIN_START_DATE, TRAIN_END_DATE, interval=INTERVAL, operation_type='training')

        # Use timeout wrapper with enhanced timeout per ticker (matching SPY phase 1.py)
        if ticker == '^VIX':
            ticker_timeout = 90   # 1.5 minutes for VIX (increased from 60s)
        elif ticker == '^VIX3M':
            ticker_timeout = 180  # 3 minutes for VIX3M (increased from 120s due to persistent timeouts)
        elif ticker in ['^IRX', '^TNX']:
            ticker_timeout = 120  # 2 minutes for treasury rates (increased from 90s)

        else:
            ticker_timeout = 90   # 1.5 minutes for others (increased from 60s)
        df, fetch_error = SPY.fetch_with_timeout(fetch_train_ticker, timeout=ticker_timeout)

        if fetch_error is not None:
            logging.error(f"TRAIN FETCH FAILED: {ticker} timed out after {ticker_timeout}s: {fetch_error}")
            logging.critical(f"STOPPING SCRIPT: Cannot fetch authentic data for {ticker}. Script will not proceed with incomplete data.")
            return

        # Validate that we got authentic data (not empty)
        if df is None or df.empty:
            logging.error(f"TRAIN DATA VALIDATION FAILED: {ticker} returned empty data")
            logging.critical(f"STOPPING SCRIPT: No authentic data available for {ticker}. Cannot proceed without complete dataset.")
            return

        # Additional validation for critical tickers
        if ticker in [UNDERLYING_TICKER, '^VIX', '^IRX', '^TNX']:
            if len(df) < 100:  # Require minimum data points for critical tickers
                logging.error(f"TRAIN DATA VALIDATION FAILED: {ticker} has insufficient data ({len(df)} rows, minimum 100 required)")
                logging.critical(f"STOPPING SCRIPT: Insufficient authentic data for critical ticker {ticker}. Cannot proceed.")
                return

        data_dict_train[ticker] = df
        logging.info(f"SUCCESS: Validated authentic training data for {ticker}: {len(df)} rows")

        # Add small delay between tickers to reduce API pressure
        if ticker != ALL_TICKERS[-1]:  # Don't delay after the last ticker
            time.sleep(2)  # 2-second delay between training data fetches

    # Final validation: ensure all critical tickers have data
    critical_tickers = [UNDERLYING_TICKER, '^VIX', '^IRX', '^TNX']
    for ticker in critical_tickers:
        if ticker not in data_dict_train or data_dict_train[ticker].empty:
            logging.error(f"CRITICAL TICKER MISSING: {ticker} not in training data or empty")
            logging.critical(f"STOPPING SCRIPT: Critical ticker {ticker} missing from training data. Cannot proceed without complete authentic dataset.")
            return

    logging.info("SUCCESS: All training data validated as authentic and complete")

    # Create and validate training features
    logging.info("Creating combined train features...")
    try:
        features_train = create_combined_features(data_dict_train)
    except Exception as e:
        logging.error(f"Failed to create combined features from training data: {e}")
        logging.critical("STOPPING SCRIPT: Cannot create features from authentic training data. Script will not proceed.")
        return

    if features_train.empty:
        logging.critical("STOPPING SCRIPT: Training features are empty. Cannot proceed without valid features from authentic data.")
        return

    if features_train.shape[0] < WINDOW_SIZE + MIN_PERIODS_INDICATORS:
        logging.critical(f"STOPPING SCRIPT: Training features insufficient ({features_train.shape[0]} rows, minimum {WINDOW_SIZE + MIN_PERIODS_INDICATORS} required). Cannot proceed without adequate authentic data.")
        return

    logging.info(f"SUCCESS: Training features created and validated: {features_train.shape[0]} rows, {features_train.shape[1]} columns")
    logging.info("Creating train environment...");
    def make_train_env_fn():
        # Call create_trading_environment without unwrap_env_func
        env = create_trading_environment(features_train, initial_cash=INITIAL_CASH, window_size=WINDOW_SIZE, action_scheme_config=action_scheme_config if 'action_scheme_config' in locals() else None)
        return Monitor(env, SB3_LOG_DIR) if env else None
    vec_env=DummyVecEnv([make_train_env_fn])
    if not hasattr(vec_env.envs[0],'env') or vec_env.envs[0].env is None: logging.critical("Failed to create base train env."); return
    # Enable observation normalization with proper configuration
    # VecNormalize helps stabilize training by normalizing observations and rewards
    norm_env=VecNormalize(vec_env,norm_obs=True,norm_reward=True,clip_obs=10.,clip_reward=10.); logging.info("Train env wrapped with observation normalization ENABLED.")

    eval_callback=None; eval_norm_env_closure=None
    try:
        logging.info("Preparing eval data..."); data_dict_eval={}; fetch_ok_eval=True
        eval_fetch_start_time = time.time()
        max_eval_fetch_time = 600  # Maximum 10 minutes for all evaluation data fetching

        for ticker in ALL_TICKERS:
            # Check if we've exceeded the total time limit for evaluation data fetching
            elapsed_eval_time = time.time() - eval_fetch_start_time
            if elapsed_eval_time > max_eval_fetch_time:
                logging.error(f"EVAL FETCH TIMEOUT: Exceeded {max_eval_fetch_time}s total time for evaluation data. Skipping remaining tickers.")
                fetch_ok_eval = False
                break

            logging.info(f"Fetching eval data for {ticker} (elapsed: {elapsed_eval_time:.1f}s)")

            # Add timeout protection around individual ticker fetch
            def fetch_eval_ticker():
                return fetch_historical_data_yf_refactored(ticker, EVAL_START_DATE, EVAL_END_DATE, interval=INTERVAL, operation_type='evaluation')

            # Use timeout wrapper with REDUCED timeout per ticker to prevent hanging during optimization
            # OPTIMIZATION FIX: Reduced timeout configuration to prevent trials from hanging
            if ticker == '^VIX':
                ticker_timeout = 30   # Reduced from 90s to 30s to prevent hanging
            elif ticker == '^VIX3M':
                ticker_timeout = 45   # Reduced from 180s to 45s to prevent hanging
            elif ticker in ['^IRX', '^TNX']:
                ticker_timeout = 30   # Reduced from 120s to 30s to prevent hanging

            else:
                ticker_timeout = 25   # Reduced from 90s to 25s to prevent hanging
            df, fetch_error = SPY.fetch_with_timeout(fetch_eval_ticker, timeout=ticker_timeout)

            if fetch_error is not None:
                logging.error(f"EVAL FETCH FAILED: {ticker} timed out after {ticker_timeout}s: {fetch_error}")
                logging.critical(f"STOPPING SCRIPT: Cannot fetch authentic evaluation data for {ticker}. Script will not proceed with incomplete data.")
                return

            # Validate that we got authentic data (not empty)
            if df is None or df.empty:
                logging.error(f"EVAL DATA VALIDATION FAILED: {ticker} returned empty data")
                logging.critical(f"STOPPING SCRIPT: No authentic evaluation data available for {ticker}. Cannot proceed without complete dataset.")
                return

            # Additional validation for critical tickers
            if ticker in [UNDERLYING_TICKER, '^VIX', '^IRX', '^TNX']:
                if len(df) < 50:  # Require minimum data points for critical tickers (less than training since eval period is shorter)
                    logging.error(f"EVAL DATA VALIDATION FAILED: {ticker} has insufficient data ({len(df)} rows, minimum 50 required)")
                    logging.critical(f"STOPPING SCRIPT: Insufficient authentic evaluation data for critical ticker {ticker}. Cannot proceed.")
                    return

            data_dict_eval[ticker] = df
            logging.info(f"SUCCESS: Validated authentic evaluation data for {ticker}: {len(df)} rows")

            # Add small delay between tickers to reduce API pressure
            if ticker != ALL_TICKERS[-1]:  # Don't delay after the last ticker
                time.sleep(2)  # 2-second delay between evaluation data fetches
        if fetch_ok_eval:
            try:
                eval_features=create_combined_features(data_dict_eval)
            except Exception as e:
                logging.error(f"Failed to create combined features for evaluation data: {e}")
                logging.warning("Skipping evaluation callback due to feature creation failure.")
                eval_features = pd.DataFrame()  # Empty DataFrame to trigger skip

            if not eval_features.empty and eval_features.shape[0]>WINDOW_SIZE:
                def make_eval_env_fn():
                    # Use the global WINDOW_SIZE constant directly
                    env=create_trading_environment(eval_features, initial_cash=INITIAL_CASH, window_size=WINDOW_SIZE, action_scheme_config=action_scheme_config if 'action_scheme_config' in locals() else None)
                    return Monitor(env) if env else None # No logging dir for basic eval env?
                eval_vec_env_closure=DummyVecEnv([make_eval_env_fn])
                if hasattr(eval_vec_env_closure.envs[0],'env') and eval_vec_env_closure.envs[0].env is not None:
                    # Enable observation normalization for eval environment
                    eval_norm_env_closure=VecNormalize(eval_vec_env_closure,training=False,norm_obs=True,norm_reward=True); logging.info("Eval env created with observation normalization ENABLED.")
                    eval_callback=EvalCallback(eval_norm_env_closure,best_model_save_path=MODEL_DIR,log_path=SB3_LOG_DIR,eval_freq=max(TOTAL_TIMESTEPS//EVAL_FREQ_DIVISOR,1000),n_eval_episodes=N_EVAL_EPISODES,deterministic=True,render=False,verbose=1); logging.info("EvalCallback created.")
                else: logging.warning("Failed to create base eval env.")
            else: logging.warning("Eval features empty/insufficient. Skip EvalCallback.")
    except Exception as e: logging.error(f"Error setting up eval env: {e}",exc_info=True); eval_callback=None; eval_norm_env_closure=None

    # Load best hyperparameters if requested
    optimized_config = None
    reward_scheme_config = None

    if args.use_optimized and not args.optimize:
        # Find the best hyperparameters file
        script_dir = os.path.dirname(os.path.abspath(__file__))

        # Define potential paths for the best hyperparameters file
        potential_paths = [
            os.path.join(script_dir, "rl_logs_yfinance_options_v3.1", "best_hyperparameters.json"),
            os.path.join(script_dir, "sb3_logs", "best_hyperparameters.json")
        ]

        best_params_found = False
        for path in potential_paths:
            if os.path.exists(path):
                try:
                    with open(path, 'r') as f:
                        best_params = json.load(f)
                        logging.info(f"Loaded best hyperparameters from {path}: {best_params}")
                        best_params_found = True

                        # Extract reward scheme config if present
                        if 'reward_scheme_config' in best_params:
                            reward_scheme_config = best_params['reward_scheme_config']
                            logging.info(f"Using optimized reward scheme config: {reward_scheme_config}")

                        # Extract action scheme config if present
                        action_scheme_config = None
                        if 'action_scheme_config' in best_params:
                            action_scheme_config = best_params['action_scheme_config']
                            logging.info(f"Using optimized action scheme config: {action_scheme_config}")

                        logging.info(f"Using global WINDOW_SIZE ({WINDOW_SIZE}) for environment creation.")

                        # Create optimized_config for later use
                        optimized_config = best_params
                        break
                except Exception as e:
                    logging.error(f"Error loading hyperparameters from {path}: {e}")

        if not best_params_found:
            logging.warning("Could not find best hyperparameters file. Using default parameters.")

    # Run optimization if requested
    if args.optimize:
        logging.info("--- Starting Hyperparameter Optimization with Optuna ---")
        try:
            if not args.storage and not os.path.exists(SB3_LOG_DIR):
                os.makedirs(SB3_LOG_DIR, exist_ok=True)

            # Run optimization
            optimized_config = optimize_hyperparameters(
                train_features_df=features_train,
                eval_features_df=eval_features,
                n_trials=args.n_trials,
                study_name=args.study_name,
                storage=args.storage
            )

            if optimized_config:
                # Extract parameters from optimization results
                if 'reward_scheme_config' in optimized_config:
                    reward_scheme_config = optimized_config['reward_scheme_config']

                action_scheme_config = None
                if 'action_scheme_config' in optimized_config:
                    action_scheme_config = optimized_config['action_scheme_config']
                logging.info(f"Using global WINDOW_SIZE ({WINDOW_SIZE}) after optimization.")

                # Save optimization results
                try:
                    # Ensure SB3_LOG_DIR is an absolute path for clarity and create if not exists
                    abs_sb3_log_dir = os.path.abspath(SB3_LOG_DIR)
                    if not os.path.isdir(abs_sb3_log_dir): # Check if it's a directory
                        logging.warning(f"Target log directory {abs_sb3_log_dir} does not exist or is not a directory. Attempting to create it.")
                        os.makedirs(abs_sb3_log_dir, exist_ok=True)

                    save_path = os.path.join(abs_sb3_log_dir, "best_hyperparameters.json")
                    logging.info(f"Attempting to save best hyperparameters to absolute path: {save_path}")

                    # Check if optimized_config is valid before attempting to dump
                    if optimized_config is None:
                        logging.error("Optimized_config is None. Cannot save best hyperparameters.")
                    elif not isinstance(optimized_config, dict):
                        logging.error(f"Optimized_config is not a dictionary (type: {type(optimized_config)}). Cannot save.")
                    else:
                        with open(save_path, "w") as f:
                            json.dump(optimized_config, f, indent=4)

                        # Immediate verification
                        if os.path.exists(save_path):
                            logging.info(f"VERIFIED: Best hyperparameters file exists at {save_path}")
                            file_size = os.path.getsize(save_path)
                            logging.info(f"File size: {file_size} bytes.")
                            if file_size == 0:
                                logging.warning(f"WARNING: File {save_path} was created but is EMPTY.")
                        else:
                            logging.error(f"CRITICAL FAILURE: File {save_path} NOT FOUND immediately after saving attempt.")
                            logging.error(f"  Current Working Directory: {os.getcwd()}")
                            logging.error(f"  SB3_LOG_DIR (original): {SB3_LOG_DIR}")
                            logging.error(f"  Resolved abs_sb3_log_dir: {abs_sb3_log_dir}")
                except Exception as e:
                    logging.error(f"EXCEPTION during saving best hyperparameters: {e}", exc_info=True)
                    # Also log CWD here in case of error
                    logging.error(f"  CWD during exception: {os.getcwd()}")
                    logging.error(f"  SB3_LOG_DIR (original) during exception: {SB3_LOG_DIR}")

        except Exception as e:
            logging.error(f"Error during hyperparameter optimization: {e}", exc_info=True)

    # Return early if only optimization was requested
    if args.optimize and not args.train:
        logging.info("Optimization completed. Exiting without training.")
        return

    if optimized_config:
        total_timesteps = optimized_config.get("total_timesteps", TOTAL_TIMESTEPS)
        reward_scheme_config = optimized_config.get("reward_scheme_config", None)

        # Close existing environments
        if 'norm_env' in locals() and norm_env:
            norm_env.close()
        if 'eval_norm_env_closure' in locals() and eval_norm_env_closure:
            eval_norm_env_closure.close()

        # Recreate training environment with reward_scheme_config
        logging.info(f"Recreating environments with global WINDOW_SIZE ({WINDOW_SIZE}) and optimized reward scheme")
        def make_train_env_fn():
            env = create_trading_environment(
                features_train,
                initial_cash=INITIAL_CASH,
                window_size=WINDOW_SIZE,
                reward_scheme_config=reward_scheme_config,
                action_scheme_config=action_scheme_config
            )
            return Monitor(env, SB3_LOG_DIR) if env else None

        vec_env = DummyVecEnv([make_train_env_fn])
        if not hasattr(vec_env.envs[0], 'env') or vec_env.envs[0].env is None:
            logging.critical("Failed to create base train env with optimized parameters.")
            return

        # Enable observation normalization for optimized training
        norm_env = VecNormalize(vec_env, norm_obs=True, norm_reward=True, clip_obs=10., clip_reward=10.)
        logging.info("Recreated training environment with optimized parameters.")

        # Recreate evaluation environment if it exists
        if 'eval_features' in locals() and not eval_features.empty and eval_features.shape[0] > WINDOW_SIZE:
            def make_eval_env_fn():
                env = create_trading_environment(
                    eval_features,
                    initial_cash=INITIAL_CASH,
                    window_size=WINDOW_SIZE,
                    reward_scheme_config=reward_scheme_config,
                    action_scheme_config=action_scheme_config
                )
                return Monitor(env) if env else None

            eval_vec_env_closure = DummyVecEnv([make_eval_env_fn])
            if hasattr(eval_vec_env_closure.envs[0], 'env') and eval_vec_env_closure.envs[0].env is not None:
                # Enable observation normalization for optimized eval environment
                eval_norm_env_closure = VecNormalize(
                    eval_vec_env_closure,
                    training=False,
                    norm_obs=True,
                    norm_reward=True
                )
                logging.info("Recreated evaluation environment with optimized parameters.")

                eval_callback = EvalCallback(
                    eval_norm_env_closure,
                    best_model_save_path=MODEL_DIR,
                    log_path=SB3_LOG_DIR,
                    eval_freq=max(total_timesteps // EVAL_FREQ_DIVISOR, 1000),
                    n_eval_episodes=N_EVAL_EPISODES,
                    deterministic=True,
                    render=False,
                    verbose=1
                )
                logging.info("Recreated EvalCallback with optimized parameters.")

    logging.info("--- Starting Model Training ---"); training_start_time=time.time(); final_model_path=os.path.join(MODEL_DIR,SB3_MODEL_FILENAME)
    try:
        # Get PPO hyperparameters from optimized config if available
        # Always use the constant TOTAL_TIMESTEPS value defined in the code
        total_timesteps = TOTAL_TIMESTEPS
        logging.info(f"Using TOTAL_TIMESTEPS from code constant: {total_timesteps:,}")

        ppo_hyperparameters = {
            "n_epochs": 10,
            "gamma": 0.99,
            "gae_lambda": 0.95,
            "clip_range": 0.2,
            "ent_coef": 0.01,
            "vf_coef": 0.5
        }

        if optimized_config and 'ppo_hyperparameters' in optimized_config:
            ppo_hyperparameters = optimized_config['ppo_hyperparameters']
            logging.info(f"Using optimized PPO hyperparameters: {ppo_hyperparameters}")
        else:
            logging.info(f"Using default PPO hyperparameters: {ppo_hyperparameters}")

        # CRITICAL FIX: Enhanced PPO model creation with debugging
        logging.info("=== CREATING PPO MODEL ===")
        logging.info(f"Environment observation space: {norm_env.observation_space}")
        logging.info(f"Environment action space: {norm_env.action_space}")

        # Verify environment is properly set up before creating model
        try:
            test_reset = norm_env.reset()
            logging.info(f"Environment reset test successful. Shape: {test_reset.shape if hasattr(test_reset, 'shape') else 'N/A'}")

            # Test a single step to ensure environment works
            test_action = norm_env.action_space.sample()
            test_step = norm_env.step([test_action])
            logging.info(f"Environment step test successful. Result length: {len(test_step)}")
        except Exception as e:
            logging.error(f"Environment test failed: {e}", exc_info=True)
            raise RuntimeError(f"Environment is not properly configured for PPO: {e}")

        # CRITICAL FIX: Remove explicit learning_rate to avoid conflict with **ppo_hyperparameters
        # Create a copy of ppo_hyperparameters and add any missing default values
        ppo_params = ppo_hyperparameters.copy()

        # Ensure all required parameters have default values if not in optimized config
        if "learning_rate" not in ppo_params:
            ppo_params["learning_rate"] = 0.00025

        model = PPO(
            "MlpPolicy",
            norm_env,
            policy_kwargs=dict(
                net_arch=dict(pi=[512, 256, 128], vf=[512, 256, 128]),
                activation_fn=torch.nn.ReLU,
                ortho_init=True  # Better initialization
            ),
            n_steps=2048,
            batch_size=128,
            **ppo_params,  # Unpack all optimized hyperparameters (including learning_rate)
            max_grad_norm=0.5,
            verbose=1,  # CRITICAL: Enable verbose output to see training progress
            tensorboard_log=SB3_LOG_DIR,
            seed=42,
            device="auto"
        )

        # Set model reference in action scheme for confidence extraction
        try:
            base_env = unwrap_env(norm_env)
            if hasattr(base_env, 'action_scheme') and hasattr(base_env.action_scheme, 'set_model_for_confidence'):
                base_env.action_scheme.set_model_for_confidence(model)
                logging.info("Set model reference in action scheme for confidence extraction during training")
        except Exception as e:
            logging.warning(f"Could not set model reference in action scheme during training: {e}")

        logging.info(f"PPO model created successfully:")
        logging.info(f"  Policy: {model.policy}")
        logging.info(f"  Device: {model.device}")
        logging.info(f"  Learning rate: {model.learning_rate}")
        logging.info(f"  N steps: {model.n_steps}")
        logging.info(f"  Batch size: {model.batch_size}")
        logging.info("=== PPO MODEL CREATION COMPLETE ===")

        if eval_norm_env_closure and eval_callback:
            logging.info("Syncing initial norm stats to eval env...")
            # Ensure consistent normalization between training and evaluation environments
            try:
                # Check if both environments have VecNormalize wrappers and normalization is enabled
                if hasattr(norm_env, 'obs_rms') and hasattr(eval_norm_env_closure, 'obs_rms'):
                    eval_norm_env_closure.obs_rms = norm_env.obs_rms
                    logging.info("Synchronized obs_rms statistics")
                elif norm_env.norm_obs and eval_norm_env_closure.norm_obs:
                    logging.warning("Both environments have norm_obs=True but obs_rms attributes not found")
                else:
                    logging.info("Observation normalization disabled - skipping obs_rms sync")

                if hasattr(norm_env, 'ret_rms') and hasattr(eval_norm_env_closure, 'ret_rms'):
                    eval_norm_env_closure.ret_rms = norm_env.ret_rms
                    logging.info("Synchronized ret_rms statistics")
                elif norm_env.norm_reward and eval_norm_env_closure.norm_reward:
                    logging.warning("Both environments have norm_reward=True but ret_rms attributes not found")
                else:
                    logging.info("Reward normalization disabled - skipping ret_rms sync")

                # Sync other normalization parameters that should always exist
                if hasattr(norm_env, 'clip_obs') and hasattr(eval_norm_env_closure, 'clip_obs'):
                    eval_norm_env_closure.clip_obs = norm_env.clip_obs
                if hasattr(norm_env, 'clip_reward') and hasattr(eval_norm_env_closure, 'clip_reward'):
                    eval_norm_env_closure.clip_reward = norm_env.clip_reward
                if hasattr(norm_env, 'norm_obs') and hasattr(eval_norm_env_closure, 'norm_obs'):
                    eval_norm_env_closure.norm_obs = norm_env.norm_obs
                if hasattr(norm_env, 'norm_reward') and hasattr(eval_norm_env_closure, 'norm_reward'):
                    eval_norm_env_closure.norm_reward = norm_env.norm_reward

                # Always set evaluation mode
                if hasattr(eval_norm_env_closure, 'training'):
                    eval_norm_env_closure.training = False

                logging.info("Successfully synchronized available normalization parameters between environments")
            except AttributeError as e:
                logging.warning(f"Could not sync some normalization attributes: {e}")
                logging.info("Continuing with training - environments may have different normalization settings")
            except Exception as e:
                logging.error(f"Unexpected error during normalization sync: {e}")
                logging.info("Continuing with training - normalization sync failed but training can proceed")

        # CRITICAL FIX: Debug PPO training setup before starting
        logging.info("=== PPO TRAINING SETUP VERIFICATION ===")
        logging.info(f"Model type: {type(model).__name__}")
        logging.info(f"Environment type: {type(norm_env).__name__}")
        logging.info(f"Environment observation space: {norm_env.observation_space}")
        logging.info(f"Environment action space: {norm_env.action_space}")
        logging.info(f"Model policy: {model.policy}")
        logging.info(f"Model device: {model.device}")
        logging.info(f"Total timesteps to train: {total_timesteps:,}")

        # CRITICAL FIX: Remove environment testing that was causing premature execution
        # The environment test was triggering the environment to start running and never return control
        # PPO will handle environment initialization properly during model.learn()
        logging.info("Skipping environment pre-test to prevent premature execution")
        logging.info("PPO will initialize and test the environment during model.learn()")

        # CRITICAL FIX: Ensure PPO verbose mode is enabled to see training progress
        model.verbose = 1

        logging.info("=== STARTING PPO TRAINING ===")
        logging.info(f"Starting PPO training for {total_timesteps:,} timesteps...")

        # CRITICAL FIX: Use minimal callback setup to avoid interference
        callbacks_to_use = []
        if eval_callback is not None:
            logging.info("Adding EvalCallback to training")
            callbacks_to_use.append(eval_callback)
        else:
            logging.warning("No EvalCallback available - training without evaluation")

        try:
            logging.info("CALLING model.learn() NOW...")
            model.learn(
                total_timesteps=total_timesteps,
                callback=callbacks_to_use if callbacks_to_use else None,
                progress_bar=True,
                tb_log_name=f"PPO_Option_{UNDERLYING_TICKER}_{SCRIPT_VERSION_TAG}",
                reset_num_timesteps=True  # Ensure clean start
            )
            logging.info("=== PPO TRAINING COMPLETED SUCCESSFULLY ===")
        except Exception as training_error:
            logging.critical(f"CRITICAL: PPO training failed: {training_error}", exc_info=True)
            logging.critical("PPO training failure is FATAL - stopping execution")
            # Force immediate exit to prevent script from continuing
            import sys
            sys.exit(1)
        training_duration=time.time()-training_start_time; logging.info(f"Training finished in {training_duration:.2f}s.")
        # Save model and normalization stats separately (legacy approach)
        model.save(final_model_path)
        norm_env.save(NORMALIZE_STATS_FILENAME)
        logging.info(f"Final model saved: {final_model_path}")
        logging.info(f"Norm stats saved: {NORMALIZE_STATS_FILENAME}")

        # Save model with normalization stats together (improved approach)
        save_model_with_norm_stats(model, norm_env, os.path.join(MODEL_WITH_STATS_DIR, f"model_with_stats_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"))
        latest_model_path = os.path.join(MODEL_WITH_STATS_DIR, "latest_model_with_stats.zip")
        save_model_with_norm_stats(model, norm_env, latest_model_path)
        logging.info(f"Model with normalization stats saved: {latest_model_path}")
    except Exception as e: logging.critical(f"Error during training: {e}",exc_info=True)
    finally:
        if 'norm_env' in locals() and norm_env: norm_env.close()
        if 'eval_norm_env_closure' in locals() and eval_norm_env_closure: eval_norm_env_closure.close()

        logging.info("--- Starting Final Evaluation & Analysis ---"); evaluation_start_time=time.time(); final_eval_norm_env_closure = None
        use_combined_approach = False
        loaded_stats = None
        try:
            best_model_path=os.path.join(MODEL_DIR,"best_model.zip"); model_to_load_path=best_model_path if os.path.exists(best_model_path) else final_model_path
            # First try to load the combined model+stats file
            latest_combined_path = os.path.join(MODEL_WITH_STATS_DIR, "latest_model_with_stats.zip")
            if os.path.exists(latest_combined_path):
                logging.info(f"Loading model with bundled normalization stats: {latest_combined_path}")
                model, loaded_stats = load_model_with_norm_stats(latest_combined_path)
                if model and loaded_stats:
                    # We'll use the combined model+stats approach
                    use_combined_approach = True
                else:
                    logging.warning(f"Failed to load combined model+stats, falling back to separate files")
                    use_combined_approach = False
            else:
                logging.info(f"No combined model+stats file found at {latest_combined_path}, using separate files")
                use_combined_approach = False

            if not use_combined_approach:
                # Traditional approach with separate files
                if not os.path.exists(model_to_load_path):
                    logging.error(f"No model at {model_to_load_path}.")
                    return
                if not os.path.exists(NORMALIZE_STATS_FILENAME):
                    logging.error(f"Stats not found: {NORMALIZE_STATS_FILENAME}.")
                    return
            logging.info("Fetching final eval data..."); data_dict_final_eval={}; fetch_ok_final_eval=True
            final_eval_fetch_start_time = time.time()
            max_final_eval_fetch_time = 600  # Maximum 10 minutes for final evaluation data fetching

            for ticker in ALL_TICKERS:
                # Check if we've exceeded the total time limit for final evaluation data fetching
                elapsed_final_eval_time = time.time() - final_eval_fetch_start_time
                if elapsed_final_eval_time > max_final_eval_fetch_time:
                    logging.error(f"FINAL EVAL FETCH TIMEOUT: Exceeded {max_final_eval_fetch_time}s total time. Skipping remaining tickers.")
                    fetch_ok_final_eval = False
                    break

                logging.info(f"Fetching final eval data for {ticker} (elapsed: {elapsed_final_eval_time:.1f}s)")

                # Add timeout protection around individual ticker fetch
                def fetch_final_eval_ticker():
                    return fetch_historical_data_yf_refactored(ticker, EVAL_START_DATE, EVAL_END_DATE, interval=INTERVAL, operation_type='evaluation')

                # Use timeout wrapper with enhanced timeout per ticker (matching SPY phase 1.py)
                # CRITICAL FIX: Consistent optimized timeout configuration for final evaluation
                if ticker == '^VIX':
                    ticker_timeout = 90   # 1.5 minutes for VIX (consistent with training/eval)
                elif ticker == '^VIX3M':
                    ticker_timeout = 180  # 3 minutes for VIX3M (consistent with training/eval)
                elif ticker in ['^IRX', '^TNX']:
                    ticker_timeout = 120  # 2 minutes for treasury rates (consistent with training/eval)

                else:
                    ticker_timeout = 90   # 1.5 minutes for others (consistent with training/eval)
                df, fetch_error = SPY.fetch_with_timeout(fetch_final_eval_ticker, timeout=ticker_timeout)

                if fetch_error is not None:
                    logging.error(f"FINAL EVAL FETCH FAILED: {ticker} timed out after {ticker_timeout}s: {fetch_error}")
                    logging.critical(f"STOPPING SCRIPT: Cannot fetch authentic final evaluation data for {ticker}. Script will not proceed with incomplete data.")
                    return

                # Validate that we got authentic data (not empty)
                if df is None or df.empty:
                    logging.error(f"FINAL EVAL DATA VALIDATION FAILED: {ticker} returned empty data")
                    logging.critical(f"STOPPING SCRIPT: No authentic final evaluation data available for {ticker}. Cannot proceed without complete dataset.")
                    return

                # Additional validation for critical tickers
                if ticker in [UNDERLYING_TICKER, '^VIX', '^IRX', '^TNX']:
                    if len(df) < 50:  # Require minimum data points for critical tickers
                        logging.error(f"FINAL EVAL DATA VALIDATION FAILED: {ticker} has insufficient data ({len(df)} rows, minimum 50 required)")
                        logging.critical(f"STOPPING SCRIPT: Insufficient authentic final evaluation data for critical ticker {ticker}. Cannot proceed.")
                        return

                data_dict_final_eval[ticker] = df
                logging.info(f"SUCCESS: Validated authentic final evaluation data for {ticker}: {len(df)} rows")

                # Add small delay between tickers to reduce API pressure
                if ticker != ALL_TICKERS[-1]:  # Don't delay after the last ticker
                    time.sleep(2)  # 2-second delay between final evaluation data fetches
            if not fetch_ok_final_eval: return
            try:
                eval_features_final=create_combined_features(data_dict_final_eval)
            except Exception as e:
                logging.error(f"Failed to create combined features for final evaluation data: {e}")
                logging.error("Cannot proceed with final evaluation due to feature creation failure.")
                return

            if eval_features_final.empty or eval_features_final.shape[0] < 2: # Ensure at least 2 rows for one step
                logging.error("Final eval features empty or too short (less than 2 rows) for QuantStats. Skipping report.")
                return
            logging.info(f"Loading model for final eval: {final_model_path}")
            # Get window size from best hyperparameters file for evaluation
            eval_window_size = WINDOW_SIZE
            try:
                hyperparams_path = os.path.join(SB3_LOG_DIR, 'best_hyperparameters.json')
                if os.path.exists(hyperparams_path) and args.use_optimized:
                    with open(hyperparams_path, 'r') as f:
                        best_params = json.load(f)
                        if 'window_size' in best_params:
                            eval_window_size = best_params['window_size']
                            logging.info(f"Using optimized window_size for evaluation: {eval_window_size}")
            except Exception as e:
                logging.error(f"Error loading window size from hyperparameters: {e}")
                logging.info(f"Falling back to default window size: {WINDOW_SIZE}")

            def make_final_eval_env_fn():
                # CRITICAL FIX: Ensure observation space consistency between model and environment
                nonlocal eval_features_final  # Allow modification of the outer variable

                if use_combined_approach and model:
                    model_obs_shape = model.observation_space.shape[0] if hasattr(model.observation_space, 'shape') else None
                    if isinstance(model_obs_shape, int):
                        expected_obs_size = model_obs_shape
                        n_static_features = N_STATIC_PORTFOLIO_FEATURES + N_STATIC_HELD_OPTION_FEATURES
                        implied_n_market_features = (expected_obs_size - n_static_features) // eval_window_size
                        current_n_market_features = eval_features_final.shape[1]

                        logging.info(f"OBSERVATION SPACE VALIDATION: Model expects {expected_obs_size} total features")
                        logging.info(f"  - Static features: {n_static_features}")
                        logging.info(f"  - Window size: {eval_window_size}")
                        logging.info(f"  - Implied market features: {implied_n_market_features}")
                        logging.info(f"  - Current market features: {current_n_market_features}")

                        if current_n_market_features != implied_n_market_features:
                            if current_n_market_features < implied_n_market_features:
                                logging.warning(f"CRITICAL FIX: Model expects {implied_n_market_features} market features but environment has {current_n_market_features}. Padding evaluation features.")
                                # Pad the evaluation features to match model expectations
                                missing_features = implied_n_market_features - current_n_market_features
                                padding_data = {}
                                for i in range(missing_features):
                                    padding_data[f'padding_feature_{i}'] = [0.0] * len(eval_features_final)
                                padding_df = pd.DataFrame(padding_data, index=eval_features_final.index)
                                eval_features_final = pd.concat([eval_features_final, padding_df], axis=1)
                                logging.info(f"Padded evaluation features from {current_n_market_features} to {eval_features_final.shape[1]} features")
                            else:
                                logging.warning(f"CRITICAL FIX: Environment has {current_n_market_features} market features but model expects {implied_n_market_features}. Truncating features.")
                                # Keep only the first implied_n_market_features columns
                                feature_cols = eval_features_final.columns[:implied_n_market_features]
                                eval_features_final = eval_features_final[feature_cols]
                                logging.info(f"Truncated evaluation features from {current_n_market_features} to {eval_features_final.shape[1]} features")

                env = create_trading_environment(eval_features_final, initial_cash=INITIAL_CASH, window_size=eval_window_size, action_scheme_config=action_scheme_config)

                # CRITICAL FIX: Validate environment observation space matches model expectations
                if env and model:
                    env_obs_shape = env.observation_space.shape[0] if hasattr(env.observation_space, 'shape') else None
                    if env_obs_shape != model_obs_shape:
                        error_msg = f"OBSERVATION SPACE MISMATCH: Environment produces {env_obs_shape} features but model expects {model_obs_shape}"
                        logging.error(error_msg)
                        raise ValueError(error_msg)
                    else:
                        logging.info(f"OBSERVATION SPACE VALIDATED: Environment and model both use {env_obs_shape} features")

                return env if env else None

            final_eval_vec_env_closure = DummyVecEnv([make_final_eval_env_fn])

            # Store direct references to all environments for better debugging
            raw_env = None
            if hasattr(final_eval_vec_env_closure, 'envs') and len(final_eval_vec_env_closure.envs) > 0:
                gym_env = final_eval_vec_env_closure.envs[0]
                if gym_env is not None:
                    # Try to get direct access to the underlying TradingEnv
                    if hasattr(gym_env, '_trading_env'):
                        raw_env = gym_env._trading_env
                        logging.info(f"Stored direct reference to raw TradingEnv: {type(raw_env).__name__}")

                        # Update global state with environment components
                        if hasattr(raw_env, 'portfolio'):
                            global_state.portfolio = raw_env.portfolio
                            logging.info(f"Updated global_state.portfolio: {global_state.portfolio}")
                        if hasattr(raw_env, 'action_scheme'):
                            global_state.action_scheme = raw_env.action_scheme
                            logging.info("Updated global_state.action_scheme")
                        if hasattr(raw_env, 'reward_scheme'):
                            global_state.reward_scheme = raw_env.reward_scheme
                            logging.info("Updated global_state.reward_scheme")

            # Load the normalization stats with explicit error handling
            if not os.path.exists(NORMALIZE_STATS_FILENAME):
                logging.error(f"Normalization stats file not found at {NORMALIZE_STATS_FILENAME}.")
                return

            if not use_combined_approach:
                try:
                    final_eval_norm_env_closure = VecNormalize.load(NORMALIZE_STATS_FILENAME, final_eval_vec_env_closure)
                    final_eval_norm_env_closure.training = False
                    final_eval_norm_env_closure.norm_reward = False
                    logging.info("Successfully loaded normalization stats")

                    # Load the model with the loaded normalization environment
                    model = PPO.load(final_model_path, env=final_eval_norm_env_closure)
                    logging.info(f"Loaded model from {final_model_path}")
                except Exception as e:
                    logging.error(f"Error loading normalization stats or model: {e}")
                    return
            else:
                # Combined approach - we already loaded the model and stats above
                # Just need to apply the loaded stats to our evaluation environment
                try:
                    # Apply the loaded stats to the evaluation environment
                    apply_norm_stats_to_env(loaded_stats, final_eval_vec_env_closure)
                    final_eval_norm_env_closure = final_eval_vec_env_closure
                    final_eval_norm_env_closure.training = False
                    final_eval_norm_env_closure.norm_reward = False
                    logging.info("Successfully applied bundled normalization stats to evaluation environment")
                except Exception as e:
                    logging.error(f"Error applying normalization stats: {e}")
                    return
            logging.info("Model and norm stats loaded for final eval.")
            logging.info("Running one full episode for QuantStats...")

            # Try to access environment directly
            try:
                # Store direct references to all environments for better debugging
                raw_env = None
                if hasattr(final_eval_vec_env_closure, 'envs') and len(final_eval_vec_env_closure.envs) > 0:
                    gym_env = final_eval_vec_env_closure.envs[0]
                    if gym_env is not None:
                        # Try to get direct access to the underlying TradingEnv
                        if hasattr(gym_env, '_trading_env'):
                            raw_env = gym_env._trading_env
                            logging.info(f"Stored direct reference to raw TradingEnv: {type(raw_env).__name__}")

                            # Update global state with environment components
                            if hasattr(raw_env, 'portfolio'):
                                global_state.portfolio = raw_env.portfolio
                                logging.info(f"Updated global_state.portfolio: {global_state.portfolio}")
                            if hasattr(raw_env, 'action_scheme'):
                                global_state.action_scheme = raw_env.action_scheme
                                logging.info("Updated global_state.action_scheme")
                            if hasattr(raw_env, 'reward_scheme'):
                                global_state.reward_scheme = raw_env.reward_scheme
                                logging.info("Updated global_state.reward_scheme")

                # Use our unwrap_env function to get to the underlying TradingEnv
                # Try to use our previously stored reference first
                if raw_env is not None and hasattr(raw_env, 'reward_scheme'):
                    base_trading_env = raw_env
                    logging.info("Using direct reference to raw trading environment")
                else:
                    # Fall back to unwrapping
                    base_trading_env = unwrap_env(final_eval_vec_env_closure.envs[0])
                    logging.info(f"Used unwrap_env to get base environment: {type(base_trading_env).__name__}")

                # Make sure we have the reward scheme for resetting
                if not hasattr(base_trading_env, 'reward_scheme'):
                    raise AttributeError("No reward_scheme attribute found")

                if not isinstance(base_trading_env.reward_scheme, OptionRewardScheme):
                    raise TypeError("Reward scheme incorrect type")

                base_trading_env.reward_scheme.reset()
                logging.info("Reset OptionRewardScheme for final run.")
            except (AttributeError, TypeError, IndexError) as e:
                logging.error(f"Failed access reward scheme: {e}. No QuantStats.")
                return

            # ENHANCED: Reset environment and ensure global_state.conceptual_net_worth_history is properly initialized
            logging.info("=== STARTING EVALUATION ENVIRONMENT RESET ===")

            # CRITICAL FIX: Set evaluation mode FIRST before any environment operations
            logging.info("Setting global state to evaluation mode...")
            global_state.set_evaluation_mode(True)

            # Reset the environment FIRST (this will call OptionActionScheme.reset())
            logging.info("Resetting evaluation environment...")
            obs = final_eval_norm_env_closure.reset()
            max_eval_steps = len(eval_features_final) - 1

            # CRITICAL FIX: Do NOT clear global state history after environment reset
            # The environment reset already properly initialized the history via OptionActionScheme.reset()
            # Additional clearing here was causing the history to be stuck at length 1
            logging.info("Verifying global state history after environment reset...")
            logging.info(f"Global state history after env reset. Length: {len(global_state.conceptual_net_worth_history)}, Initial value: {global_state.conceptual_net_worth_history[0] if global_state.conceptual_net_worth_history else 'None'}")

            # Verify that the reset properly initialized the global state
            logging.info(f"Final verification - global_state.conceptual_net_worth_history length: {len(global_state.conceptual_net_worth_history)}")
            logging.info(f"Final verification - Initial value in history: {global_state.conceptual_net_worth_history[0] if global_state.conceptual_net_worth_history else 'None'}")

            # ENHANCED VALIDATION: Ensure we have a proper starting point
            if not global_state.conceptual_net_worth_history or len(global_state.conceptual_net_worth_history) == 0:
                logging.warning("Global state history is empty after reset. Manually initializing...")
                global_state.conceptual_net_worth_history = [INITIAL_CASH]
                logging.info(f"Manually initialized global state with: {global_state.conceptual_net_worth_history}")

            logging.info("=== EVALUATION ENVIRONMENT RESET COMPLETE ===")

            if max_eval_steps > 0:
                logging.info(f"Starting QuantStats final eval loop for {max_eval_steps} steps...")
                current_loop_step = 0 # Initialize loop counter
                for i in range(max_eval_steps):
                    current_loop_step = i + 1
                    action, _ = model.predict(obs, deterministic=True)
                    obs, _rews, dones, _infos = final_eval_norm_env_closure.step(action)

                    # CRITICAL FIX: Manually update global state during evaluation since action scheme skips updates in eval mode
                    # The action scheme's update_global_net_worth_history() now skips updates during evaluation mode
                    # to prevent double entries, so we need to handle updates manually here
                    current_value = None
                    update_success = False

                    # STRATEGY 1: Try to get action scheme from global state (most reliable)
                    try:
                        if hasattr(global_state, 'action_scheme') and global_state.action_scheme is not None:
                            if hasattr(global_state.action_scheme, 'get_current_portfolio_value'):
                                current_value = global_state.action_scheme.get_current_portfolio_value()
                                global_state.add_conceptual_net_worth(current_value)
                                update_success = True
                                logging.debug(f"EVALUATION MANUAL UPDATE (GLOBAL): Added {current_value:.2f} to global state history (length: {len(global_state.conceptual_net_worth_history)})")
                    except Exception as global_update_error:
                        logging.debug(f"STRATEGY 1 failed: {global_update_error}")

                    # STRATEGY 2: Try to get action scheme from environment wrapper
                    if not update_success:
                        try:
                            # Try different ways to access the action scheme
                            action_scheme = None

                            # Method 2a: Direct env access
                            if hasattr(final_eval_norm_env_closure, 'env'):
                                env = final_eval_norm_env_closure.env
                                if hasattr(env, 'action_scheme'):
                                    action_scheme = env.action_scheme

                            # Method 2b: Unwrap environment to get base env
                            if action_scheme is None:
                                try:
                                    base_env = unwrap_env(final_eval_norm_env_closure)
                                    if hasattr(base_env, 'action_scheme'):
                                        action_scheme = base_env.action_scheme
                                except:
                                    pass

                            # Method 2c: Try envs[0] if it's a vectorized environment
                            if action_scheme is None and hasattr(final_eval_norm_env_closure, 'envs'):
                                try:
                                    base_env = unwrap_env(final_eval_norm_env_closure.envs[0])
                                    if hasattr(base_env, 'action_scheme'):
                                        action_scheme = base_env.action_scheme
                                except:
                                    pass

                            if action_scheme and hasattr(action_scheme, 'get_current_portfolio_value'):
                                current_value = action_scheme.get_current_portfolio_value()
                                global_state.add_conceptual_net_worth(current_value)
                                update_success = True
                                logging.debug(f"EVALUATION MANUAL UPDATE (ENV): Added {current_value:.2f} to global state history (length: {len(global_state.conceptual_net_worth_history)})")
                        except Exception as env_update_error:
                            logging.debug(f"STRATEGY 2 failed: {env_update_error}")

                    # STRATEGY 3: Fallback to maintain history continuity
                    if not update_success:
                        logging.warning(f"EVALUATION MANUAL UPDATE: All strategies failed at step {current_loop_step}. Using fallback.")
                        if global_state.conceptual_net_worth_history:
                            current_value = global_state.conceptual_net_worth_history[-1]  # Use last known value
                            global_state.add_conceptual_net_worth(current_value)
                            logging.warning(f"EVALUATION FALLBACK UPDATE: Used last known value {current_value:.2f}")
                        else:
                            current_value = INITIAL_CASH
                            global_state.add_conceptual_net_worth(current_value)
                            logging.warning(f"EVALUATION FALLBACK UPDATE: Used initial cash {current_value:.2f}")

                    try:
                        # Only get current value for logging, don't update global state
                        base_env = unwrap_env(final_eval_norm_env_closure.envs[0])
                        if base_env and hasattr(base_env, 'action_scheme') and hasattr(base_env.action_scheme, 'get_current_portfolio_value'):
                            current_value = base_env.action_scheme.get_current_portfolio_value()
                            if current_loop_step % 50 == 0:  # Log every 50 steps to verify it's working
                                logging.info(f"Step {current_loop_step}: Portfolio value retrieved for logging: {current_value:.2f}")
                        elif hasattr(global_state, 'action_scheme') and global_state.action_scheme is not None:
                            if hasattr(global_state.action_scheme, 'get_current_portfolio_value'):
                                current_value = global_state.action_scheme.get_current_portfolio_value()
                                if current_loop_step % 50 == 0:
                                    logging.info(f"Step {current_loop_step}: Portfolio value retrieved from global state: {current_value:.2f}")
                        else:
                            # Fallback: use the last value from history
                            if len(global_state.conceptual_net_worth_history) > 0:
                                current_value = global_state.conceptual_net_worth_history[-1]
                                if current_loop_step % 50 == 0:
                                    logging.info(f"Step {current_loop_step}: Using last history value: {current_value:.2f}")

                    except Exception as update_error:
                        if current_loop_step % 50 == 0:  # Only log periodically to avoid spam
                            logging.error(f"Step {current_loop_step}: Exception during portfolio value retrieval: {update_error}", exc_info=True)

                    # ENHANCED FALLBACK: More sophisticated validation and recovery
                    if current_loop_step > 10:  # Allow some initial steps to establish pattern
                        current_history_length = len(global_state.conceptual_net_worth_history)
                        expected_length = current_loop_step + 1  # +1 for initial value

                        # Check if we're significantly behind in updates
                        if current_history_length < (expected_length * 0.5):  # Less than 50% of expected updates
                            logging.error(f"AUTHENTIC DATA VALIDATION FAILED: Global state updates consistently failing after {current_loop_step} steps.")
                            logging.error(f"Net worth history length: {current_history_length} (expected ~{expected_length})")
                            logging.error("Stopping evaluation early to prevent generation of invalid performance data.")
                            break  # Exit the evaluation loop early

                    # ENHANCED LOGGING: Log progress periodically with detailed tracking
                    if current_loop_step % 50 == 0 or current_loop_step == max_eval_steps:
                        current_history_length = len(global_state.conceptual_net_worth_history)
                        latest_net_worth = global_state.conceptual_net_worth_history[-1] if global_state.conceptual_net_worth_history else 0.0
                        expected_length = current_loop_step + 1  # +1 for initial value
                        update_success_rate = (current_history_length / expected_length) * 100 if expected_length > 0 else 0

                        logging.info(f"=== EVALUATION PROGRESS STEP {current_loop_step}/{max_eval_steps} ===")
                        logging.info(f"  Global state history length: {current_history_length} (expected: {expected_length})")
                        logging.info(f"  Update success rate: {update_success_rate:.1f}%")
                        logging.info(f"  Latest net worth: ${latest_net_worth:.2f}")
                        logging.info(f"  Environment step handled updates: True")
                        if current_value is not None:
                            logging.info(f"  Current portfolio value: ${current_value:.2f}")

                        # Log recent net worth changes for trend analysis
                        if current_history_length >= 2:
                            recent_change = global_state.conceptual_net_worth_history[-1] - global_state.conceptual_net_worth_history[-2]
                            logging.info(f"  Recent change: ${recent_change:.2f}")

                        logging.info("=== END EVALUATION PROGRESS ===")

                        # Additional validation for critical steps
                        if current_loop_step in [1, 10, 50, 100] or current_loop_step == max_eval_steps:
                            logging.info(f"CRITICAL STEP {current_loop_step} VALIDATION:")
                            logging.info(f"  Full net worth history: {global_state.conceptual_net_worth_history[:5]}...{global_state.conceptual_net_worth_history[-5:] if len(global_state.conceptual_net_worth_history) > 10 else global_state.conceptual_net_worth_history}")

                    if dones[0]:
                        # CRITICAL FIX: Preserve history BEFORE breaking out of loop
                        # The environment will auto-reset after episode completion, clearing the history
                        preserved_net_worth_history = list(global_state.conceptual_net_worth_history) if global_state.conceptual_net_worth_history else []
                        logging.info(f"PRESERVED: Saved {len(preserved_net_worth_history)} net worth entries before episode completion")
                        logging.info(f"Final eval episode done at QuantStats step {current_loop_step}.")
                        break
                logging.info(f"QuantStats run finished after {current_loop_step} steps.")

                # CRITICAL FIX: Preserve history if not already preserved (loop completed without done=True)
                if 'preserved_net_worth_history' not in locals():
                    preserved_net_worth_history = list(global_state.conceptual_net_worth_history) if global_state.conceptual_net_worth_history else []
                    logging.info(f"PRESERVED: Saved {len(preserved_net_worth_history)} net worth entries after loop completion")
            else:
                logging.warning(f"max_eval_steps is {max_eval_steps}, skipping QuantStats loop.")
                # Still need to preserve whatever history we have
                preserved_net_worth_history = list(global_state.conceptual_net_worth_history) if global_state.conceptual_net_worth_history else []
                logging.info(f"PRESERVED: Saved {len(preserved_net_worth_history)} net worth entries (no evaluation loop)")

            # CRITICAL FIX: Extract ONLY evaluation period performance data
            # Ensure we only report performance for the evaluation period, not training + evaluation
            final_net_worth_history = []
            eval_period_length = len(eval_features_final)

            logging.info(f"EVAL_ONLY_PERFORMANCE: Extracting evaluation-only performance data")
            logging.info(f"EVAL_ONLY_PERFORMANCE: Evaluation period length: {eval_period_length} data points")
            logging.info(f"EVAL_ONLY_PERFORMANCE: Date range: {EVAL_START_DATE} to {EVAL_END_DATE}")

            if (preserved_net_worth_history and
                isinstance(preserved_net_worth_history, list) and
                len(preserved_net_worth_history) >= 2):

                raw_history = list(preserved_net_worth_history)
                logging.info(f"EVAL_ONLY_PERFORMANCE: Raw history length: {len(raw_history)}")

                # CRITICAL: Extract ONLY the evaluation period data
                if len(raw_history) >= eval_period_length:
                    # Take the LAST eval_period_length entries (these are from evaluation period only)
                    final_net_worth_history = raw_history[-eval_period_length:]
                    logging.info(f"EVAL_ONLY_PERFORMANCE: Extracted {len(final_net_worth_history)} evaluation-only entries")
                    logging.info(f"EVAL_ONLY_PERFORMANCE: Evaluation period performance: ${final_net_worth_history[0]:.2f} -> ${final_net_worth_history[-1]:.2f}")

                    # Calculate evaluation period return
                    eval_return = ((final_net_worth_history[-1] / final_net_worth_history[0]) - 1) * 100
                    logging.info(f"EVAL_ONLY_PERFORMANCE: Evaluation period total return: {eval_return:.2f}%")
                elif len(raw_history) > 1:
                    # Use what we have, but warn about insufficient data
                    final_net_worth_history = raw_history
                    logging.warning(f"EVAL_ONLY_PERFORMANCE: Insufficient history ({len(raw_history)} < {eval_period_length}), using all available data")
                else:
                    # Fallback for insufficient data
                    final_net_worth_history = [INITIAL_CASH] * min(eval_period_length, 2)
                    logging.error(f"EVAL_ONLY_PERFORMANCE: Critical data shortage, using fallback history")
            else:
                # This should not happen if the environment was properly reset and steps were executed
                preserved_hist_len = len(preserved_net_worth_history) if preserved_net_worth_history and isinstance(preserved_net_worth_history, list) else 'N/A'
                current_hist_len = len(global_state.conceptual_net_worth_history) if hasattr(global_state, 'conceptual_net_worth_history') and isinstance(global_state.conceptual_net_worth_history, list) else 'N/A'
                logging.error(f"EVAL_ONLY_PERFORMANCE: CRITICAL ERROR - preserved net worth history insufficient. Preserved: {preserved_hist_len}, Current: {current_hist_len}")
                logging.error("EVAL_ONLY_PERFORMANCE: This indicates evaluation tracking failed.")
                final_net_worth_history = [INITIAL_CASH]
                logging.error("EVAL_ONLY_PERFORMANCE: Defaulting to [INITIAL_CASH]. QuantStats will be skipped.")

            # Correctly indented block for QuantStats output
            logging.info(f"QUANTSTATS_DIAGNOSTIC: Checking conditions for report generation...")
            logging.info(f"QUANTSTATS_DIAGNOSTIC: QUANTSTATS_OUTPUT = {QUANTSTATS_OUTPUT}")
            logging.info(f"QUANTSTATS_DIAGNOSTIC: final_model_path = {final_model_path}")
            logging.info(f"QUANTSTATS_DIAGNOSTIC: final_net_worth_history type = {type(final_net_worth_history)}")
            logging.info(f"QUANTSTATS_DIAGNOSTIC: final_net_worth_history length = {len(final_net_worth_history) if isinstance(final_net_worth_history, list) else 'N/A'}")

            if QUANTSTATS_OUTPUT and final_model_path:
                logging.info("QUANTSTATS_DIAGNOSTIC: Passed first condition check (QUANTSTATS_OUTPUT and final_model_path)")
                # This check is now less critical due to the consolidation above, but kept for safety.
                if not isinstance(final_net_worth_history, list):
                    logging.error(f"final_net_worth_history is not a list ({type(final_net_worth_history)}). This should not happen after consolidation. Defaulting to empty for safety.")
                    final_net_worth_history = [] # Should be [INITIAL_CASH] from consolidation if issues.

                logging.info(f"QUANTSTATS_DIAGNOSTIC: Checking final_net_worth_history length > 1: {len(final_net_worth_history)} > 1 = {len(final_net_worth_history) > 1}")
                if len(final_net_worth_history) > 1:
                    logging.info("QUANTSTATS_DIAGNOSTIC: Passed length check - proceeding with QuantStats generation")
                    # CRITICAL FIX: Create evaluation-only returns series with proper date alignment
                    # The final_net_worth_history is already trimmed to evaluation period only

                    logging.info(f"EVAL_ONLY_RETURNS: Creating returns series from {len(final_net_worth_history)} evaluation-only net worth entries")

                    # Create returns series from evaluation-only data
                    returns_series = pd.Series(final_net_worth_history).pct_change().fillna(0).iloc[1:]

                    # Align with evaluation period dates
                    logging.info(f"QUANTSTATS_DIAGNOSTIC: Checking returns series conditions...")
                    logging.info(f"QUANTSTATS_DIAGNOSTIC: returns_series.empty = {returns_series.empty}")
                    logging.info(f"QUANTSTATS_DIAGNOSTIC: len(returns_series) = {len(returns_series)}")
                    logging.info(f"QUANTSTATS_DIAGNOSTIC: len(eval_features_final.index) = {len(eval_features_final.index)}")
                    logging.info(f"QUANTSTATS_DIAGNOSTIC: len(returns_series) <= len(eval_features_final.index) = {len(returns_series) <= len(eval_features_final.index)}")

                    if not returns_series.empty and len(returns_series) <= len(eval_features_final.index):
                        logging.info("QUANTSTATS_DIAGNOSTIC: Passed returns series validation - proceeding with date alignment")
                        # Use evaluation period dates for the returns series
                        eval_dates = eval_features_final.index[:len(returns_series)]
                        returns_series.index = eval_dates

                        logging.info(f"EVAL_ONLY_RETURNS: Returns series created with {len(returns_series)} entries")
                        logging.info(f"EVAL_ONLY_RETURNS: Date range: {returns_series.index.min()} to {returns_series.index.max()}")
                        logging.info(f"EVAL_ONLY_RETURNS: Performance period: {(returns_series.index.max() - returns_series.index.min()).days} days")

                        # Calculate and log evaluation period statistics
                        if len(returns_series) > 0:
                            total_return = (returns_series + 1).prod() - 1
                            annualized_return = ((1 + total_return) ** (252 / len(returns_series))) - 1 if len(returns_series) > 0 else 0
                            volatility = returns_series.std() * np.sqrt(252) if len(returns_series) > 1 else 0
                            sharpe = returns_series.mean() / returns_series.std() * np.sqrt(252) if len(returns_series) > 1 and returns_series.std() > 0 else 0

                            logging.info(f"EVAL_ONLY_RETURNS: Total Return: {total_return:.4f} ({total_return*100:.2f}%)")
                            logging.info(f"EVAL_ONLY_RETURNS: Annualized Return: {annualized_return:.4f} ({annualized_return*100:.2f}%)")
                            logging.info(f"EVAL_ONLY_RETURNS: Volatility: {volatility:.4f} ({volatility*100:.2f}%)")
                            logging.info(f"EVAL_ONLY_RETURNS: Sharpe Ratio: {sharpe:.4f}")

                        # CRITICAL FIX: QuantStats generation should happen when validation PASSES, not fails
                        # Calculate the time period in years for validation
                        if isinstance(returns_series.index, pd.DatetimeIndex) and len(returns_series) > 1:
                            time_period_days = (returns_series.index.max() - returns_series.index.min()).days
                            time_period_years = time_period_days / 365.25
                            logging.info(f"Time period: {time_period_days} days ({time_period_years:.4f} years)")

                            # CRITICAL FIX: Validate minimum time period for QuantStats
                            if time_period_years < 0.01:  # Less than ~3.65 days
                                logging.warning(f"Time period too short for QuantStats CAGR calculation: {time_period_years:.6f} years. Skipping QuantStats report.")
                                _create_fallback_report(final_net_worth_history, returns_series, eval_features_final)
                                continue_with_quantstats = False
                            else:
                                continue_with_quantstats = True
                        else:
                            # AUTHENTIC DATA ONLY: Check if we have insufficient data due to failed global state updates
                            if len(returns_series) == 1:
                                logging.error(f"AUTHENTIC DATA VALIDATION FAILED: Only 1 return data point available (from {len(final_net_worth_history)} net worth entries).")
                                logging.error("This indicates that global state updates failed during evaluation.")
                                logging.error("Cannot generate QuantStats report with insufficient authentic data.")
                                _create_fallback_report(final_net_worth_history, returns_series, eval_features_final)
                                continue_with_quantstats = False
                            else:
                                logging.warning("Invalid date index or insufficient data for time period calculation. Skipping QuantStats report.")
                                _create_fallback_report(final_net_worth_history, returns_series, eval_features_final)
                                continue_with_quantstats = False

                        # Only proceed with QuantStats if time period is sufficient
                        if continue_with_quantstats:
                            # Fix resampling to avoid QuantStats numpy operations error
                            if not returns_series.empty and isinstance(returns_series.index, pd.DatetimeIndex):
                                logging.info(f"Resampling returns series to daily frequency. Original length: {len(returns_series)}")
                                # Use .last() instead of .sum() to avoid numpy operations error in QuantStats
                                # This takes the last return value for each day, which is appropriate for daily data
                                daily_returns = returns_series.resample('D').last().dropna()
                                returns_series = pd.Series(daily_returns.values, index=daily_returns.index, name='returns')
                                logging.info(f"Resampled daily returns series length: {len(returns_series)}")

                            try: # TRY BLOCK for QuantStats
                                logging.info("Generating QuantStats report...")
                                # Ensure the directory for the report exists
                                report_dir = os.path.dirname(QUANTSTATS_REPORT_FILENAME)
                                if report_dir: # Check if there is a directory part
                                    os.makedirs(report_dir, exist_ok=True)
                                    logging.info(f"Ensured directory exists: {report_dir}")

                                logging.info(f"Attempting to generate QuantStats HTML for: {os.path.abspath(QUANTSTATS_REPORT_FILENAME)}")

                                # Try to get HTML as string and write manually
                                html_report_str = None

                                # Calculate actual risk-free rate from evaluation period Treasury data
                                actual_risk_free_rate = RISK_FREE_RATE_FALLBACK  # Default fallback
                                try:
                                    # Try to get actual IRX rate from evaluation features
                                    irx_columns = [col for col in eval_features_final.columns if 'close_IRX' in col or 'close_irx' in col]
                                    if irx_columns:
                                        irx_col = irx_columns[0]
                                        # Use median IRX rate from evaluation period (more robust than mean)
                                        median_irx_rate = eval_features_final[irx_col].median()
                                        if pd.notna(median_irx_rate) and 0.01 <= median_irx_rate <= 20.0:  # Validate reasonable range
                                            actual_risk_free_rate = median_irx_rate / 100.0  # Convert percentage to decimal
                                            logging.info(f"Using actual median IRX rate from evaluation period: {median_irx_rate:.2f}% ({actual_risk_free_rate:.4f})")
                                        else:
                                            logging.warning(f"IRX rate {median_irx_rate} outside reasonable range, using fallback {RISK_FREE_RATE_FALLBACK*100:.1f}%")
                                    else:
                                        logging.warning(f"No IRX column found in evaluation features, using fallback {RISK_FREE_RATE_FALLBACK*100:.1f}%")
                                except Exception as rf_calc_error:
                                    logging.warning(f"Error calculating actual risk-free rate: {rf_calc_error}, using fallback {RISK_FREE_RATE_FALLBACK*100:.1f}%")

                                # Generate QuantStats report using the reliable method (qs.reports.basic with output parameter)
                                quantstats_success = False

                                # Generate QuantStats report using the reliable method
                                try:
                                    logging.info("Generating QuantStats basic report with output parameter...")
                                    qs.reports.basic(returns_series,
                                                   output=QUANTSTATS_REPORT_FILENAME,
                                                   title=f'{UNDERLYING_TICKER} PPO Option Strategy {SCRIPT_VERSION_TAG} (Conceptual)',
                                                   rf=actual_risk_free_rate)

                                    # Check if file was created
                                    if os.path.exists(QUANTSTATS_REPORT_FILENAME):
                                        logging.info(f"SUCCESS: QuantStats basic report generated: {QUANTSTATS_REPORT_FILENAME}")
                                        quantstats_success = True
                                    else:
                                        logging.warning("QuantStats report generation did not create file")
                                        quantstats_success = False
                                except Exception as qs_error:
                                    logging.warning(f"QuantStats report generation failed: {qs_error}")
                                    quantstats_success = False

                                # Final fallback if QuantStats report generation failed
                                if not quantstats_success:
                                    logging.warning("QuantStats report generation failed. Creating fallback report...")
                                    try:
                                        _create_fallback_report(final_net_worth_history, returns_series, eval_features_final)
                                        # Verify fallback report was created
                                        if os.path.exists(QUANTSTATS_REPORT_FILENAME):
                                            logging.info(f"SUCCESS: Fallback report created at {QUANTSTATS_REPORT_FILENAME}")
                                        else:
                                            logging.error("CRITICAL: Even fallback report creation failed!")
                                    except Exception as fallback_error:
                                        logging.error(f"CRITICAL: Fallback report creation failed: {fallback_error}")
                                        # Last resort - create minimal HTML report
                                        try:
                                            minimal_html = f"""
                                            <html>
                                            <head><title>SPY Trading Report - Error</title></head>
                                            <body>
                                                <h1>SPY Option Trading Report</h1>
                                                <p><strong>Status:</strong> Report generation failed</p>
                                                <p><strong>Net Worth History Length:</strong> {len(final_net_worth_history)}</p>
                                                <p><strong>Returns Series Length:</strong> {len(returns_series) if 'returns_series' in locals() else 'N/A'}</p>
                                                <p><strong>Error:</strong> {fallback_error}</p>
                                                <p>Please check the log file for detailed error information.</p>
                                            </body>
                                            </html>
                                            """
                                            with open(QUANTSTATS_REPORT_FILENAME, 'w', encoding='utf-8') as f:
                                                f.write(minimal_html)
                                            logging.info(f"MINIMAL: Created minimal error report at {QUANTSTATS_REPORT_FILENAME}")
                                        except Exception as minimal_error:
                                            logging.error(f"CRITICAL: Even minimal report creation failed: {minimal_error}")

                                # ENHANCED DIAGNOSTIC CHECK with file backup
                                if os.path.exists(QUANTSTATS_REPORT_FILENAME):
                                    logging.info(f"CONFIRMED: File exists at {os.path.abspath(QUANTSTATS_REPORT_FILENAME)} immediately after saving.")

                                    # Create a backup copy to prevent loss
                                    try:
                                        backup_filename = QUANTSTATS_REPORT_FILENAME.replace('.html', '_backup.html')
                                        import shutil
                                        shutil.copy2(QUANTSTATS_REPORT_FILENAME, backup_filename)
                                        logging.info(f"BACKUP: Created backup copy at {backup_filename}")

                                        # Also create a timestamped copy
                                        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                                        timestamped_filename = QUANTSTATS_REPORT_FILENAME.replace('.html', f'_{timestamp}.html')
                                        shutil.copy2(QUANTSTATS_REPORT_FILENAME, timestamped_filename)
                                        logging.info(f"TIMESTAMPED: Created timestamped copy at {timestamped_filename}")

                                        # Verify file size and content
                                        file_size = os.path.getsize(QUANTSTATS_REPORT_FILENAME)
                                        logging.info(f"VERIFICATION: File size = {file_size} bytes")

                                        if file_size > 1000:  # Reasonable minimum size for HTML report
                                            logging.info("VERIFICATION: File size appears reasonable for HTML report")
                                        else:
                                            logging.warning(f"VERIFICATION: File size {file_size} bytes seems too small for HTML report")

                                    except Exception as backup_error:
                                        logging.error(f"Failed to create backup copies: {backup_error}")
                                else:
                                    logging.error(f"ERROR: File NOT FOUND at {os.path.abspath(QUANTSTATS_REPORT_FILENAME)} immediately after saving.")
                                    # Try to create fallback report if main report failed
                                    logging.info("Attempting to create fallback report due to missing main report...")
                                    _create_fallback_report(final_net_worth_history, returns_series, eval_features_final)
                            except Exception as qs_error: # EXCEPT for QuantStats
                                logging.error(f"QuantStats failed: {qs_error}", exc_info=True)
                                # DIAGNOSTIC PRINT for exception
                                print(f"DEBUG: QuantStats exception: {qs_error}")
                                _create_fallback_report(final_net_worth_history, returns_series, eval_features_final)

                    else:
                        logging.error(f"QUANTSTATS_DIAGNOSTIC: FAILED returns series validation!")
                        logging.error(f"EVAL_ONLY_RETURNS: Invalid returns series - empty or length mismatch. Returns: {len(returns_series)}, Features: {len(eval_features_final.index)}")
                        # Create fallback report for failed validation
                        _create_fallback_report(final_net_worth_history, pd.Series(dtype=float), eval_features_final)

                    # FIXED: These conditions should be inside the len(final_net_worth_history) > 1 block
                    # Check if returns_series is empty after processing
                    if 'returns_series' in locals() and returns_series.empty:
                        logging.warning("Returns series empty after processing. QuantStats report will be skipped.")
                    elif 'returns_series' in locals() and len(returns_series) > len(eval_features_final.index):
                        logging.warning(f"Length mismatch: Returns({len(returns_series)}) vs Features({len(eval_features_final.index)}). Fallback report will be generated.")
                        _create_fallback_report(final_net_worth_history, returns_series, eval_features_final)
                else: # ELSE for len(final_net_worth_history) > 1 check
                    logging.error(f"QUANTSTATS_DIAGNOSTIC: FAILED length check - final_net_worth_history has {len(final_net_worth_history)} entries (need > 1)")
                    logging.warning("Not enough net worth history for QuantStats. Report will be skipped.")
            else:
                logging.error(f"QUANTSTATS_DIAGNOSTIC: FAILED first condition check!")
                logging.error(f"QUANTSTATS_DIAGNOSTIC: QUANTSTATS_OUTPUT = {QUANTSTATS_OUTPUT}, final_model_path = {final_model_path}")
                logging.error("QUANTSTATS_DIAGNOSTIC: QuantStats report generation SKIPPED due to failed conditions")
        except Exception as e:
            logging.critical(f"Error during final evaluation: {e}", exc_info=True)
        finally:
            # CRITICAL FIX: Reset evaluation mode back to False after evaluation is complete
            logging.info("Resetting global state evaluation mode to False...")
            global_state.set_evaluation_mode(False)

            if 'final_eval_norm_env_closure' in locals() and final_eval_norm_env_closure: final_eval_norm_env_closure.close()
            evaluation_duration=time.time()-evaluation_start_time; logging.info(f"Evaluation finished in {evaluation_duration:.2f}s.")
        run_duration=time.time()-run_start_time; logging.info(f"--- Option Trader {SCRIPT_VERSION_TAG} finished in {run_duration:.2f}s ---")

# --- Signal Generation Function ---
# --- Helper Functions for Model Serialization ---
def save_model_with_norm_stats(model, norm_env, save_path):
    """
    Save both the model and its normalization statistics in a single file.

    Args:
        model: Trained model to save
        norm_env: Normalized environment containing statistics
        save_path: Path where the combined model and stats will be saved
    """
    try:
        # Create a temporary directory
        temp_dir = os.path.join(os.path.dirname(save_path), '.temp_save')
        os.makedirs(temp_dir, exist_ok=True)

        # Save model and normalization stats to temporary location
        temp_model_path = os.path.join(temp_dir, 'model.zip')
        temp_stats_path = os.path.join(temp_dir, 'norm_stats.pkl')

        model.save(temp_model_path)
        norm_env.save(temp_stats_path)

        # Create a zip file containing both
        import zipfile
        with zipfile.ZipFile(save_path, 'w') as zipf:
            zipf.write(temp_model_path, arcname='model.zip')
            zipf.write(temp_stats_path, arcname='norm_stats.pkl')

            # Add metadata
            metadata = {
                'timestamp': datetime.now().isoformat(),
                'version': SCRIPT_VERSION_TAG,
                'underlying': UNDERLYING_TICKER
            }
            with open(os.path.join(temp_dir, 'metadata.json'), 'w') as f:
                json.dump(metadata, f)
            zipf.write(os.path.join(temp_dir, 'metadata.json'), arcname='metadata.json')

        logging.info(f"Saved model with normalization stats to {save_path}")

        # Clean up temporary directory
        import shutil
        shutil.rmtree(temp_dir)
        return True
    except Exception as e:
        logging.error(f"Error saving model with stats: {e}", exc_info=True)
        return False

def load_model_with_norm_stats(load_path):
    """
    Load a model along with its normalization statistics from a single file.

    Args:
        load_path: Path to the combined model and stats file

    Returns:
        Tuple of (model, normalization_stats)
    """
    try:
        # Create a temporary directory
        temp_dir = os.path.join(os.path.dirname(load_path), '.temp_load')
        os.makedirs(temp_dir, exist_ok=True)

        # Extract the zip file
        import zipfile
        with zipfile.ZipFile(load_path, 'r') as zipf:
            zipf.extractall(temp_dir)

        # Load model
        model_path = os.path.join(temp_dir, 'model.zip')
        model = PPO.load(model_path)

        # Load normalization stats (don't apply yet, just extract the values)
        stats_path = os.path.join(temp_dir, 'norm_stats.pkl')
        norm_stats = joblib.load(stats_path)

        # Clean up temporary directory
        import shutil
        shutil.rmtree(temp_dir)

        logging.info(f"Successfully loaded model with normalization stats from {load_path}")
        return model, norm_stats
    except Exception as e:
        logging.error(f"Error loading model with stats: {e}", exc_info=True)
        return None, None

def apply_norm_stats_to_env(norm_stats, env):
    """
    Apply loaded normalization statistics to a vector environment.

    Args:
        norm_stats: The loaded normalization statistics
        env: The vector environment to apply the stats to

    Returns:
        The updated environment
    """
    try:
        # Create a new VecNormalize wrapper around the env
        normalized_env = VecNormalize(
            env,
            training=False,
            norm_obs=True,
            norm_reward=False
        )

        # CRITICAL FIX: Use object.__getattribute__ to safely access norm_stats attributes
        # This prevents infinite recursion in Stable Baselines3 VecNormalize
        try:
            # Check if attributes exist before accessing them
            if hasattr(norm_stats, 'obs_rms'):
                obs_rms = object.__getattribute__(norm_stats, 'obs_rms')
                if obs_rms is not None:
                    normalized_env.obs_rms = obs_rms
                    logging.info("Applied obs_rms statistics")
                else:
                    logging.info("obs_rms is None - skipping")
            else:
                logging.info("norm_stats has no obs_rms attribute - skipping")
        except (AttributeError, RecursionError) as e:
            logging.warning(f"Could not access obs_rms from norm_stats: {e}")

        try:
            if hasattr(norm_stats, 'ret_rms'):
                ret_rms = object.__getattribute__(norm_stats, 'ret_rms')
                if ret_rms is not None:
                    normalized_env.ret_rms = ret_rms
                    logging.info("Applied ret_rms statistics")
                else:
                    logging.info("ret_rms is None - skipping")
            else:
                logging.info("norm_stats has no ret_rms attribute - skipping")
        except (AttributeError, RecursionError) as e:
            logging.warning(f"Could not access ret_rms from norm_stats: {e}")

        # Apply other attributes safely
        try:
            if hasattr(norm_stats, 'clip_obs'):
                normalized_env.clip_obs = object.__getattribute__(norm_stats, 'clip_obs')
        except (AttributeError, RecursionError):
            pass

        try:
            if hasattr(norm_stats, 'clip_reward'):
                normalized_env.clip_reward = object.__getattribute__(norm_stats, 'clip_reward')
        except (AttributeError, RecursionError):
            pass

        try:
            if hasattr(norm_stats, 'norm_obs'):
                normalized_env.norm_obs = object.__getattribute__(norm_stats, 'norm_obs')
        except (AttributeError, RecursionError):
            pass

        try:
            if hasattr(norm_stats, 'norm_reward'):
                normalized_env.norm_reward = object.__getattribute__(norm_stats, 'norm_reward')
        except (AttributeError, RecursionError):
            pass

        logging.info("Successfully applied normalization statistics to environment")
        return normalized_env
    except Exception as e:
        logging.error(f"Error applying normalization stats: {e}", exc_info=True)
        return env

def verify_observation_consistency(observer_total_obs_size: int, signal_total_obs_size: int,
                                 observer_market_features: int, signal_market_features: int,
                                 observer_feature_columns: list, signal_feature_columns: list) -> bool:
    """
    Verify that observation structures between OptionObserver and generate_trading_signal are consistent.

    Args:
        observer_total_obs_size: Total observation size from OptionObserver
        signal_total_obs_size: Total observation size from generate_trading_signal
        observer_market_features: Number of market features from OptionObserver
        signal_market_features: Number of market features from generate_trading_signal
        observer_feature_columns: Market feature columns from OptionObserver
        signal_feature_columns: Market feature columns from generate_trading_signal

    Returns:
        bool: True if consistent, False otherwise
    """
    logging.info("=== OBSERVATION CONSISTENCY VERIFICATION ===")

    # Check total observation sizes
    if observer_total_obs_size != signal_total_obs_size:
        logging.error(f"CONSISTENCY ERROR: Total obs size mismatch - Observer: {observer_total_obs_size}, Signal: {signal_total_obs_size}")
        return False

    # Check market feature counts
    if observer_market_features != signal_market_features:
        logging.error(f"CONSISTENCY ERROR: Market features mismatch - Observer: {observer_market_features}, Signal: {signal_market_features}")
        return False

    # Check feature column consistency
    if observer_feature_columns != signal_feature_columns:
        logging.error(f"CONSISTENCY ERROR: Feature columns mismatch")
        logging.error(f"  Observer columns: {observer_feature_columns}")
        logging.error(f"  Signal columns: {signal_feature_columns}")
        return False

    logging.info("SUCCESS: OBSERVATION CONSISTENCY VERIFIED - All structures match!")
    logging.info(f"  Total obs size: {observer_total_obs_size}")
    logging.info(f"  Market features: {observer_market_features}")
    logging.info(f"  Feature columns: {observer_feature_columns}")

    return True

def save_observation_metadata(observer: 'OptionObserver', filepath: str) -> None:
    """
    Save observation structure metadata for consistency verification.

    Args:
        observer: OptionObserver instance
        filepath: Path to save metadata
    """
    try:
        metadata = {
            'total_obs_size': observer._total_obs_size,
            'flat_market_obs_size': observer._flat_market_obs_size,
            'n_market_features': observer.n_market_features,
            'window_size': observer.window_size,
            'portfolio_features': N_STATIC_PORTFOLIO_FEATURES,
            'option_features': N_STATIC_HELD_OPTION_FEATURES,
            'market_feature_columns': observer.market_feature_columns,
            'feature_names': observer._feature_names
        }

        with open(filepath, 'w') as f:
            json.dump(metadata, f, indent=2)

        logging.info(f"Observation metadata saved to: {filepath}")

    except Exception as e:
        logging.error(f"Failed to save observation metadata: {e}")

def load_observation_metadata(filepath: str) -> dict:
    """
    Load observation structure metadata for consistency verification.

    Args:
        filepath: Path to load metadata from

    Returns:
        dict: Observation metadata
    """
    try:
        with open(filepath, 'r') as f:
            metadata = json.load(f)

        logging.info(f"Observation metadata loaded from: {filepath}")
        return metadata

    except Exception as e:
        logging.error(f"Failed to load observation metadata: {e}")
        return {}

def generate_trading_signal(model_filename:str, normalize_stats_filename:str, config:dict, action_scheme_config:dict=None, reward_scheme_config:dict=None) -> dict:
    """
    Generate trading signals using a trained model.
    Ensures consistent normalization and window size across all phases.

    Args:
        model_filename: Path to the trained model file
        normalize_stats_filename: Path to saved normalization statistics
        config: Dictionary containing configuration parameters

    Returns:
        dict: Signal output with decision details
    """
    signal_output={"timestamp":datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S UTC"),"status":"Error","message":"Init error","spy_price_at_decision":None,"vix_at_decision":None,"irx_at_decision":None,"tnx_at_decision":None,"raw_action":-1,"decision_type":"ERROR","details":None,"confidence_score":0.0,"human_readable_suggestion":"Error."}
    try:
        logging.info(f"--- Trading Signal Generation {SCRIPT_VERSION_TAG} ---"); window_size=config['WINDOW_SIZE']; n_market_features=config['N_MARKET_FEATURES']

        # CRITICAL FIX: Include both portfolio and option static features to match OptionObserver
        n_static_portfolio_features=config['N_STATIC_PORTFOLIO_FEATURES']
        n_static_option_features=config['N_STATIC_HELD_OPTION_FEATURES']
        total_obs_size=(window_size*n_market_features)+n_static_portfolio_features+n_static_option_features

        # Store metadata path for later verification (after features_df is created)
        obs_metadata_path = os.path.join(MODEL_DIR, "observation_metadata.json")

        # First try to load combined model+stats file (preferred approach)
        latest_combined_path = os.path.join(MODEL_WITH_STATS_DIR, "latest_model_with_stats.zip")
        use_combined_approach = False
        loaded_stats = None

        if os.path.exists(latest_combined_path):
            logging.info(f"Attempting to load combined model+stats from {latest_combined_path}")
            model, loaded_stats = load_model_with_norm_stats(latest_combined_path)
            if model and loaded_stats:
                use_combined_approach = True
                logging.info("Successfully loaded model and normalization stats from combined file")
            else:
                logging.warning("Failed to load from combined file, falling back to separate files")

        # Fall back to legacy approach if combined file not available or loading failed
        if not use_combined_approach:
            if not os.path.exists(model_filename):
                signal_output["message"]=f"Model not found: {model_filename}"
                return signal_output

            logging.info(f"Loading model from {model_filename}")
            model = PPO.load(model_filename)
            logging.info(f"Loading normalization stats from {normalize_stats_filename}")

        # Note: We'll load normalization stats after creating the dummy environment
        model_obs_shape = model.observation_space.shape[0] if hasattr(model.observation_space, 'shape') else "unknown"
        logging.info(f"Model observation space shape: {model_obs_shape}")

        # CRITICAL FIX: Use the model's observation shape if available, overriding calculated shape
        if isinstance(model_obs_shape, int):
            # We now know the exact observation size from the model (1340)
            # Let's override our calculated value to match
            expected_obs_size = model_obs_shape

            # Calculate the implied number of market features
            # total_size = window_size * market_features + portfolio_features + option_features
            # market_features = (total_size - portfolio_features - option_features) / window_size
            total_static_features = n_static_portfolio_features + n_static_option_features
            implied_n_market_features = (expected_obs_size - total_static_features) // window_size

            logging.info(f"CRITICAL FIX: Model expects {expected_obs_size} total features with {implied_n_market_features} market features")
            logging.info(f"Current environment has {n_market_features} market features, need to pad {implied_n_market_features - n_market_features} features")
            logging.info(f"Static features: {n_static_portfolio_features} portfolio + {n_static_option_features} option = {total_static_features} total")

            # CRITICAL FIX: Update feature counts to match model expectations
            n_market_features = implied_n_market_features
            total_obs_size = expected_obs_size

        if not os.path.exists(normalize_stats_filename): signal_output["message"]=f"Stats not found: {normalize_stats_filename}"; return signal_output

        # Skip trying to inspect the normalize stats directly - we'll use model's shape
        # Hard code the correct observation space directly matching the model
        logging.info(f"Creating dummy environment with observation shape: {total_obs_size}")
        dummy_obs_space=spaces.Box(low=-np.inf,high=np.inf,shape=(total_obs_size,),dtype=np.float32)
        dummy_action_space=spaces.Discrete(config['ACTION_SPACE_SIZE'])
        dummy_env=MinimalPredictionEnv(dummy_obs_space,dummy_action_space)

        # Now load the stats with our adjusted environment
        vec_env_predict=DummyVecEnv([lambda:dummy_env])
        vec_env_predict=VecNormalize.load(normalize_stats_filename,vec_env_predict)
        vec_env_predict.training=False; vec_env_predict.norm_reward=False
        logging.info("VecNormalize wrapper created successfully.")

        # CRITICAL FIX: Use consistent date calculation system across all modes
        # Use the same FIXED_END_DATE system as training/optimization modes
        logging.info("Fetching latest market data...")
        signal_end_date = FIXED_END_DATE.strftime('%Y-%m-%d')
        signal_start_date = (FIXED_END_DATE - timedelta(days=window_size+90)).strftime('%Y-%m-%d')
        logging.info(f"Signal mode data period: {signal_start_date} to {signal_end_date} (consistent with training date system)")
        start_date = signal_start_date
        end_date = signal_end_date
        # CRITICAL FIX: Use reduced minimum periods for signal generation data fetching
        data_dict_signal={}
        for t in config['ALL_TICKERS']:
            # Use min_periods_override=50 for signal generation to allow shorter data periods
            df = fetch_historical_data_yf_refactored(t, start_date, end_date, min_periods_override=50)
            data_dict_signal[t] = df
        if data_dict_signal[config['UNDERLYING_TICKER']].empty: signal_output["message"]=f"No recent data for {config['UNDERLYING_TICKER']}."; return signal_output

        features_df=create_combined_features(data_dict_signal)
        if features_df.empty or features_df.shape[0]<window_size: signal_output["message"]=f"Insufficient features: {features_df.shape[0]} rows, need {window_size}."; return signal_output

        # CRITICAL FIX: Reorder columns to match training data order (alphabetical)
        # This ensures consistency between training and signal generation
        # Training now creates DataFeed streams in alphabetical order, so signal generation must match
        if not features_df.empty:
            original_columns = list(features_df.columns)
            sorted_columns = sorted(original_columns)
            if original_columns != sorted_columns:
                logging.info(f"Reordering feature columns from creation order to alphabetical order for consistency with training DataFeed")
                logging.debug(f"Original order: {original_columns}")
                logging.debug(f"Sorted order: {sorted_columns}")
                features_df = features_df[sorted_columns]
                logging.info(f"Feature columns successfully reordered to match training DataFeed stream order")

        # Now perform observation metadata consistency verification
        if os.path.exists(obs_metadata_path):
            obs_metadata = load_observation_metadata(obs_metadata_path)
            if obs_metadata:
                # Verify consistency with training
                training_total_obs = obs_metadata.get('total_obs_size', total_obs_size)
                training_market_features = obs_metadata.get('n_market_features', n_market_features)
                training_feature_columns = obs_metadata.get('market_feature_columns', [])

                signal_feature_columns = list(features_df.columns)

                if not verify_observation_consistency(
                    training_total_obs, total_obs_size,
                    training_market_features, n_market_features,
                    training_feature_columns, signal_feature_columns
                ):
                    error_msg = "OBSERVATION CONSISTENCY VERIFICATION FAILED"
                    logging.error(error_msg)
                    signal_output["message"] = error_msg
                    return signal_output

        # Log the actual feature count from the data to help diagnose issues
        actual_feature_count = features_df.shape[1]
        logging.info(f"Actual market features from data: {actual_feature_count}, using: {n_market_features} for observation")

        market_window_df=features_df.iloc[-window_size:].copy()
        if market_window_df.shape[0]!=window_size: signal_output["message"]=f"Market window shape incorrect: {market_window_df.shape}."; return signal_output

        # CRITICAL FIX: Handle feature count mismatch for model compatibility
        actual_features = market_window_df.shape[1]
        if actual_features != n_market_features:
            logging.warning(f"Feature count mismatch: data has {actual_features} but model expects {n_market_features}")
            if actual_features > n_market_features:
                # Too many features - select subset to match expected count
                logging.info(f"Selecting first {n_market_features} features from available {actual_features}")
                columns_to_use = market_window_df.columns[:n_market_features]
                market_window_df = market_window_df[columns_to_use]
            else:
                # Too few features - pad with zeros to match model expectations
                missing_features = n_market_features - actual_features
                logging.info(f"CRITICAL FIX: Padding with {missing_features} zero columns to reach {n_market_features} features for model compatibility")
                for i in range(missing_features):
                    market_window_df[f'padding_feature_{i}'] = 0.0

        flat_market_obs=market_window_df.values.flatten().astype(np.float32)

        # CRITICAL FIX: Create portfolio features to match OptionObserver structure
        # Portfolio features: [cash, net_worth, cash_pct, net_worth_pct, pnl_pct]
        portfolio_features = np.zeros(n_static_portfolio_features, dtype=np.float32)
        portfolio_features[0] = INITIAL_CASH  # Starting cash (no position held during signal generation)
        portfolio_features[1] = INITIAL_CASH  # Net worth equals cash when no position
        portfolio_features[2] = 1.0  # Cash percentage = 100%
        portfolio_features[3] = 1.0  # Net worth percentage = 100%
        portfolio_features[4] = 0.0  # P&L percentage = 0%

        # Option features (default zeros for no position)
        option_features=np.array(DEFAULT_STATIC_HELD_OPTION_FEATURES,dtype=np.float32)

        # CRITICAL FIX: Combine market, portfolio, and option features to match OptionObserver
        raw_observation=np.concatenate([flat_market_obs, portfolio_features, option_features])

        # ENHANCED LOGGING FOR OBSERVATION STRUCTURE VERIFICATION IN SIGNAL GENERATION
        logging.info(f"generate_trading_signal: OBSERVATION STRUCTURE VERIFICATION")
        logging.info(f"  - Window size: {window_size}")
        logging.info(f"  - Market features: {n_market_features}")
        logging.info(f"  - Market obs size: {flat_market_obs.shape[0]}")
        logging.info(f"  - Portfolio features: {portfolio_features.shape[0]}")
        logging.info(f"  - Option features: {option_features.shape[0]}")
        logging.info(f"  - Total obs size: {raw_observation.shape[0]}")
        logging.info(f"  - Expected total: {total_obs_size}")

        # Verify observation structure matches OptionObserver exactly
        expected_market_size = window_size * n_market_features
        expected_portfolio_size = n_static_portfolio_features
        expected_option_size = n_static_option_features
        expected_total = expected_market_size + expected_portfolio_size + expected_option_size

        if raw_observation.shape[0] != expected_total:
            error_msg = f"OBSERVATION STRUCTURE MISMATCH: Expected {expected_total}, got {raw_observation.shape[0]}"
            logging.error(error_msg)
            signal_output["message"] = error_msg
            return signal_output

        # Log a sample of the raw observation for verification
        if len(raw_observation) > 0:
            sample_size = min(10, len(raw_observation))
            logging.info(f"  - Raw obs sample (first {sample_size}): {raw_observation[:sample_size]}")

        # Ensure observation matches expected size
        if raw_observation.shape[0] != total_obs_size:
            logging.warning(f"Observation size mismatch after adjustments: {raw_observation.shape[0]} vs expected {total_obs_size}")

            # Fix the size by padding or truncating as needed
            if raw_observation.shape[0] < total_obs_size:
                # Pad with zeros if too small
                padding_size = total_obs_size - raw_observation.shape[0]
                logging.info(f"Padding observation with {padding_size} zeros")
                raw_observation = np.pad(raw_observation, (0, padding_size), 'constant')
            else:
                # Truncate if too large
                logging.info(f"Truncating observation from {raw_observation.shape[0]} to {total_obs_size}")
                raw_observation = raw_observation[:total_obs_size]

        logging.info(f"Final raw observation shape: {raw_observation.shape}")

        normalized_observation=vec_env_predict.normalize_obs(raw_observation.reshape(1,-1))
        logging.info("Observation normalized.")

        logging.info("Making prediction..."); action, _ = model.predict(normalized_observation,deterministic=True); raw_action=int(action[0]); logging.info(f"Raw action: {raw_action}")

        # CRITICAL FIX: Apply confidence filtering using optimized hyperparameters
        confidence_threshold = 0.0  # Default threshold
        if action_scheme_config and 'confidence_threshold' in action_scheme_config:
            confidence_threshold = action_scheme_config['confidence_threshold']
            logging.info(f"SIGNAL MODE: Using optimized confidence_threshold: {confidence_threshold}")

        # Extract action confidence from model prediction
        action_confidence = 0.5  # Default confidence
        try:
            # Get action probabilities from the model
            if hasattr(model.policy, 'predict') and hasattr(model.policy, 'action_dist'):
                # Get the action distribution
                obs_tensor = model.policy.obs_to_tensor(normalized_observation)[0]
                with torch.no_grad():
                    latent_pi, _ = model.policy.mlp_extractor(obs_tensor)
                    action_logits = model.policy.action_net(latent_pi)
                    action_probs = torch.softmax(action_logits, dim=-1)
                    action_confidence = float(action_probs[0, raw_action].item())
                    logging.info(f"SIGNAL MODE: Extracted action confidence: {action_confidence:.3f}")
        except Exception as e:
            logging.warning(f"SIGNAL MODE: Could not extract action confidence: {e}")
            action_confidence = 0.5

        # Apply confidence filtering
        original_action = raw_action
        if confidence_threshold > 0.0 and action_confidence < confidence_threshold:
            logging.info(f"SIGNAL MODE: Action {raw_action} confidence {action_confidence:.3f} below threshold {confidence_threshold:.3f}. Filtering to HOLD.")
            raw_action = 0  # Override to hold
        else:
            logging.info(f"SIGNAL MODE: Action {raw_action} confidence {action_confidence:.3f} meets threshold {confidence_threshold:.3f}. Proceeding.")

        logging.info(f"SIGNAL MODE: Final action after confidence filtering: {raw_action} (original: {original_action})")
        current_spy_price=features_df[f"close_{config['UNDERLYING_TICKER']}"].iloc[-1]
        vix_clean=config['VIX_TICKER'].replace('^','').upper(); irx_clean=config['IRX_TICKER'].replace('^','').upper(); tnx_clean=config['TNX_TICKER'].replace('^','').upper()

        # CRITICAL FIX: Reduce excessive logging in production code
        # Improved column detection for VIX, IRX and TNX
        logging.debug(f"Available columns in features_df: {features_df.columns.tolist()}")

        # Try multiple possible column name formats for VIX
        vix_column_options = [f"close_{vix_clean}", f"Close_{vix_clean}", "close_VIX", "Close_VIX", "close", "Close"]
        current_vix = None
        for col in vix_column_options:
            if col in features_df.columns:
                current_vix = features_df[col].iloc[-1]
                logging.info(f"Found VIX data in column: {col}, value: {current_vix}")
                # Validate VIX value is reasonable
                if 5.0 <= current_vix <= 100.0:
                    logging.info(f"SUCCESS: Valid VIX value found: {current_vix}")
                    break
                else:
                    logging.warning(f"VIX value {current_vix} outside reasonable range (5-100). Will try direct fetch.")
                    current_vix = None
                    break

        # Try multiple possible column name formats for IRX
        irx_column_options = [f"close_{irx_clean}", f"Close_{irx_clean}", "close_IRX", "Close_IRX", "close", "Close"]
        current_irx = None
        for col in irx_column_options:
            try:
                current_irx = features_df[col].iloc[-1]
                break
            except (KeyError, IndexError):
                continue
        if current_irx is None:
            error_msg = "AUTHENTIC DATA VALIDATION FAILED: Could not find IRX data in features_df"
            logging.error(error_msg)
            raise ValueError(error_msg)

        # Try multiple possible column name formats for TNX
        tnx_column_options = [f"close_{tnx_clean}", f"Close_{tnx_clean}", "close_TNX", "Close_TNX", "close", "Close"]
        current_tnx = None
        for col in tnx_column_options:
            if col in features_df.columns:
                current_tnx = features_df[col].iloc[-1]
                logging.info(f"Found TNX data in column: {col}, value: {current_tnx}")
                # CRITICAL FIX: Validate TNX value in decimal format (0.005 to 0.18 = 0.5% to 18%)
                if 0.005 <= current_tnx <= 0.18:
                    logging.info(f"SUCCESS: Valid TNX value found: {current_tnx} ({current_tnx*100:.2f}%)")
                    break
                else:
                    logging.warning(f"TNX value {current_tnx} ({current_tnx*100:.2f}%) outside reasonable range (0.5-18%). Will try direct fetch.")
                    current_tnx = None
                    break

        # If still None, try to fetch real-time data directly with multiple retries
        if current_vix is None or current_irx is None or current_tnx is None:
            logging.warning("VIX, IRX, or TNX data not found in features dataframe, attempting direct fetch")

            # Initialize session for better reliability
            session = shared_yf_session if 'shared_yf_session' in globals() else None

            # Direct fetch for VIX
            if current_vix is None:
                try:
                    logging.info(f"Directly fetching latest VIX data...")
                    vix_ticker = yf.Ticker(config['VIX_TICKER'], session=session)

                    # Try multiple methods to get real-time VIX data
                    methods = [
                        lambda: vix_ticker.history(period="1d"),
                        lambda: vix_ticker.history(period="5d").iloc[-1:],
                        lambda: vix_ticker.info.get('regularMarketPrice'),
                        lambda: vix_ticker.fast_info.get('last_price')
                    ]

                    for i, method in enumerate(methods):
                        try:
                            result = method()
                            if isinstance(result, pd.DataFrame) and not result.empty:
                                current_vix = result['Close'].iloc[-1]
                                logging.info(f"Directly fetched VIX value using method {i+1}: {current_vix}")
                                break
                            elif result is not None:
                                current_vix = float(result)
                                logging.info(f"Directly fetched VIX value using alternative method {i+1}: {current_vix}")
                                break
                        except Exception as method_e:
                            logging.warning(f"VIX fetch method {i+1} failed: {method_e}")
                except Exception as e:
                    logging.error(f"All VIX direct fetch attempts failed: {e}")

            # Direct fetch for IRX
            if current_irx is None:
                try:
                    logging.info(f"Directly fetching latest IRX data...")
                    irx_ticker = yf.Ticker(config['IRX_TICKER'], session=session)

                    # Try multiple methods to get real-time IRX data
                    methods = [
                        lambda: irx_ticker.history(period="1d"),
                        lambda: irx_ticker.history(period="5d").iloc[-1:],
                        lambda: irx_ticker.info.get('regularMarketPrice'),
                        lambda: irx_ticker.fast_info.get('last_price')
                    ]

                    for i, method in enumerate(methods):
                        try:
                            result = method()
                            if isinstance(result, pd.DataFrame) and not result.empty:
                                current_irx = result['Close'].iloc[-1]
                                logging.info(f"Directly fetched IRX value using method {i+1}: {current_irx}")
                                break
                            elif result is not None:
                                current_irx = float(result)
                                logging.info(f"Directly fetched IRX value using alternative method {i+1}: {current_irx}")
                                break
                        except Exception as method_e:
                            logging.warning(f"IRX fetch method {i+1} failed: {method_e}")
                except Exception as e:
                    logging.error(f"All IRX direct fetch attempts failed: {e}")

            # Direct fetch for TNX
            if current_tnx is None:
                try:
                    logging.info(f"Directly fetching latest TNX data...")
                    tnx_ticker = yf.Ticker(config['TNX_TICKER'], session=session)

                    # Try multiple methods to get real-time TNX data
                    methods = [
                        lambda: tnx_ticker.history(period="1d"),
                        lambda: tnx_ticker.history(period="5d").iloc[-1:],
                        lambda: tnx_ticker.info.get('regularMarketPrice'),
                        lambda: tnx_ticker.fast_info.get('last_price')
                    ]

                    for i, method in enumerate(methods):
                        try:
                            result = method()
                            if isinstance(result, pd.DataFrame) and not result.empty:
                                tnx_raw = result['Close'].iloc[-1]
                                # Convert from percentage to decimal format for consistency
                                current_tnx = tnx_raw / 100.0 if tnx_raw > 1.0 else tnx_raw
                                logging.info(f"Directly fetched TNX value using method {i+1}: {current_tnx} ({current_tnx*100:.2f}%)")
                                break
                            elif result is not None:
                                tnx_raw = float(result)
                                # Convert from percentage to decimal format for consistency
                                current_tnx = tnx_raw / 100.0 if tnx_raw > 1.0 else tnx_raw
                                logging.info(f"Directly fetched TNX value using alternative method {i+1}: {current_tnx} ({current_tnx*100:.2f}%)")
                                break
                        except Exception as method_e:
                            logging.warning(f"TNX fetch method {i+1} failed: {method_e}")
                except Exception as e:
                    logging.error(f"All TNX direct fetch attempts failed: {e}")

        # Use the actual extracted confidence score instead of hardcoded values
        confidence_score = action_confidence
        if raw_action==0: decision_type="HOLD"; details="Hold cash."; human_suggestion="Hold cash, market conditions may not favor new option trades."
        else:
            opt_idx=raw_action-1; opt_type_val=opt_idx//(config['N_STRIKES']*config['N_EXPIRIES']); opt_type="CALL" if opt_type_val==0 else "PUT"
            strike_expiry_idx=opt_idx%(config['N_STRIKES']*config['N_EXPIRIES']); strike_cat_idx=strike_expiry_idx//config['N_EXPIRIES']; expiry_cat_idx=strike_expiry_idx%config['N_EXPIRIES']
            strike_pct=config['STRIKE_CATEGORIES_PCT'][strike_cat_idx]; expiry_dte=config['EXPIRY_CATEGORIES_DTE'][expiry_cat_idx]; strike_price=current_spy_price*(1+strike_pct); decision_type=f"BUY_{opt_type}"

            # Determine option moneyness
            moneyness = get_option_moneyness(opt_type, strike_pct)

            # Keep the actual model confidence instead of overriding with hardcoded values
            # The confidence_score already contains the actual model confidence from action_confidence

            details={
                "option_type": opt_type,
                "strike_price": float(f"{strike_price:.2f}"),
                "strike_percentage": float(f"{strike_pct:.3f}"),
                "days_to_expiry": int(expiry_dte),
                "moneyness": moneyness  # Add moneyness to details
            }

            # Update human suggestion to include moneyness information
            human_suggestion=f"Consider Buying a {opt_type} SPY option ({moneyness}):\n- Strike: ${strike_price:.2f} ({strike_pct*100:+.1f}% from current)\n- Expiry: ~{expiry_dte} trading days\n- Confidence: {confidence_score:.2f}\n\nGuidance: {'Expect SPY to rise.' if opt_type=='CALL' else 'Expect SPY to fall.'}\nManage risk: Consider stop-loss."

            # Add a clarification for deep ITM calls or deep ITM puts
            if "deep in-the-money" in moneyness:
                if opt_type == "CALL":
                    human_suggestion += f"\n\nNote: This is a deep in-the-money CALL option (strike well below current price), which provides high delta exposure to SPY with lower premium cost relative to buying shares outright."
                else:  # PUT
                    human_suggestion += f"\n\nNote: This is a deep in-the-money PUT option (strike well above current price), which provides high delta exposure to SPY's downside with significant intrinsic value."

        # Fail explicitly if authentic data not available
        if current_vix is None:
            error_msg = "AUTHENTIC DATA VALIDATION FAILED: No valid VIX data available for signal generation"
            logging.error(error_msg)
            raise ValueError(error_msg)
        if current_irx is None:
            error_msg = "AUTHENTIC DATA VALIDATION FAILED: No valid IRX data available for signal generation"
            logging.error(error_msg)
            raise ValueError(error_msg)
        if current_tnx is None:
            error_msg = "AUTHENTIC DATA VALIDATION FAILED: No valid TNX data available for signal generation"
            logging.error(error_msg)
            raise ValueError(error_msg)

        signal_output.update({"status":"Success","message":"Signal generated.","spy_price_at_decision":float(f"{current_spy_price:.2f}"),"vix_at_decision":float(f"{current_vix:.2f}"),"irx_at_decision":float(f"{current_irx:.3f}"),"tnx_at_decision":float(f"{current_tnx:.3f}"),"raw_action":raw_action,"decision_type":decision_type,"details":details,"confidence_score":float(f"{confidence_score:.3f}"),"human_readable_suggestion":human_suggestion})
        logging.info(f"Generated signal: {decision_type} with confidence {confidence_score:.2f}")
    except Exception as e: logging.error(f"Error generating signal: {e}",exc_info=True); signal_output["message"]=f"Error: {str(e)}"; signal_output["status"]="Error"; signal_output["decision_type"]="ERROR"; signal_output["human_readable_suggestion"]=f"Error: {str(e)}"
    return signal_output

# --- Fallback Report & Equity Curve Helpers ---
def _create_fallback_report(_conceptual_net_worth_history, returns_series, eval_features_final):
    try:
        logging.info("Creating fallback report..."); daily_returns=returns_series; cumulative_returns=(1+daily_returns).cumprod()-1
        total_return=((daily_returns+1).prod()-1)*100 if not daily_returns.empty else 0; annualized_return=((1+total_return/100)**(252/len(daily_returns))-1)*100 if len(daily_returns)>0 else 0; sharpe=daily_returns.mean()/daily_returns.std()*np.sqrt(252) if not daily_returns.empty and daily_returns.std()>0 else 0
        if not cumulative_returns.empty: cumulative_max=cumulative_returns.cummax(); drawdown=(cumulative_returns-cumulative_max)/(1+cumulative_max); max_dd=drawdown.min()*100 if not drawdown.empty else 0
        else: drawdown=pd.Series([]); max_dd=0
        csv_path=os.path.join(SB3_LOG_DIR,f'strategy_returns_fallback_{SCRIPT_VERSION_TAG}.csv'); csv_msg=f"Failed CSV save."
        try:
            # Align lengths for DataFrame creation
            num_returns = len(returns_series)

            df_data = {
                'date': returns_series.index[:num_returns] if hasattr(returns_series, 'index') else list(range(num_returns)),
                'daily_return': daily_returns.values[:num_returns] if not daily_returns.empty else [0.0] * num_returns,
                'cumulative_return': cumulative_returns.values[:num_returns] if not cumulative_returns.empty else [0.0] * num_returns,
                'drawdown': drawdown.values[:num_returns] if not drawdown.empty else [0.0] * num_returns,
                # Always use the official net worth history from global_state
                'net_worth': global_state.conceptual_net_worth_history[1:num_returns+1] if len(global_state.conceptual_net_worth_history) > num_returns else [INITIAL_CASH] * num_returns
            }

            # Ensure all arrays in df_data actually have num_returns elements
            for k, v_list in df_data.items():
                if len(v_list) != num_returns:
                    logging.warning(f"Fallback CSV: Length mismatch for '{k}'. Expected {num_returns}, got {len(v_list)}. Adjusting.")
                    # This case should ideally not be hit if logic above is correct
                    # As a safeguard, pad with default or truncate. Using last known net worth or initial cash for 'net_worth'.
                    default_val = INITIAL_CASH if k == 'net_worth' else 0.0
                    corrected_list = list(v_list) # Make it a list to modify
                    if len(corrected_list) < num_returns:
                        corrected_list.extend([default_val] * (num_returns - len(corrected_list)))
                    else:
                        corrected_list = corrected_list[:num_returns]
                    df_data[k] = corrected_list

            returns_df = pd.DataFrame(df_data)
            returns_df.to_csv(csv_path,index=False); csv_msg=f"Returns CSV: {csv_path}"
        except Exception as csv_err: logging.error(f"Failed fallback CSV: {csv_err}")
        chart_html="";
        try:
            chart_img=_create_equity_curve_chart(global_state.conceptual_net_worth_history,eval_features_final.index[:len(global_state.conceptual_net_worth_history)] if eval_features_final is not None else None)
            if chart_img: chart_html=f"""<div style="text-align:center;margin:20px 0;"><img src="data:image/png;base64,{chart_img}" width="800"></div>"""
        except Exception as chart_err: logging.warning(f"No equity chart: {chart_err}")
        html_content=f"""<html><head><title>{UNDERLYING_TICKER} Report (Fallback)</title><style>body{{font-family:Arial;margin:20px;}}.metrics{{display:flex;flex-wrap:wrap;}}.metric-box{{border:1px solid #ddd;border-radius:5px;padding:15px;margin:10px;min-width:200px;box-shadow:2px 2px 12px rgba(0,0,0,0.1);}}.metric-value{{font-size:24px;font-weight:bold;margin-top:10px;}}.positive{{color:green;}}.negative{{color:red;}}h1,h2{{color:#444;}}.note{{color:#888;font-style:italic;}}</style></head><body><h1>{UNDERLYING_TICKER} PPO Option Strategy {SCRIPT_VERSION_TAG} (Fallback)</h1><p class="note">Fallback report.</p><h2>Metrics</h2><div class="metrics"><div class="metric-box"><div>Total Return</div><div class="metric-value {'positive'if total_return>0 else 'negative'}">{total_return:.2f}%</div></div><div class="metric-box"><div>Annualized Return</div><div class="metric-value {'positive'if annualized_return>0 else 'negative'}">{annualized_return:.2f}%</div></div><div class="metric-box"><div>Sharpe Ratio</div><div class="metric-value {'positive'if sharpe>1 else 'negative'}">{sharpe:.2f}</div></div><div class="metric-box"><div>Max Drawdown</div><div class="metric-value negative">{max_dd:.2f}%</div></div><div class="metric-box"><div>Trading Days</div><div class="metric-value">{len(daily_returns)}</div></div></div>{chart_html}<h2>Info</h2><p>{csv_msg}</p><p class="note">Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p></body></html>"""
        with open(QUANTSTATS_REPORT_FILENAME,'w') as f: f.write(html_content)
        logging.info(f"Fallback report saved: {QUANTSTATS_REPORT_FILENAME}")
    except Exception as e2:
        logging.error(f"Failed fallback report: {e2}",exc_info=True)
        try:
            with open(QUANTSTATS_REPORT_FILENAME,'w') as f: f.write(f"<html><body><h1>Report Failed</h1><p>Logs.</p></body></html>")
            logging.info(f"Basic error report saved.")
        except: logging.error("Could not create basic error report.")

def _create_equity_curve_chart(net_worth_history, date_index):
    try:
        import matplotlib.pyplot as plt; import io; import base64
        plt.figure(figsize=(10,6)); plt.plot(net_worth_history,linewidth=2); plt.title('Equity Curve'); plt.xlabel('Trading Days'); plt.ylabel('Net Worth ($)'); plt.grid(True,alpha=0.3)
        if date_index is not None and len(date_index)==len(net_worth_history):
            try: step=max(1,len(date_index)//10); plt.xticks(range(0,len(date_index),step),[d.strftime('%Y-%m-%d') for d in date_index[::step]],rotation=45)
            except Exception as e: logging.debug(f"No date index for chart: {e}")
        buf=io.BytesIO(); plt.tight_layout(); plt.savefig(buf,format='png'); buf.seek(0)
        img_base64=base64.b64encode(buf.getvalue()).decode('ascii'); buf.close(); plt.close(); return img_base64
    except Exception as e: logging.error(f"Error equity chart: {e}"); return ""

def get_option_moneyness(option_type, strike_pct):
    """Determine if an option is ITM, ATM, or OTM based on strike percentage from current price."""
    # For moneyness determination, we need to check if strike is above or below current price
    if abs(strike_pct) <= 0.02:  # Within 2% of current price
        return "at-the-money (ATM)"

    if option_type.upper() == "CALL":
        if strike_pct < 0:  # Strike below current price
            if strike_pct < -0.20:  # 20% or more below
                return "deep in-the-money (ITM)"
            return "in-the-money (ITM)"
        else:  # Strike above current price
            if strike_pct > 0.20:  # 20% or more above
                return "deep out-of-the-money (OTM)"
            return "out-of-the-money (OTM)"
    else:  # PUT
        if strike_pct > 0:  # Strike above current price
            if strike_pct > 0.20:  # 20% or more above
                return "deep in-the-money (ITM)"
            return "in-the-money (ITM)"
        else:  # Strike below current price
            if strike_pct < -0.20:  # 20% or more below
                return "deep out-of-the-money (OTM)"
            return "out-of-the-money (OTM)"

# --- Minimal Prediction Env ---
class MinimalPredictionEnv(gym.Env):
    def __init__(self,observation_space,action_space): super().__init__(); self.observation_space=observation_space; self.action_space=action_space
    def reset(self,seed=None,_options=None): super().reset(seed=seed); return self.observation_space.sample(),{}
    def step(self,_action): return self.observation_space.sample(),0,False,False,{}
    def render(self): pass
    def close(self): pass

# --- Underlying Env Components ---
class UnderlyingStopper(Stopper):
    def __init__(self,max_steps:int=MAX_STEPS_PER_EPISODE,max_loss_pct:float=MAX_ALLOWED_LOSS): super().__init__(); self.max_steps=max_steps; self.max_loss_pct=max_loss_pct; logging.info(f"Stopper: max_steps={max_steps}, max_loss={max_loss_pct}")
    def stop(self,env:'TradingEnv')->bool:
        current_step=env.clock.step
        if current_step>=self.max_steps: logging.info(f"Stopper: Max steps ({current_step}/{self.max_steps})."); return True
        if hasattr(env,'reward_scheme') and isinstance(env.reward_scheme,OptionRewardScheme):
            if env.reward_scheme._internal_net_worth_history:
                current_NW=env.reward_scheme._internal_net_worth_history[-1]; loss_thresh=env.reward_scheme.initial_cash*(1-self.max_loss_pct)
                logging.debug(f"Stopper: Step {current_step}, NW:{current_NW:.2f}, LossThresh:{loss_thresh:.2f}")
                if current_NW<loss_thresh: logging.info(f"Stopper: Max loss (NW:{current_NW:.2f} < Thresh:{loss_thresh:.2f})."); return True
        if hasattr(env,'observer') and hasattr(env.observer,'feed') and not env.observer.feed.has_next(): logging.info(f"Stopper: End of data feed @{current_step}."); return True
        return False

class UnderlyingInformer(Informer):
    def __init__(self): super().__init__(); logging.info("Informer initialized")
    def reset(self): logging.debug("Informer.reset.")
    def info(self,env:'TradingEnv')->dict:
        try:
            step=env.clock.step; spy_price=None
            if hasattr(env,'features_df') and env.features_df is not None and f'close_{UNDERLYING_TICKER}' in env.features_df.columns and step<len(env.features_df): spy_price=float(env.features_df[f'close_{UNDERLYING_TICKER}'].iloc[step])

            # Default values for P&L components
            cash = 0.0
            opt_val = 0.0
            NW = 0.0
            held_opt = None
            cum_pnl_val = 0.0
            cum_costs_val = 0.0

            if hasattr(env, 'action_scheme') and isinstance(env.action_scheme, OptionActionScheme):
                action_scheme = env.action_scheme
                cash = action_scheme.simulated_cash
                opt_val = action_scheme.current_option_market_value
                NW = action_scheme.get_current_portfolio_value()
                held_opt = action_scheme.held_option
                logging.debug(f"Informer: Using OptionActionScheme for P&L. Cash={cash:.2f}, OptVal={opt_val:.2f}, NW={NW:.2f}")
            elif hasattr(env,'reward_scheme') and isinstance(env.reward_scheme,OptionRewardScheme):
                # Fallback to reward scheme if action_scheme is not the expected type (should be less common)
                reward_scheme = env.reward_scheme
                cash = reward_scheme._internal_cash
                opt_val = reward_scheme._internal_option_value
                NW = cash+opt_val
                # Cannot reliably get held_option from reward_scheme, action_scheme is the source
                if hasattr(env, 'action_scheme'): held_opt = getattr(env.action_scheme, 'held_option', None)
                logging.warning("Informer: Falling back to OptionRewardScheme for P&L data as OptionActionScheme was not suitable.")
            else:
                logging.warning("Informer: Neither compatible OptionActionScheme nor OptionRewardScheme found for P&L data.")

            # Get cumulative P&L and costs if reward_scheme is available
            if hasattr(env,'reward_scheme') and isinstance(env.reward_scheme,OptionRewardScheme):
                cum_pnl_val = env.reward_scheme.cumulative_pnl
                cum_costs_val = env.reward_scheme.cumulative_costs

            info={
                'step':step,
                'spy_price':spy_price,
                'cash':float(cash),
                'option_value':float(opt_val),
                'conceptual_net_worth':float(NW),
                'held_option':held_opt,
                'step_reward':getattr(env,'reward',0.0),
                'cum_pnl':cum_pnl_val,
                'cum_costs':cum_costs_val
            }
            return info
        except Exception as e: logging.error(f"Error informer.info: {e}"); return {'error':str(e)}

class UnderlyingRenderer(Renderer):
    def __init__(self,step_interval:int=100): super().__init__(); self.step_interval=step_interval; logging.info("Renderer initialized")
    def reset(self): logging.debug("Renderer.reset.")
    def render(self,env:'TradingEnv',_mode:str='human'):
        current_step=env.clock.step
        if current_step%self.step_interval!=0: return
        if hasattr(env,'informer') and hasattr(env.informer,'info'):
            info=env.informer.info(env); spy=info.get('spy_price','N/A'); cash=info.get('cash','N/A'); NW=info.get('conceptual_net_worth','N/A'); opt_val=info.get('option_value',0.0); held=info.get('held_option')
            pnl_pct=(NW/INITIAL_CASH-1)*100 if isinstance(NW,(int,float)) else 'N/A'
            held_s=f"Holding: {held['type']} K={held['strike']:.2f} (Val:${opt_val:.2f})" if held else "Holding: None"
            logging.info(f"Step {current_step}: SPY={spy:.2f}, Cash=${cash:.2f}, NW=${NW:.2f}, P&L={pnl_pct:.2f}%, {held_s}")
        else: logging.info(f"Step {current_step}: Rendering info N/A.")
        return None

# --- Gymnasium Wrapper ---
class GymnasiumWrapper(gym.Env):
    def __init__(self, env):
        self.env = env
        self.action_space = self.env.action_scheme.action_space
        self.observation_space = self.env.observer.observation_space

        # Store direct references to critical components
        # Use getattr with default None for all attributes to handle missing attributes gracefully
        self._portfolio = getattr(env, 'portfolio', None)
        self._action_scheme = getattr(env, 'action_scheme', None)
        self._observer = getattr(env, 'observer', None)
        self._clock = getattr(env, 'clock', None)
        self._exchange = getattr(env, 'exchange', None)
        self._reward_scheme = getattr(env, 'reward_scheme', None)
        self._feed = getattr(env, 'feed', None)
        self._features_df = getattr(env, 'features_df', None)

        # Create a proxy that provides direct attribute access (for backward compatibility)
        # Check if we have the necessary components to create a TradingEnvProxy
        try:
            self._trading_env_proxy = TradingEnvProxy(
                env_wrapper=env,
                portfolio=self._portfolio,
                action_scheme=self._action_scheme,
                observer=self._observer,
                clock=self._clock,
                exchange=self._exchange,
                reward_scheme=self._reward_scheme,
                feed=self._feed
            )
            logging.debug(f"Successfully created TradingEnvProxy")
        except Exception as e:
            logging.warning(f"Could not create TradingEnvProxy: {e}. Using env directly.")
            self._trading_env_proxy = env

        # For backward compatibility
        self._trading_env = env

        # For debugging purpose
        logging.debug(f"GymnasiumWrapper initialized with env type: {type(env).__name__}")

    # Forward these attributes directly from stored references
    @property
    def portfolio(self):
        return self._portfolio

    @property
    def action_scheme(self):
        return self._action_scheme

    @property
    def observer(self):
        return self._observer

    @property
    def clock(self):
        return self._clock

    @property
    def feed(self):
        return self._feed

    @property
    def reward_scheme(self):
        return self._reward_scheme

    @property
    def exchange(self):
        return self._exchange

    @property
    def features_df(self):
        return self._features_df

    def reset(self, seed=None, options=None):
        if seed is not None:
            super().reset(seed=seed)
            # Also set seed in the inner environment if possible
            if hasattr(self.env, 'seed'):
                self.env.seed(seed)

        # Handle different Gymnasium versions
        try:
            # For newer Gymnasium versions that return (obs, info)
            result = self.env.reset(seed=seed, options=options)
            if isinstance(result, tuple) and len(result) == 2:
                obs, info = result
            else:
                # Single observation returned
                obs = result
                info = {}
        except TypeError:
            # For older versions that don't accept seed/options
            try:
                result = self.env.reset()
                if isinstance(result, tuple) and len(result) == 2:
                    obs, info = result
                else:
                    # Single observation returned
                    obs = result
                    info = {}
            except Exception as e:
                logging.error(f"Error in reset: {e}")
                # Fallback
                obs = self.observation_space.sample() * 0
                info = {}

        # CRITICAL FIX: Ensure global state is properly initialized after reset
        # Only initialize if not already done by action scheme reset to avoid race conditions
        try:
            current_history_length = len(global_state.conceptual_net_worth_history) if hasattr(global_state, 'conceptual_net_worth_history') and isinstance(global_state.conceptual_net_worth_history, list) else 0

            # Only initialize if the action scheme hasn't already done it
            if current_history_length == 0:
                if self._action_scheme and hasattr(self._action_scheme, 'get_current_portfolio_value'):
                    initial_value = self._action_scheme.get_current_portfolio_value()
                    global_state.conceptual_net_worth_history = [initial_value]
                    logging.debug(f"GymnasiumWrapper: Initialized global state after reset with value: {initial_value:.2f}")
                elif hasattr(global_state, 'action_scheme') and global_state.action_scheme:
                    if hasattr(global_state.action_scheme, 'get_current_portfolio_value'):
                        initial_value = global_state.action_scheme.get_current_portfolio_value()
                        global_state.conceptual_net_worth_history = [initial_value]
                        logging.debug(f"GymnasiumWrapper: Initialized global state via global_state.action_scheme with value: {initial_value:.2f}")
            else:
                logging.debug(f"GymnasiumWrapper: Global state already initialized with {current_history_length} entries, skipping initialization")
        except Exception as global_init_error:
            logging.debug(f"GymnasiumWrapper: Failed to initialize global state after reset: {global_init_error}")

        return obs, info

    def step(self, action):
        # CRITICAL FIX: Handle both old and new Gymnasium API formats
        # Also ensure global state is updated after each step
        try:
            step_result = self.env.step(action)

            if len(step_result) == 5:
                # New Gymnasium format: obs, reward, terminated, truncated, info
                obs, reward, terminated, truncated, info = step_result
            elif len(step_result) == 4:
                # Old Gymnasium format: obs, reward, done, info
                obs, reward, done, info = step_result
                terminated = done
                truncated = info.get('truncated', False)
            else:
                raise ValueError(f"Unexpected step result format: {len(step_result)} values returned")

            # CRITICAL FIX: Ensure global state is updated after each step
            # This is a backup mechanism in case the action scheme's perform method wasn't called properly
            try:
                if self._action_scheme and hasattr(self._action_scheme, 'update_global_net_worth_history'):
                    self._action_scheme.update_global_net_worth_history()
                    logging.debug("GymnasiumWrapper: Successfully updated global state after step")
                elif hasattr(global_state, 'action_scheme') and global_state.action_scheme:
                    if hasattr(global_state.action_scheme, 'update_global_net_worth_history'):
                        global_state.action_scheme.update_global_net_worth_history()
                        logging.debug("GymnasiumWrapper: Successfully updated global state via global_state.action_scheme")
            except Exception as global_update_error:
                logging.debug(f"GymnasiumWrapper: Failed to update global state: {global_update_error}")

            # Always return new format for consistency with Stable Baselines 3
            if hasattr(gym, '__version__') and gym.__version__ >= '0.26.0':
                return obs, reward, terminated, truncated, info
            else:
                return obs, reward, terminated, info

        except Exception as step_error:
            logging.error(f"GymnasiumWrapper: Critical error in step: {step_error}", exc_info=True)
            # Return safe fallback values
            obs = self.observation_space.sample() * 0
            reward = 0.0
            terminated = True
            truncated = False
            info = {}

            if hasattr(gym, '__version__') and gym.__version__ >= '0.26.0':
                return obs, reward, terminated, truncated, info
            else:
                return obs, reward, terminated, info

    def close(self):
        return self.env.close()

    def render(self, mode='human'):
        if hasattr(self.env, 'render'):
            return self.env.render(mode)
        return None

# --- Global Helper Functions ---
EPSILON = 1e-8

def unwrap_env(env):
    """
    Enhanced environment unwrapping with better support for VecEnv and complex wrapper chains.
    Ensures consistent environment unwrapping while preventing infinite recursion.

    Args:
        env: The environment to unwrap

    Returns:
        The base environment with direct access to trading components
    """
    # If None, return early
    if env is None:
        logging.warning("UNWRAP_ENV: Received None environment")
        return None

    # Debug helper
    env_type = type(env).__name__
    logging.debug(f"UNWRAP_ENV_START: Unwrapping env of type: {env_type}, ID={id(env)}")

    try:
        # CRITICAL FIX: Handle VecNormalize wrapper specifically
        if env_type == 'VecNormalize':
            logging.debug(f"UNWRAP_ENV: Detected VecNormalize wrapper")
            if hasattr(env, 'venv') and hasattr(env.venv, 'envs') and len(env.venv.envs) > 0:
                # Get the first environment from the VecEnv
                first_env = env.venv.envs[0]
                logging.debug(f"UNWRAP_ENV: Accessing first env from VecNormalize.venv.envs: {type(first_env).__name__}")
                return unwrap_env(first_env)  # Recursively unwrap

        # CRITICAL FIX: Handle DummyVecEnv wrapper specifically
        if env_type == 'DummyVecEnv':
            logging.debug(f"UNWRAP_ENV: Detected DummyVecEnv wrapper")
            if hasattr(env, 'envs') and len(env.envs) > 0:
                first_env = env.envs[0]
                logging.debug(f"UNWRAP_ENV: Accessing first env from DummyVecEnv.envs: {type(first_env).__name__}")
                return unwrap_env(first_env)  # Recursively unwrap

        # CRITICAL FIX: Handle Monitor wrapper specifically
        if env_type == 'Monitor':
            logging.debug(f"UNWRAP_ENV: Detected Monitor wrapper")
            if hasattr(env, 'env'):
                logging.debug(f"UNWRAP_ENV: Accessing env from Monitor: {type(env.env).__name__}")
                return unwrap_env(env.env)  # Recursively unwrap

        # CRITICAL FIX: Handle GymnasiumWrapper specifically
        if env_type == 'GymnasiumWrapper':
            logging.debug(f"UNWRAP_ENV: Detected GymnasiumWrapper")
            # Check if it has direct component access
            if hasattr(env, '_action_scheme') and env._action_scheme is not None:
                logging.debug(f"UNWRAP_ENV: GymnasiumWrapper has direct component access")
                return env  # Return the wrapper itself as it provides direct access
            elif hasattr(env, 'env'):
                logging.debug(f"UNWRAP_ENV: Accessing env from GymnasiumWrapper: {type(env.env).__name__}")
                return unwrap_env(env.env)  # Recursively unwrap

        # Define protocol for environments
        if hasattr(env, 'get_base_env'):
            logging.debug(f"Using get_base_env protocol for {env_type}")
            return env.get_base_env()

        # Check if this environment already has the required trading components
        if (hasattr(env, 'portfolio') and hasattr(env, 'action_scheme') and
            getattr(env, 'portfolio') is not None and getattr(env, 'action_scheme') is not None):
            logging.debug(f"UNWRAP_ENV: Found trading components directly in {env_type}")
            return env

        # Standard unwrapping for known wrappers
        unwrap_attrs = ['env', 'venv', 'envs', '_env', '_trading_env']
        current = env

        for depth in range(10):  # Prevent infinite loops
            if hasattr(current, 'unwrapped'):
                unwrapped = current.unwrapped
                if unwrapped is not current:  # Prevent self-references
                    logging.debug(f"Found unwrapped attribute in {type(current).__name__}")
                    return unwrapped

            wrapped = False
            for attr in unwrap_attrs:
                if hasattr(current, attr):
                    next_env = getattr(current, attr)
                    if next_env is not None and next_env is not current:  # Prevent self-references and None
                        # Special handling for envs list (VecEnv)
                        if attr == 'envs' and isinstance(next_env, list) and len(next_env) > 0:
                            next_env = next_env[0]  # Take first environment

                        logging.debug(f"Unwrapping via {attr} attribute: {type(next_env).__name__}")
                        current = next_env
                        wrapped = True
                        break

            if not wrapped:
                logging.debug(f"No more wrappers found at depth {depth}, returning current env: {type(current).__name__}")
                return current

        logging.warning("Maximum unwrap depth exceeded")
        return current  # Return what we have instead of raising error

    except Exception as e:
        logging.error(f"Error during environment unwrapping: {str(e)}", exc_info=True)
        # Return a TradingEnvProxy as fallback
        return TradingEnvProxy(env_wrapper=env)

class QuantStatsComparisonCallback(BaseCallback):
    def __init__(self, eval_env, train_env, train_features_df, eval_features_df, log_path, comparison_metrics_log_path, eval_freq=10000, n_eval_episodes=5, deterministic=True, verbose=0):
        super(QuantStatsComparisonCallback, self).__init__(verbose)
        self.eval_env = eval_env
        self.train_env = train_env # Store the training environment
        self.train_features_df = train_features_df
        self.eval_features_df = eval_features_df
        self.log_path = log_path
        self.comparison_metrics_log_path = comparison_metrics_log_path
        self.eval_freq = eval_freq
        self.n_eval_episodes = n_eval_episodes
        self.deterministic = deterministic
        self.best_mean_reward = -np.inf
        self.comparison_metrics = [] # To store metrics over time

        # Ensure log_path exists
        os.makedirs(self.log_path, exist_ok=True)

        # Initialize comparison metrics CSV
        if not os.path.exists(self.comparison_metrics_log_path) or os.path.getsize(self.comparison_metrics_log_path) == 0:
            pd.DataFrame(columns=[
                'eval_step', 'train_sharpe', 'train_avg_profit', 'train_max_drawdown', 'train_win_rate',
                'eval_sharpe', 'eval_avg_profit', 'eval_max_drawdown', 'eval_win_rate'
            ]).to_csv(self.comparison_metrics_log_path, index=False)

    def _on_step(self) -> bool:
        if self.eval_freq > 0 and self.n_calls % self.eval_freq == 0:
            logging.info(f"QuantStatsComparisonCallback: Starting evaluation at step {self.num_timesteps}")

            # Evaluate on training environment
            train_report_path = os.path.join(self.log_path, f"quantstats_train_report_step_{self.num_timesteps}.html")
            logging.info(f"QuantStatsComparisonCallback: Evaluating training environment")
            train_metrics = self._evaluate_and_generate_report(self.train_env, self.train_features_df, train_report_path, "Training Set")
            logging.info(f"QuantStatsComparisonCallback: Training metrics: {train_metrics}")

            # Evaluate on evaluation environment
            eval_report_path = os.path.join(self.log_path, f"quantstats_eval_report_step_{self.num_timesteps}.html")
            logging.info(f"QuantStatsComparisonCallback: Evaluating evaluation environment")
            eval_metrics = self._evaluate_and_generate_report(self.eval_env, self.eval_features_df, eval_report_path, "Evaluation Set")
            logging.info(f"QuantStatsComparisonCallback: Evaluation metrics: {eval_metrics}")

            # Log metrics with validation
            current_metrics = {
                'eval_step': self.num_timesteps,
                'train_sharpe': train_metrics.get('sharpe'),
                'train_avg_profit': train_metrics.get('avg_profit_per_trade'),
                'train_max_drawdown': train_metrics.get('max_drawdown'),
                'train_win_rate': train_metrics.get('win_rate'),
                'eval_sharpe': eval_metrics.get('sharpe'),
                'eval_avg_profit': eval_metrics.get('avg_profit_per_trade'),
                'eval_max_drawdown': eval_metrics.get('max_drawdown'),
                'eval_win_rate': eval_metrics.get('win_rate')
            }

            # Validate that we got meaningful metrics
            if all(v is None for v in current_metrics.values() if v != self.num_timesteps):
                logging.warning(f"QuantStatsComparisonCallback: All metrics are None at step {self.num_timesteps} - evaluation may have failed")

            # Compare with OptunaPruningEvalCallback results if available
            if hasattr(self, 'optuna_callback') and self.optuna_callback is not None:
                if hasattr(self.optuna_callback, 'last_mean_reward') and self.optuna_callback.last_mean_reward is not None:
                    optuna_mean_reward = self.optuna_callback.last_mean_reward
                    eval_sharpe = current_metrics.get('eval_sharpe')
                    if eval_sharpe is not None:
                        logging.info(f"Callback Comparison at step {self.num_timesteps}: "
                                   f"OptunaPruning mean_reward={optuna_mean_reward:.4f}, "
                                   f"QuantStats eval_sharpe={eval_sharpe:.4f}")
                        # Check for significant discrepancies
                        if abs(optuna_mean_reward) > 0.1 and eval_sharpe is not None and abs(eval_sharpe) > 0.1:
                            ratio = abs(optuna_mean_reward / eval_sharpe) if eval_sharpe != 0 else float('inf')
                            if ratio > 10 or ratio < 0.1:
                                logging.warning(f"Large discrepancy between callback metrics: ratio={ratio:.2f}")
                    else:
                        logging.warning(f"QuantStats eval_sharpe is None while OptunaPruning has mean_reward={optuna_mean_reward:.4f}")
                else:
                    logging.warning(f"OptunaPruningEvalCallback has no last_mean_reward at step {self.num_timesteps}")

            self.comparison_metrics.append(current_metrics)

            # Append to CSV
            try:
                pd.DataFrame([current_metrics]).to_csv(self.comparison_metrics_log_path, mode='a', header=False, index=False)
                logging.info(f"QuantStatsComparisonCallback: Successfully saved metrics to CSV")
            except Exception as e:
                logging.error(f"QuantStatsComparisonCallback: Failed to save metrics to CSV: {e}")

            if self.verbose > 0:
                logging.info(f"QuantStats Comparison at step {self.num_timesteps}:")
                logging.info(f"  Train: Sharpe={train_metrics.get('sharpe', 'N/A')}, Max Drawdown={train_metrics.get('max_drawdown', 'N/A')}")
                logging.info(f"  Eval:  Sharpe={eval_metrics.get('sharpe', 'N/A')}, Max Drawdown={eval_metrics.get('max_drawdown', 'N/A')}")

        return True

    def _evaluate_and_generate_report(self, env, features_df, report_path, dataset_name):
        # Reset the environment and global state for a clean evaluation run
        env.reset()

        # First try to access components directly if it's a GymnasiumWrapper
        if hasattr(env, '_portfolio') and hasattr(env, '_action_scheme') and hasattr(env, '_reward_scheme'):
            actual_env = env
            trading_env = env
            logging.info(f"Using direct component access for {dataset_name} in QuantStats callback.")
        # If it's a VecEnv, try to access the first environment
        elif hasattr(env, 'envs') and len(env.envs) > 0:
            # Try direct access first
            if hasattr(env.envs[0], '_portfolio') and hasattr(env.envs[0], '_action_scheme'):
                actual_env = env.envs[0]
                trading_env = env.envs[0]
                logging.info(f"Using direct component access from VecEnv for {dataset_name} in QuantStats callback.")
            # Fall back to unwrapping
            else:
                actual_env = unwrap_env(env.envs[0])
                trading_env = getattr(actual_env, '_trading_env', actual_env)
        # Otherwise try to unwrap the environment
        else:
            actual_env = unwrap_env(env)
            trading_env = getattr(actual_env, '_trading_env', actual_env)

        # Check if we have access to the portfolio and action_scheme
        portfolio = getattr(trading_env, 'portfolio', None) or getattr(actual_env, '_portfolio', None) or getattr(actual_env, 'portfolio', None)
        action_scheme = getattr(trading_env, 'action_scheme', None) or getattr(actual_env, '_action_scheme', None) or getattr(actual_env, 'action_scheme', None)
        reward_scheme = getattr(trading_env, 'reward_scheme', None) or getattr(actual_env, '_reward_scheme', None) or getattr(actual_env, 'reward_scheme', None)

        if portfolio is None or action_scheme is None:
            logging.error(f"Could not access portfolio or action_scheme for {dataset_name} in QuantStats callback.")
            logging.error(f"Portfolio: {portfolio}, ActionScheme: {action_scheme}")
            logging.error(f"TradingEnv: {trading_env}, ActualEnv: {actual_env}")
            logging.error(f"Env type: {type(env)}, Env attributes: {[attr for attr in dir(env) if not attr.startswith('_')]}")
            return {}

        # Reset specific components
        portfolio.reset()
        action_scheme.reset() # Ensure action scheme is reset
        if reward_scheme is not None:
            reward_scheme.reset() # Ensure reward scheme is reset

        # CRITICAL FIX: Reset global state but preserve conceptual_net_worth_history structure
        # Store the initial value before reset to maintain consistency
        initial_cash = getattr(portfolio, 'initial_cash', INITIAL_CASH)
        global_state.net_worth_history = []  # Reset only the regular net worth history

        # RACE CONDITION FIX: Only reset if not already properly initialized
        current_history_length = len(global_state.conceptual_net_worth_history) if hasattr(global_state, 'conceptual_net_worth_history') and isinstance(global_state.conceptual_net_worth_history, list) else 0
        if current_history_length == 0:
            global_state.conceptual_net_worth_history = [initial_cash]  # Reset with proper initial value
            logging.info(f"QUANTSTATS_CALLBACK: Reset global_state for {dataset_name} evaluation. "
                        f"Initial conceptual_net_worth_history: [{initial_cash}]")
        else:
            logging.info(f"QUANTSTATS_CALLBACK: Global state already initialized for {dataset_name} evaluation with {current_history_length} entries")

        # Simulate episodes
        # Handle different Gymnasium API versions
        try:
            reset_result = env.reset()
            if isinstance(reset_result, tuple) and len(reset_result) == 2:
                obs, info = reset_result
            else:
                obs = reset_result
                info = {}
        except Exception as e:
            logging.error(f"Error during env.reset(): {e}")
            # Fallback to using the observation space sample
            obs = env.observation_space.sample() * 0
            info = {}

        done = False
        episode_rewards = []

        # CRITICAL FIX: Use the official global_state.conceptual_net_worth_history instead of creating our own
        # The ActionScheme will update this history via update_global_net_worth_history() after each action
        logging.info(f"QUANTSTATS_CALLBACK: Starting {dataset_name} evaluation. "
                    f"Initial global_state.conceptual_net_worth_history length: {len(global_state.conceptual_net_worth_history)}")

        # Verify that the ActionScheme is properly connected to update the global state
        if hasattr(action_scheme, 'update_global_net_worth_history'):
            logging.info(f"QUANTSTATS_CALLBACK: ActionScheme has update_global_net_worth_history method - synchronization should work")
        else:
            logging.error(f"QUANTSTATS_CALLBACK: ActionScheme missing update_global_net_worth_history method - synchronization will fail!")

        # CRITICAL FIX: Replace fixed-step loop with proper episode-based evaluation
        # Run the intended number of complete episodes instead of a fixed number of steps
        num_episodes_completed = 0
        max_steps_per_episode = MAX_STEPS_PER_EPISODE  # Safety limit to prevent infinite episodes

        logging.info(f"QUANTSTATS_CALLBACK: Starting episode-based evaluation for {self.n_eval_episodes} episodes")

        while num_episodes_completed < self.n_eval_episodes:
            episode_step_count = 0
            episode_done = False

            logging.debug(f"QUANTSTATS_CALLBACK: Starting episode {num_episodes_completed + 1}/{self.n_eval_episodes}")

            # Run one complete episode
            while not episode_done and episode_step_count < max_steps_per_episode:
                action, _ = self.model.predict(obs, deterministic=self.deterministic)

                # Handle different Gymnasium API versions for step
                try:
                    step_result = env.step(action)
                    # Check if step returns 5 values (obs, reward, terminated, truncated, info) - newer Gym
                    if isinstance(step_result, tuple) and len(step_result) == 5:
                        obs, reward, done, truncated, info = step_result
                    # Check if step returns 4 values (obs, reward, done, info) - older Gym
                    elif isinstance(step_result, tuple) and len(step_result) == 4:
                        obs, reward, done, info = step_result
                        truncated = False
                    else:
                        # Unexpected return format
                        logging.warning(f"Unexpected env.step return format: {type(step_result)}")
                        obs, reward, done, truncated, info = step_result, 0, True, False, {}
                except Exception as e:
                    logging.error(f"Error during env.step: {e}")
                    # Fallback values
                    obs = env.observation_space.sample() * 0
                    reward = 0
                    done = True
                    truncated = False
                    info = {}

                episode_rewards.append(reward[0] if isinstance(reward, np.ndarray) else reward)
                episode_step_count += 1

                # CRITICAL FIX: No manual net worth tracking - the ActionScheme automatically updates
                # global_state.conceptual_net_worth_history via update_global_net_worth_history()
                # during its perform() method after each env.step()

                # Log the current state of the global net worth history for verification
                if len(global_state.conceptual_net_worth_history) > 0:
                    current_global_net_worth = global_state.conceptual_net_worth_history[-1]
                    logging.debug(f"QUANTSTATS_CALLBACK: Episode {num_episodes_completed + 1}, Step {episode_step_count} completed. "
                                 f"Global net worth history length: {len(global_state.conceptual_net_worth_history)}, "
                                 f"Latest value: {current_global_net_worth:.2f}")
                else:
                    logging.warning(f"QUANTSTATS_CALLBACK: Global net worth history is empty after step - synchronization issue!")

                # Check for episode termination
                if (isinstance(done, np.ndarray) and done[0]) or done or truncated:
                    episode_done = True
                    logging.debug(f"QUANTSTATS_CALLBACK: Episode {num_episodes_completed + 1} terminated after {episode_step_count} steps")
                    break

            # Episode completed (either by termination or max steps reached)
            num_episodes_completed += 1

            if episode_step_count >= max_steps_per_episode:
                logging.warning(f"QUANTSTATS_CALLBACK: Episode {num_episodes_completed} reached max steps ({max_steps_per_episode}) without termination")

            # Reset environment for next episode (if more episodes to run)
            if num_episodes_completed < self.n_eval_episodes:
                try:
                    reset_result = env.reset()
                    if isinstance(reset_result, tuple) and len(reset_result) == 2:
                        obs, info = reset_result
                    else:
                        obs = reset_result
                        info = {}
                    logging.debug(f"QUANTSTATS_CALLBACK: Environment reset for episode {num_episodes_completed + 1}")
                except Exception as e:
                    logging.error(f"Error during env.reset() after episode {num_episodes_completed}: {e}")
                    # Fallback
                    obs = env.observation_space.sample() * 0
                    info = {}

        logging.info(f"QUANTSTATS_CALLBACK: Completed {num_episodes_completed} episodes for {dataset_name} evaluation")

        # CRITICAL FIX: Use the official global_state.conceptual_net_worth_history for QuantStats
        logging.info(f"QUANTSTATS_CALLBACK: Evaluation complete for {dataset_name}. "
                    f"Final global_state.conceptual_net_worth_history length: {len(global_state.conceptual_net_worth_history)}")

        if len(global_state.conceptual_net_worth_history) > 1:
            # The simplest approach possible to create valid returns data for QuantStats
            try:
                # Create synthetic dates - one per day, moving backwards from today
                end_date = pd.Timestamp.now().normalize()
                date_range = pd.date_range(end=end_date, periods=len(global_state.conceptual_net_worth_history), freq='D')

                # Create a simple pandas Series with the net worth values and date index
                net_worth_series = pd.Series(data=global_state.conceptual_net_worth_history, index=date_range)

                # Calculate percent change (returns) and drop NaN values (first entry)
                returns_series = net_worth_series.pct_change().dropna()

                # Skip QuantStats reporting for optimization runs
                # Check if we're in optimization mode by looking for optuna_callback reference
                in_optimization = hasattr(self, 'optuna_callback') and self.optuna_callback is not None

                if in_optimization:
                    # Skip report generation during optimization
                    logging.info(f"Skipping QuantStats report for {dataset_name} during optimization")

                    # Return basic metrics without generating reports
                    try:
                        # Try to get metrics from action_scheme directly
                        if action_scheme is not None and hasattr(action_scheme, 'total_profit'):
                            total_profit = action_scheme.total_profit
                            trade_count = action_scheme.trade_count if action_scheme.trade_count > 0 else 1
                            win_count = action_scheme.win_count
                        elif hasattr(actual_env, '_trading_env') and actual_env._trading_env is not None and hasattr(actual_env._trading_env, 'action_scheme'):
                            total_profit = actual_env._trading_env.action_scheme.total_profit
                            trade_count = actual_env._trading_env.action_scheme.trade_count if actual_env._trading_env.action_scheme.trade_count > 0 else 1
                            win_count = actual_env._trading_env.action_scheme.win_count
                        else:
                            # Default values
                            total_profit = sum(episode_rewards)
                            trade_count = max(1, len(episode_rewards))
                            win_count = sum(1 for r in episode_rewards if r > 0)

                        # Calculate metrics
                        avg_profit = total_profit / trade_count
                        win_rate = win_count / trade_count

                        # Approximate Sharpe ratio from episode rewards
                        if len(episode_rewards) > 1:
                            mean_reward = np.mean(episode_rewards)
                            std_reward = np.std(episode_rewards) if np.std(episode_rewards) > 0 else 1.0
                            sharpe = mean_reward / std_reward * np.sqrt(252)  # Annualize
                        else:
                            sharpe = 0.0

                        metrics = {
                            'sharpe': sharpe,
                            'max_drawdown': min(0.0, np.min(episode_rewards)) if episode_rewards else 0.0,
                            'avg_profit_per_trade': avg_profit,
                            'win_rate': win_rate,
                        }
                        return metrics
                    except Exception as e:
                        logging.error(f"Error calculating optimization metrics: {e}")
                        return {}

                # For non-optimization runs, continue with normal QuantStats reporting
                logging.info(f"Created returns series with DatetimeIndex of length {len(returns_series)}")
                try:
                    # Generate QuantStats report only for non-optimization runs
                    qs.reports.html(returns_series, output=report_path, title=f"{dataset_name} Performance - Step {self.num_timesteps}")

                    # Calculate metrics with better error handling
                    try:
                        sharpe_ratio = qs.stats.sharpe(returns_series)
                        max_dd = qs.stats.max_drawdown(returns_series)
                    except Exception as e:
                        logging.warning(f"Error calculating QuantStats metrics: {e}")
                        sharpe_ratio = 0.0
                        max_dd = 0.0

                    # Get trading metrics with fallbacks
                    try:
                        if action_scheme is not None and hasattr(action_scheme, 'total_profit'):
                            total_profit = action_scheme.total_profit
                            trade_count = action_scheme.trade_count if action_scheme.trade_count > 0 else 1
                            win_count = action_scheme.win_count
                        elif hasattr(actual_env, '_trading_env') and actual_env._trading_env is not None and hasattr(actual_env._trading_env, 'action_scheme'):
                            total_profit = actual_env._trading_env.action_scheme.total_profit
                            trade_count = actual_env._trading_env.action_scheme.trade_count if actual_env._trading_env.action_scheme.trade_count > 0 else 1
                            win_count = actual_env._trading_env.action_scheme.win_count
                        else:
                            # Fallback to episode rewards
                            total_profit = sum(episode_rewards)
                            trade_count = max(1, len(episode_rewards))
                            win_count = sum(1 for r in episode_rewards if r > 0)

                        avg_profit = total_profit / trade_count
                        win_rate = win_count / trade_count
                    except Exception as e:
                        logging.warning(f"Error calculating trading metrics: {e}")
                        avg_profit = 0.0
                        win_rate = 0.0

                    metrics = {
                        'sharpe': sharpe_ratio,
                        'max_drawdown': max_dd,
                        'avg_profit_per_trade': avg_profit,
                        'win_rate': win_rate,
                    }
                    logging.info(f"QuantStats metrics for {dataset_name}: {metrics}")
                    return metrics
                except Exception as e:
                    logging.error(f"Error generating QuantStats report: {e}")
                    return {}

            except Exception as e:
                logging.error(f"Error in returns series processing: {e}")
                return {}
        else:
            logging.warning(f"Net worth history too short for QuantStats report for {dataset_name} at step {self.num_timesteps}.")
            return {}

# --- TradingEnv Proxy for Direct Attribute Access ---
class TradingEnvProxy:
    """
    A proxy class that provides direct access to TradingEnv attributes.
    This solves the problem of attribute access being lost during environment wrapping.
    """
    def __init__(self, env_wrapper=None, portfolio=None, action_scheme=None, observer=None, clock=None, exchange=None, reward_scheme=None, feed=None):
        """
        Initialize with either:
        1. Direct attributes (portfolio, action_scheme, etc.)
        2. An environment wrapper from which to extract attributes
        """
        # Initialize attributes to None
        self._portfolio = portfolio
        self._action_scheme = action_scheme
        self._observer = observer
        self._clock = clock
        self._exchange = exchange
        self._reward_scheme = reward_scheme
        self._feed = feed
        self._features_df = None
        self._cached_attributes = {}
        self._env_wrapper = env_wrapper  # Store reference to the wrapped environment

        # CRITICAL FIX: Add the missing _trading_env attribute that was causing the error
        # This attribute is expected by various components that access TradingEnvProxy
        self._trading_env = env_wrapper  # Set to the wrapped environment

        # ENHANCED LOGGING for TradingEnvProxy initialization
        logging.info(f"TRADINGENVPROXY_INIT: TradingEnvProxy_ID={id(self)}, "
                    f"EnvWrapper_ID={id(env_wrapper) if env_wrapper else 'None'}, "
                    f"Portfolio_ID={id(portfolio) if portfolio else 'None'}, "
                    f"ActionScheme_ID={id(action_scheme) if action_scheme else 'None'}, "
                    f"RewardScheme_ID={id(reward_scheme) if reward_scheme else 'None'}")

        # If we were passed a wrapper environment, try to extract attributes from it
        if env_wrapper is not None:
            # If env_wrapper is a TradingEnv, it should have all needed attributes directly
            if hasattr(env_wrapper, '__class__') and env_wrapper.__class__.__name__ == 'TradingEnv':
                self._extract_all_attributes(env_wrapper)
                return

            # Special case for direct trading env reference
            if hasattr(env_wrapper, '_trading_env') and env_wrapper._trading_env is not None:
                if hasattr(env_wrapper._trading_env, '__class__') and env_wrapper._trading_env.__class__.__name__ == 'TradingEnv':
                    self._extract_all_attributes(env_wrapper._trading_env)
                    return

            # Extract attributes from the wrapper if they weren't provided directly
            self._extract_all_attributes(env_wrapper)

            # If we still don't have essential attributes but have env, try to extract from there
            if hasattr(env_wrapper, 'env') and env_wrapper.env is not None:
                self._extract_from_nested_env(env_wrapper.env)

            # Try other common wrapper paths
            if hasattr(env_wrapper, 'venv') and env_wrapper.venv is not None:
                self._extract_from_nested_env(env_wrapper.venv)

            # Try VecEnv unwrapping
            if hasattr(env_wrapper, 'envs') and isinstance(env_wrapper.envs, list) and len(env_wrapper.envs) > 0:
                for sub_env in env_wrapper.envs:
                    if sub_env is not None:
                        self._extract_from_nested_env(sub_env)
                        # If we found what we need, stop searching
                        if self._portfolio is not None and self._action_scheme is not None:
                            break

            # Try _trading_env - often used in wrappers
            if hasattr(env_wrapper, '_trading_env') and env_wrapper._trading_env is not None:
                self._extract_from_nested_env(env_wrapper._trading_env)

            # Try unwrapped - gymnasium convention
            if hasattr(env_wrapper, 'unwrapped') and env_wrapper.unwrapped is not None:
                self._extract_from_nested_env(env_wrapper.unwrapped)

            # Last attempt - check if this is a VecNormalize wrapper
            if hasattr(env_wrapper, '__class__') and env_wrapper.__class__.__name__ == 'VecNormalize':
                if hasattr(env_wrapper, 'venv') and hasattr(env_wrapper.venv, 'envs') and len(env_wrapper.venv.envs) > 0:
                    base_env = env_wrapper.venv.envs[0]
                    if hasattr(base_env, '_trading_env'):
                        self._extract_all_attributes(base_env._trading_env)
                    elif hasattr(base_env, 'env') and hasattr(base_env.env, '_trading_env'):
                        self._extract_all_attributes(base_env.env._trading_env)

    def _extract_all_attributes(self, env):
        """Extract all important attributes from an environment."""
        if env is None:
            return

        # Try to find core attributes
        if self._portfolio is None:
            self._portfolio = getattr(env, 'portfolio', None)
        if self._action_scheme is None:
            self._action_scheme = getattr(env, 'action_scheme', None)
        if self._observer is None:
            self._observer = getattr(env, 'observer', None)
        if self._clock is None:
            self._clock = getattr(env, 'clock', None)
        if self._exchange is None:
            self._exchange = getattr(env, 'exchange', None)
        if self._reward_scheme is None:
            self._reward_scheme = getattr(env, 'reward_scheme', None)
        if self._feed is None:
            self._feed = getattr(env, 'feed', None)
        if self._features_df is None:
            self._features_df = getattr(env, 'features_df', None)

    def _extract_from_nested_env(self, nested_env):
        """Helper to extract attributes from a nested environment."""
        if nested_env is None:
            return

        # Try direct extraction
        self._extract_all_attributes(nested_env)

        # If this is a wrapper, try its env too
        if hasattr(nested_env, 'env') and nested_env.env is not None and nested_env.env is not nested_env:
            self._extract_from_nested_env(nested_env.env)

        # Try unwrapped property - gymnasium convention
        if hasattr(nested_env, 'unwrapped') and nested_env.unwrapped is not None and nested_env.unwrapped is not nested_env:
            self._extract_from_nested_env(nested_env.unwrapped)

        # Try VecEnv's first environment
        if hasattr(nested_env, 'envs') and isinstance(nested_env.envs, list) and len(nested_env.envs) > 0:
            if nested_env.envs[0] is not None and nested_env.envs[0] is not nested_env:
                self._extract_from_nested_env(nested_env.envs[0])

        # Try internal _trading_env reference
        if hasattr(nested_env, '_trading_env') and nested_env._trading_env is not None and nested_env._trading_env is not nested_env:
            self._extract_from_nested_env(nested_env._trading_env)

    def __getattr__(self, name):
        """Dynamic attribute access for attributes not explicitly defined."""
        # CRITICAL FIX: Completely avoid recursion by blocking all problematic attributes immediately
        # Block access to problematic attributes that can cause circular references
        blocked_attributes = {
            'class_attributes', '_get_all_attributes', 'getattr_depth_check',
            '__dict__', '__class__', '__getattribute__', '__setattr__',
            '__delattr__', '__reduce__', '__reduce_ex__', '__getstate__',
            '__setstate__', '__getnewargs__', '__getnewargs_ex__',
            '_recursion_guard'  # Block access to recursion guard itself
        }

        if name in blocked_attributes:
            raise AttributeError(f"TradingEnvProxy intentionally blocks access to '{name}' to prevent infinite recursion")

        # Try to find the attribute in our internal references first
        if name == 'portfolio':
            if hasattr(self, '_portfolio') and self._portfolio is not None:
                return self._portfolio
            raise AttributeError(f"TradingEnvProxy has no attribute 'portfolio'")
        elif name == 'action_scheme':
            if hasattr(self, '_action_scheme') and self._action_scheme is not None:
                return self._action_scheme
            raise AttributeError(f"TradingEnvProxy has no attribute 'action_scheme'")
        elif name == 'reward_scheme':
            if hasattr(self, '_reward_scheme') and self._reward_scheme is not None:
                return self._reward_scheme
            raise AttributeError(f"TradingEnvProxy has no attribute 'reward_scheme'")
        elif name == 'observer':
            if hasattr(self, '_observer'):
                return self._observer
            raise AttributeError(f"TradingEnvProxy has no attribute 'observer'")
        elif name == 'clock':
            if hasattr(self, '_clock'):
                return self._clock
            raise AttributeError(f"TradingEnvProxy has no attribute 'clock'")
        elif name == 'exchange':
            if hasattr(self, '_exchange'):
                return self._exchange
            raise AttributeError(f"TradingEnvProxy has no attribute 'exchange'")
        elif name == 'feed':
            if hasattr(self, '_feed'):
                return self._feed
            raise AttributeError(f"TradingEnvProxy has no attribute 'feed'")
        elif name == 'features_df':
            if hasattr(self, '_features_df'):
                return self._features_df
            raise AttributeError(f"TradingEnvProxy has no attribute 'features_df'")
        elif name == '_trading_env':
            if hasattr(self, '_trading_env') and self._trading_env is not None:
                return self._trading_env
            raise AttributeError(f"TradingEnvProxy has no attribute '_trading_env'")

        # CRITICAL FIX: Only allow very specific, safe attributes from wrapped env
        # Never access methods or properties that could trigger __getattr__ recursion
        safe_wrapper_attributes = {
            'action_space', 'observation_space', 'spec', 'metadata', 'render_mode'
        }

        if hasattr(self, '_env_wrapper') and self._env_wrapper is not None and name in safe_wrapper_attributes:
            try:
                # Use object.__getattribute__ to bypass any custom __getattr__ in the wrapper
                return object.__getattribute__(self._env_wrapper, name)
            except AttributeError:
                pass

        # If attribute still not found, raise AttributeError immediately
        raise AttributeError(f"TradingEnvProxy has no attribute '{name}'")

# Add main execution with enhanced interrupt handling
if __name__ == "__main__":
    import signal
    import sys

    def signal_handler(_signum, _frame):
        """Handle KeyboardInterrupt gracefully."""
        print("\n\nKeyboardInterrupt received. Shutting down gracefully...")
        logging.warning("Script interrupted by user (Ctrl+C)")
        # Disable further logging to prevent hanging
        logging.getLogger().setLevel(logging.CRITICAL)
        sys.exit(0)

    # Register signal handler for clean shutdown
    signal.signal(signal.SIGINT, signal_handler)

    try:
        main()
    except KeyboardInterrupt:
        print("\n\nKeyboardInterrupt received. Shutting down gracefully...")
        logging.warning("Script interrupted by user")
        sys.exit(0)
    finally:
        # Log performance summary at the end
        try:
            logging.info(ErrorMessageFormatter.info("Main", "Script execution completed"))
            performance_monitor.log_summary()
        except:
            # If logging fails during shutdown, just exit
            pass